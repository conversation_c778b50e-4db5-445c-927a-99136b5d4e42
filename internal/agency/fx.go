package agency

import (
	"gitlab.sskjz.com/go/ev"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/im"
	"gitlab.sskjz.com/overseas/live/osl/internal/link"
	"gitlab.sskjz.com/overseas/live/osl/internal/live"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
)

func Provide(
	im *im.Manager,
	lm *live.Manager,
	lnm *link.Manager,
	ug user.Getter,
	db *db.Client,
	ev ev.Bus,
	dm *redi.Mutex,
	vnd log.Vendor,
) (*Manager, error) {
	return newManager(im, lm, lnm, ug, db, ev, dm, vnd.Scope("agency.mgr")), nil
}
