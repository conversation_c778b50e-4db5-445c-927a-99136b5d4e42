package agency

import "time"

const (
	AgencyStatusNormal = 0
	AgencyStatusDelete = -1
)

// Agency 公会
type Agency struct {
	ID            uint      `gorm:"primaryKey"`
	NumId         int64     `gorm:"not null;size:64;unique"`         // 内部数字ID
	Name          string    `gorm:"not null;size:255;unique"`        // 公会名称
	MemberNum     int       `gorm:"not null;size:32;default:1"`      // 成员数量
	ImageUrl      string    `gorm:"not null;size:255;default:''"`    // 公会头像
	ShowId        string    `gorm:"not null;size:12;unique"`         // 公会ID 6位数字
	Ratio         float64   `gorm:"not null;type:decimal(10,2)"`     // 公会分成比例，指占主播收入的比例
	ChiefId       string    `gorm:"not null;size:32;index:chief_id"` // 公会长ID
	Phone         string    `gorm:"not null;size:255"`               // 联系电话
	InviteCode    string    `gorm:"not null;size:255;default:''"`    // 创建公会填写的邀请码
	WhatsappId    string    `gorm:"not null;size:255"`               // whatsapp联系方式
	ContactPerson string    `gorm:"not null;size:255;default:''"`    // 对接人
	Status        int       `gorm:"not null;size:8;default:0"`       // 申请状态 -1:删除 0:正常
	Platform      string    `gorm:"not null;size:255"`               // 合作平台
	Material      string    `gorm:"type:LONGTEXT;not null"`          // 上传资料，最多三张
	CreatedAt     time.Time `gorm:"not null"`
	UpdatedAt     time.Time `gorm:"not null"`
}

func (u *Agency) TableName() string {
	return "agencies"
}

// AgencyMember 公会成员
type AgencyMember struct {
	ID        uint      `gorm:"primaryKey"`
	AgencyId  uint      `gorm:"not null;index:agency_id"`        // 公会数据库ID
	ChiefId   string    `gorm:"not null;size:32;index:chief_id"` // 公会长ID
	UserId    string    `gorm:"not null;size:32;unique"`         // 公会主播ID，只能加入一个公会，唯一索引
	CreatedAt time.Time `gorm:"not null"`
	UpdatedAt time.Time `gorm:"not null"`
}

func (m *AgencyMember) TableName() string {
	return "agency_members"
}

// 公会成员申请表
type AgencyMemberApply struct {
	ID        uint      `gorm:"primaryKey"`
	AgencyId  uint      `gorm:"not null;index:agency_id"`       // 公会ID
	UserId    string    `gorm:"not null;size:32;index:user_id"` // 申请用户ID
	Status    int       `gorm:"not null;size:8;default:0"`      // 申请状态 -1:拒绝 0:待审核 1:通过
	CreatedAt time.Time `gorm:"not null"`
	UpdatedAt time.Time `gorm:"not null"`
}

func (a *AgencyMemberApply) TableName() string {
	return "agency_member_applies"
}

// 公会申请表
type AgencyApply struct {
	ID              uint      `gorm:"primaryKey"`
	Name            string    `gorm:"not null;size:255;unique"`        // 公会名称
	ImageUrl        string    `gorm:"not null;size:255;default:''"`    // 公会头像
	Phone           string    `gorm:"not null;size:255"`               // 联系电话
	WhatsappId      string    `gorm:"not null;size:255"`               // whatsapp联系方式
	MemberNum       int       `gorm:"not null;size:32"`                // 成员数量
	Platform        string    `gorm:"not null;size:255"`               // 合作平台
	Material        string    `gorm:"type:LONGTEXT;not null"`          // 上传资料，最多三张
	InviteCode      string    `gorm:"not null;size:255;default:''"`    // 邀请码
	ChiefId         string    `gorm:"not null;size:32;index:chief_id"` // 申请用户ID
	Status          int       `gorm:"not null;size:8;default:0"`       // 申请状态 -1:拒绝 0:待审核 1:通过
	AuditUserId     string    `gorm:"not null;size:32"`                // 审核用户ID
	AuditedAt       time.Time `gorm:""`                                // 审核时间
	ContactWhatsapp string    `gorm:"not null;size:255"`               // 私信预留的Whatsapp账号
	CreatedAt       time.Time `gorm:"not null"`
	UpdatedAt       time.Time `gorm:"not null"`
}

func (a *AgencyApply) TableName() string {
	return "agency_applies"
}

// AgencyQuitApply 公会成员退出申请表
type AgencyQuitApply struct {
	ID        uint      `gorm:"primaryKey"`
	AgencyId  uint      `gorm:"not null;index:agency_id"`       // 公会ID
	UserId    string    `gorm:"not null;size:32;index:user_id"` // 申请用户ID
	Status    int       `gorm:"not null;size:8;default:0"`      // 申请状态 -2取消申请 -1:拒绝 0:待审核 1:通过
	CreatedAt time.Time `gorm:"not null"`
	UpdatedAt time.Time `gorm:"not null"`
}

func (a *AgencyQuitApply) TableName() string {
	return "agency_quit_applies"
}

const (
	ForceQuitStatusQuit    = -2 // 公会长操作导致用户退出公会，关闭退会流程
	ForceQuitStatusCancel  = -1 // 用户自己取消强制退出公会流程
	ForceQuitStatusOpen    = 0  // 开启状态
	ForceQuitStatusSuccess = 1  // 用户确认强制退出公会，完成
)

type AgencyForceQuitApply struct {
	ID        uint      `gorm:"primaryKey"`
	AgencyId  uint      `gorm:"not null;index:agency_id"`       // 公会ID
	UserId    string    `gorm:"not null;size:32;index:user_id"` // 申请用户ID
	Status    int       `gorm:"not null;size:8;default:0"`      // 申请状态
	LastAlive time.Time `gorm:"not null"`                       // 最后活动时间，包括直播，连线等
	CreatedAt time.Time `gorm:"not null"`
	UpdatedAt time.Time `gorm:"not null"`
}

func (a *AgencyForceQuitApply) TableName() string {
	return "agency_force_quit_applies"
}
