package asd

import (
	"time"

	"github.com/samber/lo"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// 公会数据，次日更新，成长数据给运营看
type AgencyGrowthDay struct {
	Id               primitive.ObjectID `bson:"_id"`
	AgencyId         int64              `bson:"agencyId"`         // 公会ID
	Day              string             `bson:"day"`              // 日期 yyyyMMdd，汇总键
	AgencyUserId     string             `bson:"agencyUserId"`     // 公会长ID
	LuckDiamond      int64              `bson:"luckDiamond"`      // 幸运礼物流水，金币
	GiftDiamond      int64              `bson:"giftDiamond"`      // 特效礼物流水，金币
	FClubDiamond     int64              `bson:"fClubDiamond"`     // 粉丝团流水，金币
	LuckIncome       fund.Decimal       `bson:"luckIncome"`       // 幸运礼物收入，水晶
	GiftIncome       fund.Decimal       `bson:"giftIncome"`       // 特效礼物收入，水晶
	FClubIncome      fund.Decimal       `bson:"fClubIncome"`      // 粉丝团收入，水晶
	AnchorIncome     fund.Decimal       `bson:"anchorIncome"`     // 主播总收入，水晶
	AgencyIncome     fund.Decimal       `bson:"agencyIncome"`     // 公会总收入，水晶
	LiveAnchorCount  int64              `bson:"liveAnchorCount"`  // 开播主播数
	ValidAnchorCount int64              `bson:"validAnchorCount"` // 有效主播数，人次：一段时间内，总达到有效主播数的所有次数
	NewAnchorCount   int64              `bson:"newAnchorCount"`   // 新增主播数
	EvaluationCount  int64              `bson:"evaluationCount"`  // 通过考核主播数
	CTZ              string             `bson:"ctz"`              // 时区, 例如：ID:Asia/Jakarta
	StartTime        time.Time          `bson:"startTime"`        // 数据统计开始时间
	EndTime          time.Time          `bson:"endTime"`          // 数据统计结束时间
}

func AgencyGrowthDayCollectionName() string {
	return "agency.growth.day"
}

func createAgencyGrowthDayIndexer(dbmc *db.MongoClient) {
	dbmc.SyncSchema(AgencyGrowthDayCollectionName(), 1,
		db.Indexer{Name: "agencyId_day", Keys: bson.D{
			{Key: "agencyId", Value: 1},
			{Key: "day", Value: -1},
		}, Uniq: lo.ToPtr(true)},
	)
}

// 公会周奖励详细表
type AgencyGrowthReward struct{}

func AgencyGrowthRewardCollectionName() string {
	return "agency.growth.reward"
}

// 公会周奖励汇总表
type AgencyGrowthRewardGroup struct{}

func AgencyGrowthRewardGroupCollectionName() string {
	return "agency.growth.reward.group"
}
