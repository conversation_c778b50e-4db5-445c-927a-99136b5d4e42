package rqa

// 强制退会处理api

import (
	"gitlab.sskjz.com/overseas/live/osl/internal/agency"
	"gitlab.sskjz.com/overseas/live/osl/pkg/app"
	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
)

type RevokeQuitAgency struct {
	RevokeQuitAgency bool `json:"revokeQuitAgency"` // 撤销强制退会
}

func ForceAgencyQuit(ctx *api.Context, am *agency.Manager, userId string, quit bool) error {
	if quit {
		if err := am.AgencyForceQuitCancel(ctx, userId); err != nil {
			return err
		}
	}

	forceQuit := am.InForceQuit(ctx, userId)
	if !forceQuit {
		return nil
	}

	if !app.CVGTE(ctx, app.V1120, 80) {
		return biz.Legacy("please upgrade to the latest version")
	}

	return agency.ErrInForceQuit
}
