package agency

import (
	"context"
	"encoding/json"
	"fmt"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
	"go.uber.org/zap"
	"time"

	"gitlab.sskjz.com/overseas/live/osl/pkg/db"

	"gitlab.sskjz.com/overseas/live/osl/internal/evt"

	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gorm.io/gorm"
)

const (
	ratio = 0.1 // 工会分成金额为主播分润的10%，从总流水里扣除
)

var (
	ErrNotJoinAgency    = biz.NewError(biz.ErrNotJoinAgency, "not join agency")
	ErrNotFoundAgency   = biz.NewError(biz.ErrNotFoundAgency, "not found agency")
	ErrRepeatAgencyName = biz.NewError(biz.ErrRepeatAgencyName, "repeat agency name")
	ErrJoinedAgency     = biz.NewError(biz.ErrJoinedAgency, "joined agency")
	ErrNoAuthority      = biz.NewError(biz.ErrNoAuthority, "no authority")
)

// GetAgencyByMember 通过用户id获取用户加入的公会
func (m *Manager) GetAgencyByMember(userId string) (*Agency, error) {
	var am AgencyMember

	if err := m.db.Where("user_id = ?", userId).Take(&am).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, ErrNotJoinAgency
		}

		return nil, err
	}

	var a Agency

	if err := m.db.Where("id = ? and status = ?", am.AgencyId, AgencyStatusNormal).Take(&a).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, ErrNotFoundAgency
		}

		return nil, err
	}

	return &a, nil
}

// GetMyAgency 获取用户当前公会的状态。无公会、正在申请加入、已经加入公会、正在申请退出
func (m *Manager) GetMyAgency(ctx context.Context, userId string) (*MyAgency, error) {
	var myAgency MyAgency
	// 获取用户公会信息
	agencyInfo, err := m.GetAgencyByMember(userId)
	// 情况一：未加入公会
	if err == ErrNotJoinAgency {
		myAgency.Status = MyAgencyStatusNoJoin

		// 是否有申请加入公会
		var apply AgencyMemberApply
		if err := m.db.
			Where("user_id = ? and status = ?", userId, VerifyStatusDefault).Order("id desc").
			Take(&apply).Error; err != nil && err != gorm.ErrRecordNotFound {
			return nil, err
		}

		if apply.ID > 0 {
			// 查询公会信息
			var a Agency
			if err := m.db.Where("id = ? and status = ?", apply.AgencyId, AgencyStatusNormal).Take(&a).Error; err != nil {
				if err == gorm.ErrRecordNotFound {
					return nil, ErrNotFoundAgency
				}

				return nil, err
			}

			// 改为申请入会
			myAgency.Agency = &a
			myAgency.Status = MyAgencyStatusApplyJoin
		}

		return &myAgency, nil
	}

	if err != nil {
		return nil, err
	}

	// 情况二：已加入公会
	myAgency.Agency = agencyInfo
	myAgency.Status = MyAgencyStatusAlreadyJoin

	// 是否正在申请退出公会
	var applyQuit AgencyQuitApply
	if err := m.db.
		Where("user_id = ? and status = ?", userId, VerifyStatusDefault).Order("id desc").
		Take(&applyQuit).Error; err != nil && err != gorm.ErrRecordNotFound {
		return nil, err
	}

	if applyQuit.ID > 0 && applyQuit.AgencyId == agencyInfo.ID {
		myAgency.Status = MyAgencyStatusApplyOut
	}

	return &myAgency, nil
}

// GetAgencyInfoByShowId 通过公户showid查询公会信息，只能查询到状态为正常的公会信息
func (m *Manager) GetAgencyInfoByShowId(ctx context.Context, showId string) (*Agency, error) {
	var a Agency

	if err := m.db.Where("show_id = ? and status = ?", showId, AgencyStatusNormal).Take(&a).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, ErrNotFoundAgency
		}

		return nil, err
	}

	return &a, nil
}

func (m *Manager) GetAgencyInfoByNumId(ctx context.Context, numId int64) (*Agency, error) {
	var a Agency

	if err := m.db.Where("num_id = ? and status = ?", numId, AgencyStatusNormal).Take(&a).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, ErrNotFoundAgency
		}

		return nil, err
	}

	return &a, nil
}

func (m *Manager) AgencyCreate(ctx context.Context, userId string, req AgencyCreateApply) error {
	// 已经加入公会
	ma, err := m.GetMyAgency(ctx, userId)
	if err != nil {
		return err
	}

	if ma != nil && ma.Status != MyAgencyStatusNoJoin {
		return ErrJoinedAgency
	}

	// 已经申请公会
	aca, err := m.GetMyAgencyApply(ctx, userId)
	if err != nil {
		return err
	}

	if aca.ID > 0 && aca.Status == int(VerifyStatusDefault) {
		return ErrJoinedAgency
	}

	// 名称是否重复
	var repeatCount int64
	if err := m.db.Model(&AgencyApply{}).Where("name = ?", req.Name).Count(&repeatCount).Error; err != nil {
		return err
	} else if repeatCount > 0 {
		return ErrRepeatAgencyName
	}

	if err := m.db.Model(&Agency{}).Where("name = ?", req.Name).Count(&repeatCount).Error; err != nil {
		return err
	} else if repeatCount > 0 {
		return ErrRepeatAgencyName
	}

	var material string
	if len(req.Material) > 0 {
		b, _ := json.Marshal(req.Material)
		material = string(b)
	}

	var contactWhatsapp string
	if req.InviteCode == "" {
		// 否则发送私信，每3天换一个WhatsApp账号
		start := time.Date(2025, 04, 14, 0, 0, 0, 0, ctz.Brazil)
		now := time.Now().In(ctz.Brazil)
		elapsedDays := int(now.Sub(start).Hours() / 24)
		index := (elapsedDays / 3) % 3
		whatsApps := []string{
			"+86 18458862822",
			"+852 67489029",
			"+86 13372497715",
		}
		if index < len(whatsApps) {
			contactWhatsapp = whatsApps[index]
		}
	}

	agencyApply := AgencyApply{
		Name:            req.Name,
		ImageUrl:        req.ImageUrl,
		WhatsappId:      req.WhatsappId,
		Phone:           req.Phone,
		InviteCode:      req.InviteCode,
		MemberNum:       req.MemberNum,
		Platform:        req.Platform,
		Material:        material,
		ChiefId:         userId,
		Status:          int(VerifyStatusDefault),
		AuditUserId:     "",
		ContactWhatsapp: contactWhatsapp,
		AuditedAt:       time.Now(),
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
	}

	err = m.db.Create(&agencyApply).Error
	if err != nil {
		return err
	}

	m.ev.Emit(ctx, evt.AgencyApply, &evt.AgencyApplyEvt{
		UserId:     userId,
		InviteCode: req.InviteCode,
	})

	if req.InviteCode != "" {
		// 如果填写了邀请码直接通过
		if err := m.AgencyApplyVerify(ctx, userId, int(agencyApply.ID), VerifyStatusApproved); err != nil {
			return err
		}
	} else {
		if contactWhatsapp != "" {
			if err := m.im.SendSystemNoticeTextToUser(
				ctx,
				userId,
				fmt.Sprintf(
					"Sua solicitação já foi enviada. Aguarde a revisão oficial. Se precisar de ajuda, entre em contato via WhatsApp: %s.",
					contactWhatsapp,
				),
			); err != nil {
				m.log.Error("创建公会私信发送失败",
					zap.Error(err),
					zap.String("userId", userId),
				)
			}
		}
	}

	return nil
}

func (m *Manager) GetMyAgencyApply(ctx context.Context, userId string) (*AgencyApply, error) {
	var uaa AgencyApply
	err := m.db.Where("chief_id = ?", userId).Order("id desc").Take(&uaa).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return nil, err
	}

	return &uaa, nil
}

func (m *Manager) getAgencyApplyById(_ context.Context, id int) (*AgencyApply, error) {
	var aa AgencyApply
	err := m.db.Where("id = ?", id).Take(&aa).Error
	if err != nil {
		return nil, err
	}

	return &aa, nil
}

// MyOwnAgency 获取我名下的公会信息（会长是我），只能获取到公会状态为正常的信息
func (m *Manager) MyOwnAgency(ctx context.Context, userId string) (*Agency, error) {
	var ag Agency
	if err := m.db.Where("chief_id = ? and status = ?", userId, AgencyStatusNormal).Take(&ag).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, ErrNotFoundAgency
		}

		return nil, err
	}

	return &ag, nil
}

func (m *Manager) GetAgencyById(ctx context.Context, id int64) (*Agency, error) {
	var ag Agency
	if err := m.db.Where("id = ?", id).Take(&ag).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, ErrNotFoundAgency
		}

		return nil, err
	}

	return &ag, nil
}

func (m *Manager) GetInviteAgencyList(ctx context.Context, inviteCode string) ([]Agency, error) {
	var inviteList []Agency
	if err := db.UseTx(ctx, m.db).Where("invite_code = ? and status = ?", inviteCode, AgencyStatusNormal).Find(&inviteList).Error; err != nil {
		return inviteList, err
	}

	return inviteList, nil
}

func (m *Manager) GetAgencyAnchorNum(ctx context.Context, agencyIds ...int) (int, error) {
	var anchorNum int64
	if err := db.UseTx(ctx, m.db).Model(&AgencyMember{}).Where("agency_id in ?", agencyIds).Count(&anchorNum).Error; err != nil {
		return int(anchorNum), err
	}

	return int(anchorNum), nil
}
