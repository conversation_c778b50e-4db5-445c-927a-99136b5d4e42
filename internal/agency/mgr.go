package agency

import (
	"gitlab.sskjz.com/go/ev"
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/im"
	"gitlab.sskjz.com/overseas/live/osl/internal/link"
	"gitlab.sskjz.com/overseas/live/osl/internal/live"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"go.uber.org/zap"
)

type Manager struct {
	im  *im.Manager
	lm  *live.Manager
	lnm *link.Manager
	ug  user.Getter
	db  *db.Client
	ev  ev.Bus
	dm  *redi.Mutex
	log *zap.Logger
}

func newManager(im *im.Manager, lm *live.Manager, lnm *link.Manager, ug user.Getter, db *db.Client, ev ev.Bus, dm *redi.Mutex, log *zap.Logger) *Manager {
	db.ApplyMigrate(&Agency{}, &AgencyMember{}, &AgencyMemberApply{}, &AgencyApply{}, &AgencyQuitApply{}, &AgencyForceQuitApply{})

	m := &Manager{
		im:  im,
		lm:  lm,
		lnm: lnm,
		ug:  ug,
		db:  db,
		ev:  ev,
		dm:  dm,
		log: log,
	}

	return m
}
