package agr

import (
	"context"

	"gitlab.sskjz.com/go/cron"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/go/mq"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/env"
)

func Provide(
	dbmc *db.MongoClient,
	vnd log.Vendor,
) (*Manager, error) {
	return newManager(dbmc, vnd.Scope("agr.mgr")), nil
}

func InvokeInScheduler(sch *cron.Scheduler, evq mq.Queue, mgr *Manager) {
	if !env.Scheduler() {
		return
	}

	// 注册周公会成长薪资发放任务
	{

	}

	// 周公会成长数据
	{
		task := sch.Exclusive("agr.mgr.growth", func(context.Context) error {
			return nil
		})

		// 巴西时间凌晨0点30分
		sch.Cron("30 3 * * *").Do(task)
	}
}
