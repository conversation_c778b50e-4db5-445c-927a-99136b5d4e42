package agency

import (
	"context"
	"fmt"
	"gitlab.sskjz.com/overseas/live/osl/internal/evt"
	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"strconv"
	"time"
)

func (m *Manager) AgencyList(ctx context.Context, chiefId, agencyShowId, phone string, page, pageSize int) ([]Agency, int64, error) {
	var ret []Agency

	if page < 1 {
		page = 1
	}

	var total int64

	query := db.UseTx(ctx, m.db).Model(&Agency{}).Where("status = ?", AgencyStatusNormal)

	if agencyShowId != "" {
		query = query.Where("show_id = ?", agencyShowId)
	}

	if chiefId != "" {
		query = query.Where("chief_id = ?", chiefId)
	}

	if phone != "" {
		query = query.Where("phone like ? or whatsapp_id like ?", "%"+phone+"%", "%"+phone+"%")
	}

	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	if err := query.Offset((page - 1) * pageSize).Limit(pageSize).Order("id desc").Find(&ret).Error; err != nil {
		return nil, 0, err
	}

	return ret, total, nil
}

func (m *Manager) AgencyModify(ctx context.Context, agencyId uint, image, name, contactPerson string) error {
	var updates = map[string]any{}

	if image != "" {
		updates["image_url"] = image
	}

	if name != "" {
		updates["name"] = name
	}

	if contactPerson != "" {
		updates["contact_person"] = contactPerson
	}

	if len(updates) == 0 {
		return nil
	}

	return db.UseTx(ctx, m.db).
		Model(&Agency{}).
		Where("id = ?", agencyId).
		Updates(updates).Error
}

func (m *Manager) Dissolution(ctx context.Context, agencyId uint) error {
	agencyInfo, err := m.GetAgencyById(ctx, int64(agencyId))
	if err != nil {
		return err
	}

	if err := db.Transaction(ctx, m.db, func(ctx context.Context, tx *gorm.DB) error {
		// 拒绝所有入会申请
		if err := tx.Model(&AgencyMemberApply{}).
			Where("agency_id = ? and status = ?", agencyId, VerifyStatusDefault).
			Update("status", VerifyStatusRefused).Error; err != nil {
			return err
		}

		// 拒绝所有退出公会申请
		if err := tx.Model(&AgencyQuitApply{}).
			Where("agency_id = ? and status = ?", agencyId, VerifyStatusDefault).
			Update("status", VerifyStatusRefused).Error; err != nil {
			return err
		}

		// 移除所有公会成员
		if err := tx.Where("agency_id = ?", agencyId).Delete(&AgencyMember{}).Error; err != nil {
			return err
		}

		// 公会软删除
		if err := tx.Model(&Agency{}).
			Where("id = ? and status = ?", agencyId, AgencyStatusNormal).
			Updates(map[string]interface{}{
				"status": AgencyStatusDelete,
				"name":   fmt.Sprintf("%s_Dissolution%d", agencyInfo.Name, time.Now().Unix()),
			}).Error; err != nil {
			return err
		}

		// 修改申请时的名字
		if err := tx.Model(&AgencyApply{}).
			Where("name = ?", agencyInfo.Name).
			Updates(map[string]interface{}{
				"name": fmt.Sprintf("%s_Dissolution%d", agencyInfo.Name, time.Now().Unix()),
			}).Error; err != nil {
			return err
		}
		return nil
	}); err != nil {
		m.log.Error("agency dissolution transaction err", zap.Error(err), zap.Uint("agencyId", agencyId))
		return err
	}

	// 公会删除事件
	m.ev.Emit(ctx, evt.AgencyDelete, &evt.AgencyDeleteEvt{
		AgencyId: int(agencyId),
	})

	var (
		msg      = "Devido à inatividade prolongada de sua agência, o sistema revogou automaticamente os direitos da agência. Em caso de dúvidas, entre em contato com a equipe de operações oficiais: %s."
		whatsapp = "+852 6748 9029"
	)
	switch agencyInfo.ContactPerson {
	case "VA":
		whatsapp = "+86 184 5886 2822"
	case "LUCAS":
		whatsapp = "+852 6748 9029"
	case "昊欣":
		whatsapp = "+86 133 7249 7715"
	}

	// 发送消息
	if err := m.im.SendSystemNoticeTextToUser(
		ctx,
		agencyInfo.ChiefId,
		fmt.Sprintf(msg, whatsapp),
	); err != nil {
		m.log.Error("解散公会私信发送失败",
			zap.Error(err),
			zap.String("userId", agencyInfo.ChiefId),
		)
	}

	m.log.Info("agency dissolution success", zap.Uint("agencyId", agencyId))

	return nil
}

func (m *Manager) MemberQuit(ctx context.Context, userId string) error {
	agency, err := m.GetAgencyByMember(userId)
	if err != nil {
		return err
	}

	if agency.ChiefId == userId {
		return ErrNoAuthority
	}

	if err := db.Transaction(ctx, m.db, func(ctx context.Context, tx *gorm.DB) error {
		// 取消退会申请
		tx.Model(&AgencyQuitApply{}).Where("user_id = ? and status = ?", userId, AgencyStatusNormal).Update("status", AgencyStatusDelete)

		// 用户退出公会
		if err := tx.Where("user_id = ?", userId).Delete(AgencyMember{}).Error; err != nil {
			return err
		}

		// 公会人数-1
		if err := tx.Model(&Agency{}).Where("id = ?", agency.ID).Update("member_num", gorm.Expr("member_num - 1")).Error; err != nil {
			return err
		}

		return nil
	}); err != nil {
		m.log.Error("agency quit transaction err", zap.Error(err), zap.String("userId", userId), zap.Uint("agencyId", agency.ID))
		return err
	}

	m.ev.Emit(ctx, evt.AgencyMemberQuit, &evt.AgencyMemberQuitEvt{
		AgencyId: int(agency.ID),
		UserId:   userId,
	})

	m.log.Info("agency quit success", zap.String("userId", userId), zap.Uint("agencyId", agency.ID))

	return nil
}

func (m *Manager) InviteEdit(ctx context.Context, agencyId int, inviteCode string) error {
	agencyInfo, err := m.GetAgencyById(ctx, int64(agencyId))
	if err != nil {
		return err
	}

	if agencyInfo.Status != AgencyStatusNormal {
		return ErrNotFoundAgency
	}

	if agencyInfo.InviteCode == inviteCode {
		return nil
	}

	if strconv.Itoa(int(agencyInfo.NumId)) == inviteCode {
		return biz.NewError(biz.ErrInviteCode, "invite code err")
	}

	originalInvite := agencyInfo.InviteCode
	if err := m.db.Model(&Agency{}).Where("id = ?", agencyId).Update("invite_code", inviteCode).Error; err != nil {
		return err
	}

	// 公会邀请码修改事件
	m.ev.Emit(ctx, evt.AgencyInviteEdit, &evt.AgencyInviteEditEvt{
		AgencyId:           agencyId,
		AgencyShowId:       agencyInfo.ShowId,
		OriginalInviteCode: originalInvite,
		CurrentInviteCode:  inviteCode,
	})

	m.log.Info("agency inviteCode edit success",
		zap.Int("agencyId", agencyId),
		zap.String("originalInvite", originalInvite),
		zap.String("inviteCode", inviteCode),
	)

	return nil
}
