package agency

import (
	"context"
	"errors"
	"fmt"
	"github.com/samber/lo"
	"gitlab.sskjz.com/go/i3n"
	"gitlab.sskjz.com/overseas/live/osl/internal/evt"
	"gitlab.sskjz.com/overseas/live/osl/internal/link"
	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/dbg"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"time"
)

var (
	ErrForceQuitApply = biz.NewError(biz.ErrNoForceQuitApply, "no force quit apply")
	ErrForceQuitTime  = biz.NewError(biz.ErrForceQuitTime, "time not enough")
	ErrInForceQuit    = biz.NewError(biz.ErrInForceQuit, "in force quit")
)

func (m *Manager) ForceQuitInfo(ctx context.Context, userId string) (*AgencyForceQuitApply, error) {
	var fqInfo AgencyForceQuitApply
	if err := m.db.Model(&AgencyForceQuitApply{}).
		Where("user_id = ? and status = ?", userId, ForceQuitStatusOpen).
		Last(&fqInfo).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrForceQuitApply
		}
		return nil, err
	}
	return &fqInfo, nil
}

// InForceQuit 用户是否在强制退会流程中
func (m *Manager) InForceQuit(ctx context.Context, userId string) bool {
	_, err := m.ForceQuitInfo(ctx, userId)
	if errors.Is(err, ErrForceQuitApply) {
		return false
	}
	return true
}

func (m *Manager) AgencyForceQuitApply(ctx context.Context, userId string, withIm bool) error {
	userAgencyInfo, err := m.GetAgencyByMember(userId)
	if errors.Is(err, ErrNotJoinAgency) {
		return err
	}

	// 公会长不允许退出公会
	if userAgencyInfo.ChiefId == userId {
		return ErrNoAuthority
	}

	var alreadyApply int64
	if err := m.db.Model(&AgencyForceQuitApply{}).
		Where("user_id = ? and status = ?", userId, ForceQuitStatusOpen).
		Count(&alreadyApply).Error; err != nil {
		return err
	}

	// 已申请
	if alreadyApply > 0 {
		return nil
	}

	var afqa AgencyForceQuitApply
	if err := m.db.Model(&AgencyForceQuitApply{}).
		Where("user_id = ? and agency_id = ?", userId, userAgencyInfo.ID).
		Last(&afqa).Error; err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}

	if afqa.ID > 0 {
		// 已申请
		if afqa.Status == int(VerifyStatusDefault) {
			return nil
		}

		// 7天内只能申请一次
		if !dbg.Ing() && afqa.CreatedAt.AddDate(0, 0, 7).After(time.Now()) {
			return biz.NewError(biz.ErrApplyTimeLimit, i3n.T2(ctx, "Hosts can submit only 1 agency exit request every %d days", 7))
		}

	}

	// 最后开播时间
	var lastAliveT time.Time = time.Now()
	r, err := m.lm.RoomByUserId(ctx, userId)
	if err != nil {
		return err
	}
	if !r.StartTime.IsZero() {
		lastAliveT = r.StartTime
	}

	linkLog, err := m.lnm.UserRecentLink(ctx, userId, 2, time.Now())
	if err != nil && !errors.Is(err, link.ErrNoRecentLink) {
		return err
	}
	if linkLog != nil && linkLog.StartedAt.Before(lastAliveT) {
		lastAliveT = linkLog.StartedAt
	}

	agencyForceQuitModel := AgencyForceQuitApply{
		AgencyId:  userAgencyInfo.ID,
		UserId:    userId,
		Status:    int(VerifyStatusDefault),
		LastAlive: lastAliveT,
	}

	if err := m.db.Create(&agencyForceQuitModel).Error; err != nil {
		return err
	}

	if withIm {
		uac, err := m.ug.Account(ctx, userId)
		if err != nil {
			return nil
		}
		// 发送消息
		if err := m.im.SendSystemNoticeTextToUser(
			ctx,
			userAgencyInfo.ChiefId,
			fmt.Sprintf("Olá Agente, seu host {%s: %s} solicitou a saída da agência através do processo de \"30 dias consecutivos sem transmissões\". Por favor, confirme com o host. Caso ele(a) permaneça 30 dias sem transmitir, o sistema permitirá sua saída direta da agência.",
				uac.ShowId,
				uac.Nickname,
			),
		); err != nil {
			m.log.Error("主播申请强制退出公会发送im失败",
				zap.Error(err),
				zap.String("userId", userAgencyInfo.ChiefId),
			)
		}
	}

	return nil
}

func (m *Manager) AgencyForceQuitCancel(ctx context.Context, userId string) error {
	_, err := m.ForceQuitInfo(ctx, userId)
	if err != nil && !errors.Is(err, ErrForceQuitApply) {
		return err
	}

	if err := m.db.Model(&AgencyForceQuitApply{}).Where("user_id = ? and status = ?", userId, ForceQuitStatusOpen).
		Updates(map[string]interface{}{
			"status":     ForceQuitStatusCancel,
			"updated_at": time.Now(),
		}).Error; err != nil {
		return err
	}

	return nil
}

// AgencyForceQuitConfirm 确认强制退会
func (m *Manager) AgencyForceQuitConfirm(ctx context.Context, userId string) error {
	fqInfo, err := m.ForceQuitInfo(ctx, userId)
	if err != nil {
		return err
	}

	if fqInfo.LastAlive.AddDate(0, 0, 30).After(time.Now()) {
		return ErrForceQuitTime
	}

	userAgencyInfo, err := m.GetMyAgency(ctx, userId)
	if err != nil {
		return err
	}

	if !lo.Contains([]int{MyAgencyStatusAlreadyJoin, MyAgencyStatusApplyOut}, userAgencyInfo.Status) || userAgencyInfo.Agency == nil {
		return ErrNotJoinAgency
	}

	if fqInfo.AgencyId != userAgencyInfo.Agency.ID {
		return ErrNotJoinAgency
	}

	if userAgencyInfo.Agency.ChiefId == userId {
		return ErrNoAuthority
	}

	if err := db.Transaction(ctx, m.db, func(ctx context.Context, tx *gorm.DB) error {
		// 移除公会成员
		if err := tx.Where("agency_id = ? and user_id = ?", userAgencyInfo.Agency.ID, userId).Delete(&AgencyMember{}).Error; err != nil {
			return err
		}
		// 公会人员数量-1
		if err := tx.Model(&Agency{}).Where("id = ?", userAgencyInfo.Agency.ID).
			Update("member_num", gorm.Expr("member_num - 1")).Error; err != nil {
			return err
		}

		// 关闭退会申请
		if err := tx.Model(&AgencyQuitApply{}).
			Where("agency_id = ? and user_id = ? and status = ?", userAgencyInfo.Agency.ID, userId, VerifyStatusDefault).Updates(map[string]interface{}{
			"status":     VerifyStatusCancel,
			"updated_at": time.Now(),
		}).Error; err != nil {
			return err
		}

		// 关闭强制退会流程
		if err := tx.Model(&AgencyForceQuitApply{}).
			Where("agency_id = ? and user_id = ? and status = ?", userAgencyInfo.Agency.ID, userId, ForceQuitStatusOpen).Updates(map[string]interface{}{
			"status":     ForceQuitStatusSuccess,
			"updated_at": time.Now(),
		}).Error; err != nil {
			return err
		}

		return nil
	}); err != nil {
		m.log.Error("agency member force quit transaction err",
			zap.Error(err),
			zap.Int64("agencyNumId", userAgencyInfo.Agency.NumId),
			zap.String("userId", userId),
		)
		return err
	}

	m.ev.Emit(ctx, evt.AgencyMemberQuit, &evt.AgencyMemberQuitEvt{
		AgencyId: int(userAgencyInfo.Agency.ID),
		UserId:   userId,
	})

	m.log.Info("agency member force quit success",
		zap.Int64("agencyNumId", userAgencyInfo.Agency.NumId),
		zap.String("agencyShowId", userAgencyInfo.Agency.ShowId),
		zap.String("userId", userId),
	)

	return nil
}
