package ganopay

import (
	"fmt"

	"github.com/bytedance/sonic"
	"github.com/go-resty/resty/v2"
	"gitlab.sskjz.com/overseas/live/osl/internal/pay"
	"gitlab.sskjz.com/overseas/live/osl/pkg/conf"
	"go.uber.org/zap"
)

const (
	codeSuccess = "0000"
)

func makeRequest[data any](cfg conf.Ganopay, biz string, v data) apiRequest[data] {
	return apiRequest[data]{
		Head: headReq{
			MerchantId: cfg.MerchantId,
			Version:    "20",
			Biz:        biz,
		},
		Body: v,
	}
}

func newApiError(code, msg string) *apiError {
	return &apiError{Code: code, Msg: msg}
}

type apiError struct {
	Code string
	Msg  string
}

func (e *apiError) Error() string {
	return fmt.Sprintf("api error: %s: %s", e.<PERSON>, e.Msg)
}

func doRequest[resp any](sig signer, log *zap.Logger, cli *resty.Client, path string, req any) (resp, error) {
	var void resp
	bs, err := sonic.Marshal(req)
	if err != nil {
		return void, err
	}

	ff := make([]zap.Field, 0, 6)
	ff = append(ff, zap.String("path", path), zap.ByteString("reqData", bs))

	if err := sig.Encode(&bs); err != nil {
		return void, err
	}

	hReq := cli.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Accept", "application/json").
		SetBody(bs)

	ff = append(ff, zap.Any("reqHeader", hReq.Header))

	var ret apiResponse[resp]

	hResp, err := hReq.Post(path)
	if hResp != nil {
		bs2 := hResp.Body()
		if err := sig.Decode(&bs2); err != nil {
			return void, err
		}
		if err := sonic.Unmarshal(bs2, &ret); err != nil {
			return void, err
		}
		ff = append(ff, zap.Int("respStatus", hResp.StatusCode()), zap.Any("respHeader", hResp.Header()), zap.ByteString("respBody", bs2))
	}

	if err != nil {
		log.With(zap.Error(err)).Warn("request api", ff...)
		return void, err
	}

	log.Debug("request api", ff...)

	if hResp.StatusCode() != 200 {
		return void, fmt.Errorf("request failed: %s", hResp.Status())
	}

	if ret.Head.Code != codeSuccess {
		return void, newApiError(ret.Head.Code, ret.Head.Msg)
	}

	return ret.Body, nil
}

type headReq struct {
	MerchantId string `json:"mchtId"`
	Version    string `json:"version"`
	Biz        string `json:"biz"`
}

type headResp struct {
	Code string `json:"respCode"`
	Msg  string `json:"respMsg"`
}

type apiRequest[data any] struct {
	Head headReq `json:"head"`
	Body data    `json:"body"`
	Sign string  `json:"sign,omitempty"`
}

type apiResponse[data any] struct {
	Head headResp `json:"head"`
	Body data     `json:"body"`
	Sign string   `json:"sign"`
}

type createOrderReq struct {
	OrderId     string       `json:"orderId"` // pay.TradeNo
	OrderTime   orderTime    `json:"orderTime"`
	Currency    pay.Currency `json:"currencyType"`
	Amount      string       `json:"amount"`
	Goods       string       `json:"goods"`
	CallbackUrl string       `json:"callBackUrl"`
	NotifyUrl   string       `json:"notifyUrl"`
}

type createOrderResp struct {
	OrderId string `json:"orderId"`
	PayUrl  string `json:"payUrl"`
	TradeId string `json:"tradeId"` // pay.OrderId
}

type queryOrderReq struct {
	OrderId   string       `json:"orderId"` // pay.TradeNo
	OrderTime orderTime    `json:"orderTime"`
	Currency  pay.Currency `json:"currencyType"`
}

type OrderStatus string

const (
	OrderSuccess OrderStatus = "SUCCESS"
	OrderFailure OrderStatus = "FAILURE"
	OrderUnknown OrderStatus = "UNKNOW"
)

type queryOrderResp struct {
	OrderId  string      `json:"orderId"`
	TradeId  string      `json:"tradeId"` // pay.OrderId
	Amount   string      `json:"amount"`
	Status   OrderStatus `json:"status"`
	ChargeAt orderTime   `json:"chargeTime"`
}

type balanceReq struct {
	MerchantId string       `json:"mchtId"`
	Currency   pay.Currency `json:"currencyType"`
}

type balanceResp struct {
	TotalBalance  string `json:"balance"`
	PayoutBalance string `json:"payoutBalance"`
}

type remitApplyReq struct {
	OrderNo     string       `json:"batchOrderNo"`
	Currency    pay.Currency `json:"currencyType"`
	TotalAmount string       `json:"totalAmount"`
	TotalNum    string       `json:"totalNum"`
	Detail      []remitForm  `json:"detail"`
	NotifyUrl   string       `json:"notifyUrl"`
}

type remitForm struct {
	Seq          string `json:"seq"`
	Amount       string `json:"amount"`
	AccType      string `json:"accType"`
	CertType     string `json:"certType"`
	CertId       string `json:"certId"`
	BankCardNo   string `json:"bankCardNo"`
	BankCardName string `json:"bankCardName"`
}

type remitApplyStatus string

const (
	remitApplySuccess remitApplyStatus = "SUCCESS"
	remitApplyFail    remitApplyStatus = "FAIL"
)

type remitApplyResp struct {
	Status  remitApplyStatus `json:"status"`
	TradeId string           `json:"tradeId"`
	OrderNo string           `json:"batchOrderNo"`
}

type remitBatchStatus string

const (
	remitBatchDone  remitBatchStatus = "DONE"
	remitBatchDoing remitBatchStatus = "DOING"
)

type remitQueryReq struct {
	OrderNo   string       `json:"batchOrderNo"`
	OrderTime orderTime    `json:"orderTime"`
	Currency  pay.Currency `json:"currencyType"`
}

type remitQueryResp struct {
	OrderNo string           `json:"batchOrderNo"`
	TradeId string           `json:"tradeId"`
	Status  remitBatchStatus `json:"status"`
	Detail  []remitOrder     `json:"detail"`
}

type remitStatus string

const (
	remitDoing   remitStatus = "DOING"
	remitSuccess remitStatus = "SUCCESS"
	remitFailure remitStatus = "FAIL"
)

type remitOrder struct {
	Seq      string      `json:"seq"`
	Amount   string      `json:"amount"`
	Status   remitStatus `json:"status"`
	FinishAt orderTime   `json:"finishTime"`
}
