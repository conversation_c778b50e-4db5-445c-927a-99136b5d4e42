package ganopay

import (
	"fmt"
	"strconv"
	"testing"
	"time"

	"github.com/davecgh/go-spew/spew"
	"github.com/go-resty/resty/v2"
	"github.com/stretchr/testify/assert"
	"gitlab.sskjz.com/overseas/live/osl/internal/pay"
	"gitlab.sskjz.com/overseas/live/osl/pkg/conf"
	"go.uber.org/zap"
)

func TestCreateOrder(t *testing.T) {
	desc, err := conf.Provide("../../configs/config.yaml")()
	if err != nil {
		t.Fatal(err)
	}
	sign := newSign1(desc.Ganopay.SecretKey)
	log, _ := zap.NewDevelopment()
	resp, err := doRequest[createOrderResp](sign, log, resty.New().SetBaseURL(desc.Ganopay.Endpoint), "/gateway/api/commPay", makeRequest[createOrderReq](desc.<PERSON><PERSON>pay, "bq101", createOrderReq{
		OrderId:     fmt.Sprintf("TEST%d", time.Now().Unix()),
		OrderTime:   orderTime(time.Now()),
		Currency:    pay.BRL,
		Amount:      "100",
		Goods:       "1000 coins",
		CallbackUrl: desc.Ganopay.CallbackUrl,
		NotifyUrl:   "https://godzilla-api-test.sskjz.com/api/v1/ganopay/notify",
	}))
	if err != nil {
		t.Fatal(err)
	}
	spew.Dump(resp)
}

func TestQueryOrder(t *testing.T) {
	orderId := "TEST1725178272"
	unixTs, _ := strconv.Atoi(orderId[4:])
	orderAt := time.Unix(int64(unixTs), 0)
	desc, err := conf.Provide("../../configs/config.yaml")()
	if err != nil {
		t.Fatal(err)
	}
	sign := newSign1(desc.Ganopay.SecretKey)
	log, _ := zap.NewDevelopment()
	resp, err := doRequest[queryOrderResp](sign, log, resty.New().SetBaseURL(desc.Ganopay.Endpoint), "/gateway/api/queryPay", makeRequest[queryOrderReq](desc.Ganopay, "bq101", queryOrderReq{
		OrderId:   orderId,
		OrderTime: orderTime(orderAt),
		Currency:  pay.BRL,
	}))
	if err != nil {
		t.Fatal(err)
	}
	spew.Dump(resp)
}

func TestBalance(t *testing.T) {
	desc, err := conf.Provide("../../configs/config.yaml")()
	if err != nil {
		t.Fatal(err)
	}
	sign, err := newSign2(desc.Ganopay.PublicKey, desc.Ganopay.PrivateKey)
	if err != nil {
		t.Fatal(err)
	}
	log, _ := zap.NewDevelopment()
	resp, err := doRequest[balanceResp](sign, log, resty.New().SetBaseURL(desc.Ganopay.Endpoint), "/df/gateway/proxybalance", makeRequest[balanceReq](desc.Ganopay, "df104", balanceReq{
		MerchantId: desc.Ganopay.MerchantId,
		Currency:   pay.BRL,
	}))
	if err != nil {
		t.Fatal(err)
	}
	spew.Dump(resp)
}

func TestRemitApply(t *testing.T) {
	desc, err := conf.Provide("../../configs/config.yaml")()
	if err != nil {
		t.Fatal(err)
	}
	sign, err := newSign2(desc.Ganopay.PublicKey, desc.Ganopay.PrivateKey)
	if err != nil {
		t.Fatal(err)
	}
	log, _ := zap.NewDevelopment()
	resp, err := doRequest[remitApplyResp](sign, log, resty.New().SetBaseURL(desc.Ganopay.Endpoint), "/df/gateway/proxyrequest", makeRequest[remitApplyReq](desc.Ganopay, "df104", remitApplyReq{
		OrderNo:     fmt.Sprintf("TEST%d", time.Now().Unix()),
		Currency:    pay.BRL,
		TotalAmount: "100",
		TotalNum:    "1",
		Detail: []remitForm{
			{
				Seq:          "1",
				Amount:       "100",
				AccType:      "0",
				CertType:     "1",
				CertId:       "**************",
				BankCardNo:   "**************",
				BankCardName: "OPBR INVESTIMENTOS LTDA",
			},
		},
		NotifyUrl: "https://godzilla-api-test.sskjz.com/api/v1/ganopay/notify",
	}))
	if err != nil {
		t.Fatal(err)
	}
	spew.Dump(resp)
}

func TestRemitQuery(t *testing.T) {
	orderId := "TEST1725260049"
	unixTs, _ := strconv.Atoi(orderId[4:])
	orderAt := time.Unix(int64(unixTs), 0)
	desc, err := conf.Provide("../../configs/config.yaml")()
	if err != nil {
		t.Fatal(err)
	}
	sign, err := newSign2(desc.Ganopay.PublicKey, desc.Ganopay.PrivateKey)
	if err != nil {
		t.Fatal(err)
	}
	log, _ := zap.NewDevelopment()
	resp, err := doRequest[remitQueryResp](sign, log, resty.New().SetBaseURL(desc.Ganopay.Endpoint), "/df/gateway/proxyquery", makeRequest[remitQueryReq](desc.Ganopay, "df104", remitQueryReq{
		OrderNo:   orderId,
		OrderTime: orderTime(orderAt),
		Currency:  pay.BRL,
	}))
	if err != nil {
		t.Fatal(err)
	}
	spew.Dump(resp)
}

func TestNotify1(t *testing.T) {
	raw := []byte(`{"body":{"amount":"100","biz":"bq101","chargeTime":"**********2503","mchtId":"2000713000197336","orderId":"TEST1725178272","payType":"bq","seq":"af89117465e14533b44aa46a130190d9","status":"SUCCESS","tradeId":"1830156456334659584"},"head":{"respCode":"0000","respMsg":"Request success"},"sign":"b59264abff871f49e511d36da7d014c3"}`)
	desc, err := conf.Provide("../../configs/config.yaml")()
	if err != nil {
		t.Fatal(err)
	}
	sign := newSign1(desc.Ganopay.SecretKey)
	if assert.NoError(t, sign.Decode(&raw)) {
		spew.Dump(raw)
	}
}

func TestNotify2(t *testing.T) {
	raw := []byte(`{"head":{"respMsg":"Request success","respCode":"0000"},"body":"K0X9gqLRCKYl2oBcjMXFt7Ke7VtsnvzG3jR7vCLbHEFgq7SU2jiI%2B4Cw8nPGEwclIyqBx8KvWawFWIwEGpe4i7Whx%2FJzxutbM%2F6gW7ViMzPJDmsp3Q%2BT3Q9b%2B3NE%2B%2BTn3mgLJWRaBE149VAAYchGIzZIhJwY7jgCoh2Fd8HGHL1GJS54wNZYEcA9BfjDrl60nHbM3p0aMJ6OkNw3JefcUmJYC7%2Ffrzv%2B7Rnab4z5DsI4xg62WDcmZ%2BVOIBGc%2BrQzVrJG5V%2FRq%2BEDrzAKMlbbU1i9wr7t4hWr1%2F0w%2FtZgTqynOBPfOo7flA181SGRR6jdEkQZ111GQ%2Bxbx2diVc%2FnyhB4fNEC60fhwMCJ5rE9I4YUCu1kmB7Tu1JOnH%2F4ZMid21b9DmyX7bPlwq5YFdA96F%2F%2FT21FwowspaWknwUcBuw3E2%2BDXJbgcajqKZRM3pk0t1d4hEMrfip5OpAyLuaLi3imTOBD27RyZ5vybTzuoHygejDdxWOSg9FBFm0Gu%2BER"}`)
	desc, err := conf.Provide("../../configs/config.yaml")()
	if err != nil {
		t.Fatal(err)
	}
	sign, err := newSign2(desc.Ganopay.PublicKey, desc.Ganopay.PrivateKey)
	if err != nil {
		t.Fatal(err)
	}
	if assert.NoError(t, sign.Decode(&raw)) {
		spew.Dump(raw)
	}
}
