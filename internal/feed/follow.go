package feed

import (
	"context"
	"fmt"
	"sort"
	"time"

	"github.com/samber/lo"
	"gitlab.sskjz.com/overseas/live/osl/internal/live"
	"gitlab.sskjz.com/overseas/live/osl/pkg/app"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
)

const (
	keyIntimacyViewed = "SET:FEED:FVI:%s:%d"
	keyLikeViewed     = "SET:FEED:FLL:%s:%d"
	ttlViewed         = 5 * time.Minute
)

const (
	FollowByIntimacy = "INTIMACY"
	FollowByLike     = "LIKE"
)

// 获取列表
func (m *Manager) FollowingLive(ctx *api.Context, followBy string, userId string, cursor int64) (*FollowingLive, error) {
	num := 20

	if cursor <= 0 {
		cursor = time.Now().UnixMilli()
	} else {
		paramKey, _ := m.getFollowingLiveViewedKey(userId, cursor, followBy)

		exist, _ := m.isFollowingLiveViewedExist(ctx, paramKey)

		if !exist {
			cursor = time.Now().UnixMilli()
		}
	}

	cachedKey, err := m.getFollowingLiveViewedKey(userId, cursor, followBy)

	if err != nil {
		return nil, err
	}

	followingUserIds, err := m.fg.TargetIds(ctx, userId)

	if err != nil {
		return nil, err
	}

	viewed, _ := m.getFollowingLiveViewedMembers(ctx, cachedKey)

	rooms := make([]live.Room, 0)

	for _, v := range followingUserIds {
		// 过滤已查看的
		if lo.Contains(viewed, v) {
			continue
		}

		r, err := m.lm.RoomByUserId2(ctx, v)

		if err != nil {
			continue
		}

		if !r.IsLiving() {
			// 客户端版本不支持显示连线中的用户
			if !showLinkedUser(ctx) {
				continue
			}

			// 不在连线中
			if li, _ := m.lkm.UserLinkInfo2(ctx, v); li == nil {
				continue
			}

			// 连线中，但用户设置了隐藏连线状态
			if s, _ := m.pvm.Take(ctx, v); s != nil {
				if s.HideLinkStatus {
					continue
				}
			}
		}

		rooms = append(rooms, *r)
	}

	tmp := make([]FollowingLiveRoom, 0)

	for _, v := range rooms {
		r := FollowingLiveRoom{
			Room:     v,
			Like:     0,
			Intimacy: 0,
		}

		switch followBy {
		case FollowByIntimacy:
			r.Intimacy = m.getIntimacy(ctx, userId, v.UserId)
		case FollowByLike:
			r.Like, _ = m.sm.GetSessionLike2(ctx, v.SessionId.Hex())
		}

		tmp = append(tmp, r)
	}

	switch followBy {
	case FollowByIntimacy:
		// 亲密度排序
		sort.SliceStable(tmp, func(i, j int) bool {
			return tmp[i].Intimacy > tmp[j].Intimacy
		})
	case FollowByLike:
		// 热度排序
		sort.SliceStable(tmp, func(i, j int) bool {
			return tmp[i].Like > tmp[j].Like
		})
	}

	if len(tmp) > num {
		tmp = tmp[:num]
	}

	for _, v := range tmp {
		m.setFollowingLiveViewed(ctx, cachedKey, v.Room.UserId)
	}

	res := &FollowingLive{
		List:   tmp,
		Cursor: cursor,
	}

	return res, nil
}

func (m *Manager) getFollowingLiveViewedKey(userId string, cursor int64, followBy string) (string, error) {
	switch followBy {
	case FollowByIntimacy:
		return fmt.Sprintf(keyIntimacyViewed, userId, cursor), nil
	case FollowByLike:
		return fmt.Sprintf(keyLikeViewed, userId, cursor), nil
	default:
		return "", fmt.Errorf("invalid followBy: %s", followBy)
	}
}

func (m *Manager) setFollowingLiveViewed(ctx context.Context, key, userId string) error {
	err := m.rc.Client.SAdd(ctx, key, userId).Err()

	m.rc.Client.Expire(ctx, key, ttlViewed)

	return err
}

func (m *Manager) getFollowingLiveViewedMembers(ctx context.Context, key string) ([]string, error) {
	return m.rc.Client.SMembers(ctx, key).Result()
}

func (m *Manager) isFollowingLiveViewedExist(ctx context.Context, key string) (bool, error) {
	val, err := m.rc.Client.Exists(ctx, key).Result()

	return val == 1, err
}

func showLinkedUser(ctx *api.Context) bool {
	return app.CVGTE(ctx, app.V1110, 62)
}
