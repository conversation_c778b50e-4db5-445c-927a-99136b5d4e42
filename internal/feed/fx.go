package feed

import (
	"gitlab.sskjz.com/go/ev"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/contrib"
	"gitlab.sskjz.com/overseas/live/osl/internal/fclub"
	"gitlab.sskjz.com/overseas/live/osl/internal/follow"
	"gitlab.sskjz.com/overseas/live/osl/internal/link"
	"gitlab.sskjz.com/overseas/live/osl/internal/live"
	"gitlab.sskjz.com/overseas/live/osl/internal/room/rsd"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/privacy"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
)

func Provide(
	dbmc *db.MongoClient,
	fg follow.Getter,
	lm *live.Manager,
	sm *rsd.Stats,
	cm *contrib.Manager,
	fc fclub.Getter,
	rc *redi.Client,
	ev ev.Bus,
	lkm *link.Manager,
	pvm *privacy.Manager,
	vnd log.Vendor,
) (*Manager, error) {
	mgr := newManager(dbmc, fg, lm, sm, cm, fc, rc, ev, lkm, pvm, vnd.Scope("feed.mgr"))

	return mgr, nil
}
