package halloween

import (
	"context"
	"fmt"
	"sort"
	"time"

	"github.com/bytedance/sonic"
	"github.com/go-redis/redis/v8"
	"github.com/rs/xid"
	"github.com/samber/lo"
)

const (
	timeSection = 20 * time.Second  // 按时间分段
	ttlPayload  = 100 * time.Second // 消息过期时间
	ttlTopList  = 100 * time.Second // 榜单过期时间
)

const (
	keyTopList  = "HALLOW:BS:T:%d"    // 时间段内飞字价值排行榜 时间段+奖池
	keyUserList = "HALLOW:BS:U:%d:%s" // 用户某个时间段的飞字记录 时间段+userId 用于优先填充自己的数据
	keyPayload  = "HALLOW:BS:P:%s"    // payload数据 payloadId
)

type Payload struct {
	Id     string    `json:"id"`
	UserId string    `json:"userId"`
	Coins  int       `json:"coins"`
	Time   time.Time `json:"time"`
}

func (m *Manager) Broadcasts(ctx context.Context, userId string, timestamp int64) []*Payload {
	var payloads []*Payload
	// 上一个10秒所在时间
	ts := time.Now().Truncate(timeSection).Add(-timeSection)

	// 客户端传上的时间戳已处于要取的时间段
	if timestamp > 0 && timestamp >= ts.Unix() {
		return payloads
	}

	// 获取所有的广播列表
	if ps, err := m.bcTopPayloads(ctx, ts); err != nil {
		return payloads
	} else {
		payloads = ps
	}

	// 首次进入，上个10秒不足10条，再往前找一次
	if timestamp == 0 && len(payloads) < 10 {
		if ps, err := m.bcTopPayloads(ctx, ts.Add(-timeSection)); err != nil {
			return payloads
		} else {
			payloads = append(payloads, ps...)
		}
	}

	// 获取当前用户的payloads
	myPayloads, _ := m.bcUserPayloads(ctx, userId, ts)

	if len(myPayloads) > 0 {
		var idd []string
		for _, p := range payloads {
			idd = append(idd, p.Id)
		}

		add := false
		for _, p2 := range myPayloads {
			if lo.Contains(idd, p2.Id) {
				continue
			}
			add = true
			payloads = append(payloads, p2)
		}

		if add {
			sort.SliceStable(payloads, func(i, j int) bool {
				return payloads[i].Time.Before(payloads[j].Time)
			})

			if len(payloads) > 10 {
				del := len(payloads) - 10
				// 从最后删除非当前userId的记录
				for i := len(payloads) - 1; i >= 0; i-- {
					if del <= 0 {
						break
					}
					if payloads[i].UserId == userId {
						continue
					}
					// 不会越界
					payloads = append(payloads[:i], payloads[i+1:]...)
					del--
				}
			}
		}
	}

	return payloads
}

func (m *Manager) drawCoins(ctx context.Context, at time.Time, userId string, coins int) {
	_ = m.bcPush(ctx, &Payload{
		Id:     xid.New().String(),
		UserId: userId,
		Coins:  coins,
		Time:   at,
	})
}

func (m *Manager) bcPush(ctx context.Context, p *Payload) error {
	bs, err := sonic.Marshal(p)
	if err != nil {
		return fmt.Errorf("payload marshal fail: %w", err)
	}

	tx := m.rc.Pipeline()

	// 写入飞字详细内容
	tx.SetEX(ctx, fmt.Sprintf(keyPayload, p.Id), bs, ttlPayload)

	ts := p.Time.Truncate(timeSection).Unix()
	// 写入时间段集合
	topListKey := fmt.Sprintf(keyTopList, ts)
	tx.ZAdd(ctx, topListKey, &redis.Z{
		Score:  float64(p.Coins), // 展示礼物的总价值
		Member: p.Id,
	})
	tx.Expire(ctx, topListKey, ttlTopList)

	// 写入时间段内用户记录
	userKey := fmt.Sprintf(keyUserList, ts, p.UserId)
	tx.SAdd(ctx, userKey, p.Id)
	tx.Expire(ctx, userKey, ttlPayload)

	// 写入时间段内房间记录
	if _, err := tx.Exec(ctx); err != nil {
		return fmt.Errorf("payload push fail: %w", err)
	}

	return nil
}

func (m *Manager) bcTopPayloads(ctx context.Context, at time.Time) ([]*Payload, error) {
	pIds, err := m.rc.ZRevRange(ctx, fmt.Sprintf(keyTopList, at.Unix()), 0, 9).Result()
	if err != nil {
		return nil, err
	}

	if len(pIds) == 0 {
		return nil, nil
	}

	keys := lo.Map(pIds, func(id string, _ int) string {
		return fmt.Sprintf(keyPayload, id)
	})

	payloads, err := m.rc.MGet(ctx, keys...).Result()
	if err != nil {
		return nil, err
	}

	var ret []*Payload
	for _, p := range payloads {
		var r Payload
		if err := sonic.Unmarshal([]byte(p.(string)), &r); err != nil {
			continue
		}
		ret = append(ret, &r)
	}

	return ret, nil
}

func (m *Manager) bcUserPayloads(ctx context.Context, userId string, at time.Time) ([]*Payload, error) {
	userKey := fmt.Sprintf(keyUserList, at.Unix(), userId)

	pIds, err := m.rc.SMembers(ctx, userKey).Result()
	if err != nil {
		return nil, err
	}

	keys := lo.Map(pIds, func(id string, _ int) string {
		return fmt.Sprintf(keyPayload, id)
	})

	payloads, err := m.rc.MGet(ctx, keys...).Result()
	if err != nil {
		return nil, err
	}

	var ret []*Payload
	for _, p := range payloads {
		var r Payload
		if err := sonic.Unmarshal([]byte(p.(string)), &r); err != nil {
			continue
		}
		ret = append(ret, &r)
	}

	return ret, nil
}
