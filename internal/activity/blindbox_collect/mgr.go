package blindbox_collect

import (
	"context"
	"fmt"
	"gitlab.sskjz.com/overseas/live/osl/internal/im"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/dress"
	"math"
	"sort"
	"strconv"
	"time"

	"github.com/go-redis/redis/v8"
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/internal/gift"
	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"go.uber.org/zap"
)

type Manager struct {
	ug  user.Getter
	gm  *gift.Manager
	rc  *redi.Client
	fm  *fund.Manager
	im  *im.Manager
	dm  *dress.Manager
	rm  *redi.Mutex
	log *zap.Logger
}

func newManager(ug user.Getter, gm *gift.Manager, rc *redi.Client, fm *fund.Manager, im *im.Manager, dm *dress.Manager, rm *redi.Mutex, log *zap.Logger) *Manager {
	m := &Manager{
		ug:  ug,
		gm:  gm,
		rc:  rc,
		fm:  fm,
		im:  im,
		dm:  dm,
		rm:  rm,
		log: log,
	}

	return m
}

type SES struct {
	StartTime time.Time
	EndTime   time.Time
	Stage     string
}

var (
	sesList = []SES{
		{
			StartTime: time.Date(2025, 05, 19, 0, 0, 0, 0, ctz.Brazil),
			EndTime:   time.Date(2025, 05, 25, 23, 59, 59, 0, ctz.Brazil),
			Stage:     "********",
		},
	}
	// 奖励水晶
	rankAward = map[int]int{
		1: 500000,
		2: 300000,
		3: 150000,
		4: 100000,
		5: 50000,
	}
)

func (m *Manager) GetSES(t time.Time) SES {
	t = t.In(ctz.Brazil)
	for _, ses := range sesList {
		if t.After(ses.StartTime) && t.Before(ses.EndTime) {
			return ses
		}
	}
	return SES{}
}

func (m *Manager) GetUserData(ctx context.Context, stage, userId string) (*UserData, error) {
	var userData UserData

	uac, err := m.ug.Account(ctx, userId)
	if err != nil {
		return nil, err
	}

	rank, value := m.GetRankByUserId(ctx, stage, userId)
	userData.UserRank = UserRank{
		Rank:  rank,
		Value: value,
		User:  uac,
	}

	// 拿出背包
	var (
		collectData = make(map[int]int)
		currentHave int
		minNum      int = math.MaxInt32
	)
	userKey := fmt.Sprintf(keyBlindBoxCollect, stage, userId)
	currentInfo := m.rc.HGetAll(ctx, userKey).Val()
	for _, id := range allCollectGiftId {
		haveStr := currentInfo[strconv.Itoa(id)]
		haveInt, _ := strconv.Atoi(haveStr)
		collectData[id] = haveInt
		if haveInt < minNum {
			minNum = haveInt
		}
	}
	for _, have := range collectData {
		if have-minNum > 0 {
			currentHave++
		}
	}
	userData.CurrentHave = currentHave
	userData.CollectData = collectData

	return &userData, nil
}

func (m *Manager) GetUserCollectData(ctx context.Context, stage, userId string) map[int]bool {
	userKey := fmt.Sprintf(keyBlindBoxCollect, stage, userId)
	currentInfo := m.rc.HGetAll(ctx, userKey).Val()
	var (
		collectData     = make(map[int]int)
		minNum      int = math.MaxInt32
	)
	for _, id := range allCollectGiftId {
		var haveInt int
		if haveStr, ok := currentInfo[strconv.Itoa(id)]; ok {
			haveInt, _ = strconv.Atoi(haveStr)
		}
		collectData[id] = haveInt
		if haveInt < minNum {
			minNum = haveInt
		}
	}

	var res = make(map[int]bool)
	for _, id := range allCollectGiftId {
		have := collectData[id]
		if have-minNum > 0 {
			res[id] = true
		} else {
			res[id] = false
		}
	}

	m.log.Debug("GetUserCollectData",
		zap.Any("currentInfo", currentInfo),
		zap.Any("collectData", collectData),
		zap.Any("minNum", minNum),
		zap.Any("res", res),
	)

	return res
}

// GetRanking 获取榜单
func (m *Manager) GetRanking(ctx context.Context, stage string) ([]UserRank, error) {
	var (
		rankList []UserRank
	)

	ranks := m.getRank(context.Background(), stage)
	for k, r := range ranks {
		anchorId := r.Member.(string)
		uac, err := m.ug.Account(ctx, anchorId)
		if err != nil {
			return nil, err
		}

		userRank := UserRank{
			Rank:  k + 1,
			Value: int(r.Score),
			User:  uac,
		}

		rankList = append(rankList, userRank)

	}

	return rankList, nil
}

func (m *Manager) GetRankByUserId(ctx context.Context, stage string, userId string) (int, int) {
	rankKey := fmt.Sprintf(keyBlindBoxCollectActivityRank, stage)
	value, err := m.rc.ZScore(ctx, rankKey, userId).Result()
	if err != nil {
		return 0, 0
	}

	rank, err := m.rc.ZRevRank(ctx, rankKey, userId).Result()
	if err != nil {
		return 0, 0
	}
	return int(rank) + 1, int(value)
}

// 同分情况，先到者在前
func (m *Manager) getRank(ctx context.Context, stage string) []redis.Z {
	var rank []redis.Z

	rankKey := fmt.Sprintf(keyBlindBoxCollectActivityRank, stage)
	rank, err := m.rc.ZRevRangeWithScores(ctx, rankKey, 0, 29).Result()
	if err != nil {
		return rank
	}

	var userIds []string
	for _, v := range rank {
		userIds = append(userIds, v.Member.(string))
	}

	var updateMap = map[string]int64{}
	u := m.rc.HMGet(ctx, fmt.Sprintf(keyBlindBoxCollectUpdateTime, stage), userIds...).Val()
	for i, v := range userIds {
		item := u[i]
		if str, ok := item.(string); ok {
			str2Int, _ := strconv.ParseInt(str, 10, 64)
			updateMap[v] = str2Int
		}
	}

	sort.Slice(rank, func(i, j int) bool {
		if rank[i].Score == rank[j].Score {
			return updateMap[rank[i].Member.(string)] < updateMap[rank[j].Member.(string)]
		}
		return rank[i].Score > rank[j].Score
	})

	return rank
}

func (m *Manager) SendCollectReward(ctx context.Context, userId string) error {
	if err := m.dm.SetBadge(ctx, userId, CollectBadge, time.Now().AddDate(0, 0, 7)); err != nil {
		return err
	}

	if err := m.dm.SetAvatarBorder(ctx, userId, CollectAvatarBorder, time.Now().AddDate(0, 0, 7)); err != nil {
		return err
	}

	if err := m.fm.Income(ctx, userId, fund.JTypeRewards, fund.PTypeFruits, CollectFruits); err != nil {
		m.log.Info("盲盒集齐发奖:调用加币方法错误",
			zap.String("userId", userId),
			zap.Int("reward", CollectFruits),
			zap.Int64("time", time.Now().Unix()),
			zap.Error(err))
		return err
	}

	m.log.Info("盲盒收集活动，集齐奖励发放成功",
		zap.String("userId", userId),
	)

	return nil
}

func (m *Manager) SendRankReward(ctx context.Context) error {
	t := time.Now().AddDate(0, 0, -1).In(ctz.Brazil)
	m.log.Info("盲盒收集活动励发送开始", zap.Int64("time", t.Unix()))
	// 是否是发奖日
	ses := m.GetSES(t)
	if ses.Stage == "" || time.Now().In(ctz.Brazil).Before(ses.EndTime) {
		m.log.Error("盲盒收集活动奖励发送失败:非发奖时间", zap.Int64("time", t.Unix()))
		return biz.NewError(biz.ErrInvalidParam, "非发奖时间")
	}

	rewardKey := fmt.Sprintf(keyBlindBoxCollectActivityReward, ses.Stage)
	if m.rc.Exists(ctx, rewardKey).Val() != 0 {
		m.log.Info("盲盒收集活动奖励发送失败:已发放完成", zap.Int64("time", t.Unix()))
		return biz.NewError(biz.ErrInvalidParam, "奖励已经发放")
	}

	rank := m.getRank(ctx, ses.Stage)
	if len(rank) > 0 {
		for i, v := range rank {
			if i+1 > 5 {
				break
			}
			userId := v.Member.(string)
			reward := rankAward[i+1]
			if reward == 0 {
				continue
			}
			if err := m.fm.Income(ctx, userId, fund.JTypeRewards, fund.PTypeFruits, reward); err != nil {
				m.log.Info("盲盒收集活动奖励发送失败:调用加币方法错误",
					zap.Int("rank", i+1),
					zap.String("userId", userId),
					zap.Int("reward", reward),
					zap.Int64("time", t.Unix()),
					zap.Error(err))
				continue
			}

			// 发送消息
			if err := m.im.SendSystemNoticeTextToUser(
				ctx,
				userId,
				fmt.Sprintf(
					"Parabéns! Você alcançou o %dº lugar no ranking do evento Mural de Presentes, e sua recompensa de %d de cristais já foi enviada ao seu saldo de cristais.",
					i+1,
					reward,
				),
			); err != nil {
				m.log.Error("盲盒收集活动奖励私信通知失败",
					zap.Error(err),
					zap.String("userId", userId),
					zap.Int("reward", reward),
					zap.Int64("time", t.Unix()))
			}

			m.log.Info("盲盒收集活动奖励发送成功",
				zap.Int("rank", i+1),
				zap.String("userId", userId),
				zap.Int("reward", reward),
				zap.Int64("time", t.Unix()))
		}
	} else {
		m.log.Info("盲盒收集活动奖励发送失败:榜单为空",
			zap.Int64("time", t.Unix()))
	}

	m.rc.SetEX(ctx, rewardKey, 1, ttlBlindBoxCollectActivityReward)
	m.log.Info("盲盒收集活动奖励发送完成", zap.Int64("time", t.Unix()))

	return nil
}
