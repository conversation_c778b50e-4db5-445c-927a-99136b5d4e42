package christmas

import (
	"context"
	"crypto/rand"
	"fmt"
	"math/big"
	"sort"
	"strconv"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/jinzhu/now"
	"github.com/samber/lo"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/mixer"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// 对外服务接口

func (m *Manager) Info(ctx context.Context) (*Info, error) {
	r, _ := m.round(ctx)

	return &Info{
		StartTime: StartTime.Unix(),
		EndTime:   EndTime.Unix(),
		Status:    status(),
		Round:     r,
		Records:   m.Records(ctx, 10),
	}, nil
}

func (m *Manager) Join(ctx context.Context, userId string) error {
	// 用户锁
	l, err := m.dm.Lock(ctx, fmt.Sprintf("STR:ACT:CHRISTMAS:JOIN:%s", userId))
	if err != nil {
		return err
	}
	defer l.MustUnlock()

	// 判断用户余额是否足够
	fac, err := m.fg.Take(ctx, userId)

	if err != nil {
		return biz.NewError(biz.ErrBusiness, "Balance acquisition failure")
	}

	if fac.BVal(fund.PTypeDiamond).IntPart() < costDiamond {
		return fund.ErrBalanceNotEnough
	}

	// 活动锁
	al, err := m.dm.Lock(ctx, "STR:ACT:CHRISTMAS:MUTEX")
	if err != nil {
		return err
	}
	defer al.MustUnlock()

	rr, err := m.roundRecord(ctx)

	if err != nil {
		return err
	}

	if err := m.canJoin(ctx, rr, userId); err != nil {
		return err
	}

	seatNo := m.randomSeat(rr)

	err = m.mc.TryTxn(ctx, func(ctx context.Context) error {
		ur, err := m.mc.Collection(RoundRecordCollectionName()).UpdateOne(
			ctx,
			bson.M{
				"_id":                           rr.Id,
				"phase":                         rr.Phase,
				"userIds":                       bson.M{"$nin": []string{userId}}, // 未参与
				fmt.Sprintf("seats.%d", seatNo): bson.M{"$exists": false},         // 空座位
			},
			bson.M{
				"$set": bson.M{
					fmt.Sprintf("seats.%d", seatNo): userId,
				},
				"$push": bson.M{
					"userIds": userId,
				},
			},
		)

		if err != nil {
			return err
		}

		if ur.MatchedCount == 0 {
			return biz.NewError(biz.ErrBusiness, "can not join")
		}

		if ur.ModifiedCount == 0 {
			return biz.NewError(biz.ErrBusiness, "can not join")
		}

		// 用户参与记录
		_, err = m.mc.Collection(UserRecordCollectionName()).InsertOne(ctx, UserRecord{
			Id:        primitive.NewObjectID(),
			UserId:    userId,
			RoundId:   rr.Id.Hex(),
			GiftId:    0,
			Diamond:   costDiamond,
			CreatedAt: time.Now(),
		})

		if err != nil {
			return err
		}

		err = m.fm.Expend(ctx, userId, fund.JTypeOthers, fund.PTypeDiamond, costDiamond)

		if err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return err
	}

	// 是否是今日首次参加，头像框1天
	joinKey := fmt.Sprintf("STR:ACT:CHRISTMAS:JOIN:%s", userId)
	duration := time.Until(now.New(time.Now().In(ctz.Brazil)).EndOfDay())
	if m.rc.SetNX(ctx, joinKey, 1, duration).Val() {
		m.sendAvatarBorder(ctx, userId, 1)
	}

	return nil
}

func (m *Manager) JoinRecord(ctx context.Context, userId string) ([]JoinRecord, error) {
	cursor, err := m.mc.Collection(UserRecordCollectionName()).Find(
		ctx,
		bson.M{
			"userId": userId,
		},
		options.Find().SetSort(bson.M{"createdAt": -1}).SetLimit(30),
	)

	if err != nil {
		return nil, err
	}

	defer cursor.Close(ctx)

	var ur []UserRecord

	if err := cursor.All(ctx, &ur); err != nil {
		return nil, err
	}

	var ret []JoinRecord

	for _, v := range ur {
		rr, err := m.roundRecordById(ctx, v.RoundId)

		if err != nil {
			continue
		}

		ret = append(ret, JoinRecord{
			WinGiftId:   v.GiftId,
			RoundStatus: rr.Status,
			CreateTime:  v.CreatedAt.Unix(),
		})
	}

	return ret, nil
}

func (m *Manager) RankUserList(ctx context.Context) []RankItem {
	ret := make([]RankItem, 0)

	day := time.Now().In(ctz.Brazil).Format("********")
	userIds, err := m.rc.ZRevRange(ctx, m.getRankUserKey(day), 0, 9).Result()

	if err != nil {
		return ret
	}

	for _, userId := range userIds {
		gifts := make([][]int, 0)
		var u *types.User

		hm, err := m.rc.HGetAll(ctx, m.getUserKey(day, userId)).Result()

		if err == nil {
			for k, v := range hm {
				giftId, _ := strconv.Atoi(k)
				giftNum, _ := strconv.Atoi(v)

				gifts = append(gifts, []int{giftId, giftNum, m.giftSortValue(giftId)})
			}

			sort.SliceStable(gifts, func(i, j int) bool { return gifts[i][2] > gifts[j][2] })
		}

		acc, err := m.ug.Account(ctx, userId)

		if err == nil {
			u = mixer.User(ctx, acc)
		}

		ret = append(ret, RankItem{
			User:  u,
			Gifts: gifts,
		})
	}

	return ret
}

func (m *Manager) GetRankMine(ctx context.Context, userId string) *RankMine {
	day := time.Now().In(ctz.Brazil).Format("********")

	no, err := m.rc.ZRevRank(ctx, m.getRankUserKey(day), userId).Result()

	if err != nil {
		no = 100
	}

	var gap int64

	if no > 0 && no < 100 {
		// 比用户排名靠前1位的人分数
		userIds, err := m.rc.ZRevRange(ctx, m.getRankUserKey(day), no-1, no-1).Result()

		if err == nil && len(userIds) > 0 {
			score := m.rc.ZScore(ctx, m.getRankUserKey(day), userIds[0]).Val()

			gap = int64(score - m.rc.ZScore(ctx, m.getRankUserKey(day), userId).Val())

			if gap < 0 {
				gap = 0
			}
		}
	}

	if gap == 0 {
		gap = 1
	}

	return &RankMine{
		Gap: gap,
		No:  int(no) + 1,
	}
}

func (m *Manager) RankAnchorList(ctx context.Context) []RankItem {
	ret := make([]RankItem, 0)

	day := time.Now().In(ctz.Brazil).Format("********")
	anchorIds, err := m.rc.ZRevRange(ctx, m.getRankAnchorKey(day), 0, 9).Result()

	if err != nil {
		return ret
	}

	for _, anchorId := range anchorIds {
		gifts := make([][]int, 0)
		var u *types.User

		hm, err := m.rc.HGetAll(ctx, m.getAnchorKey(day, anchorId)).Result()

		if err == nil {
			for k, v := range hm {
				giftId, _ := strconv.Atoi(k)
				giftNum, _ := strconv.Atoi(v)

				gifts = append(gifts, []int{giftId, giftNum, m.giftSortValue(giftId)})
			}

			sort.SliceStable(gifts, func(i, j int) bool { return gifts[i][2] > gifts[j][2] })
		}

		acc, err := m.ug.Account(ctx, anchorId)

		if err == nil {
			u = mixer.User(ctx, acc)
		}

		ret = append(ret, RankItem{
			User:  u,
			Gifts: gifts,
		})
	}

	return ret
}

func (m *Manager) canJoin(ctx context.Context, rr *RoundRecord, userId string) error {
	p := m.phase(ctx, rr)

	if p.Name != RoundPhaseJoin || p.Countdown == 0 {
		// 等待下一局
		return biz.NewError(biz.ErrBusiness, "Aguarde a próxima rodada")
	}

	if lo.Contains(rr.UserIds, userId) {
		// 已经参与
		return biz.NewError(biz.ErrBusiness, "Você participou da rodada atual")
	}

	if len(rr.Seats) == rr.TotalSeat {
		// 慢了一步，人数已满
		return biz.NewError(biz.ErrBusiness, "Esta rodada está cheia, aguarde a próxima")
	}

	return nil
}

func (m *Manager) randomSeat(rr *RoundRecord) int {
	availableSeat := make([]int, 0)

	for i := 1; i <= rr.TotalSeat; i++ {
		_, ok := rr.Seats[i]
		if !ok {
			availableSeat = append(availableSeat, i)
		}
	}

	if len(availableSeat) == 0 {
		return 0
	}

	n, err := rand.Int(rand.Reader, big.NewInt(int64(len(availableSeat))))

	if err != nil {
		return 0
	}

	return availableSeat[n.Int64()]
}

const (
	keyResultUnread = "STR:CHRISTMAS:RESULT:UNREAD:%s" // userId
	ttlResultUnread = time.Hour * 24
)

func (m *Manager) Result(ctx context.Context, userId string) (*Round, error) {
	roundId, err := m.rc.Get(ctx, fmt.Sprintf(keyResultUnread, userId)).Result()

	if err != nil {
		if err == redis.Nil {
			return nil, nil
		}

		return nil, err
	}

	return m.roundById(ctx, roundId)
}

func (m *Manager) ReadResult(ctx context.Context, userId string) error {
	return m.rc.Del(ctx, fmt.Sprintf(keyResultUnread, userId)).Err()
}

func (m *Manager) setUnreadResult(ctx context.Context, userId, roundId string) error {
	return m.rc.SetEX(ctx, fmt.Sprintf(keyResultUnread, userId), roundId, ttlResultUnread).Err()
}
