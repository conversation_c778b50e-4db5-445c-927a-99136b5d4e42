package super_lucky_star

import (
	"context"
	"gitlab.sskjz.com/overseas/live/osl/internal/evt"
	"gitlab.sskjz.com/overseas/live/osl/internal/im"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/dress"

	"gitlab.sskjz.com/go/cron"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"

	"gitlab.sskjz.com/go/ev"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/gift"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
)

func Provide(
	ug user.Getter,
	gm *gift.Manager,
	rc *redi.Client,
	fm *fund.Manager,
	im *im.Manager,
	dm *dress.Manager,
	vnd log.Vendor,
) (*Manager, error) {
	m := newManager(ug, gm, rc.Cluster("rank"), fm, im, dm, vnd.Scope("super_lucky_star.mgr"))

	return m, nil
}

func Invoke(
	evb ev.Bus,
	mgr *Manager,
	sch *cron.Scheduler,
) {
	evb.Watch(evt.GiftSend, "superlucky.star.gift", ev.NewWatcher(mgr.onSendGift), ev.WithAsync())

	// 巴西时间每天00:01发送奖励：UTC 03:01 = Sao_Paulo 00:01
	sch.CronWithSeconds("0 1 3 * * *").Do(sch.Exclusive("activity.superluckystar.reward", func(ctx context.Context) error {
		return mgr.SendReward(ctx)
	}))
}
