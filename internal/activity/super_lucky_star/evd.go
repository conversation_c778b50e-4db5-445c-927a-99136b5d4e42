package super_lucky_star

import (
	"context"
	"fmt"
	"gitlab.sskjz.com/overseas/live/osl/internal/evt"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
	"go.uber.org/zap"
)

func (m *Manager) onSendGift(ctx context.Context, evd *evt.SendGift) error {
	if evd.GiftId == 0 || evd.Count <= 0 || evd.AnchorId == "" || len(evd.Prizes) == 0 {
		return nil
	}

	// 活动时间判断
	if evd.At.Before(startTime) || evd.At.After(endTime) {
		return nil
	}

	giftInfo, err := m.gm.GiftById(evd.GiftId)
	if err != nil {
		return err
	}

	var (
		ratio float64
		value int64
	)
	switch {
	case giftInfo.Diamond <= 10:
		ratio = 0.05
	case giftInfo.Diamond > 10 && giftInfo.Diamond <= 50:
		ratio = 0.15
	case giftInfo.Diamond > 50 && giftInfo.Diamond <= 100:
		ratio = 0.25
	case giftInfo.Diamond > 100 && giftInfo.Diamond <= 500:
		ratio = 0.35
	case giftInfo.Diamond > 500 && giftInfo.Diamond <= 1000:
		ratio = 0.45
	case giftInfo.Diamond > 1000:
		ratio = 0.55
	}

	for _, prize := range evd.Prizes {
		if prize < 500 {
			continue
		}
		value += int64(float64(giftInfo.Diamond*prize) * ratio)
	}

	if value == 0 {
		return nil
	}

	rankKey := fmt.Sprintf(keySuperLuckyStarActivityRank, evd.At.In(ctz.Brazil).Format("20060102"))
	if err := m.rc.ZIncrBy(ctx, rankKey, float64(value), evd.UserId).Err(); err != nil {
		m.log.Error("superluckystar onSendGift zincrBy score err", zap.Error(err), zap.Any("evd", evd))
		return err
	}
	m.rc.Expire(ctx, rankKey, ttlSuperLuckyStarActivityRank)

	// 记录用户分数最后更新时间
	updateKey := fmt.Sprintf(keySuperLuckyStarUpdateTime, evd.At.In(ctz.Brazil).Format("20060102"))
	m.rc.HSet(ctx, updateKey, evd.UserId, evd.At.Unix())
	m.rc.Expire(ctx, updateKey, ttlSuperLuckyStarUpdateTime)

	return nil
}
