package super_lucky_star

import (
	"context"
	"fmt"
	"gitlab.sskjz.com/overseas/live/osl/internal/im"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/dress"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/dress/vehicle"
	"sort"
	"strconv"
	"time"

	"github.com/go-redis/redis/v8"
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/internal/gift"
	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"go.uber.org/zap"
)

type Manager struct {
	ug  user.Getter
	gm  *gift.Manager
	rc  *redi.Client
	fm  *fund.Manager
	im  *im.Manager
	dm  *dress.Manager
	log *zap.Logger
}

func newManager(ug user.Getter, gm *gift.Manager, rc *redi.Client, fm *fund.Manager, im *im.Manager, dm *dress.Manager, log *zap.Logger) *Manager {
	return &Manager{
		ug:  ug,
		gm:  gm,
		rc:  rc,
		fm:  fm,
		im:  im,
		dm:  dm,
		log: log,
	}
}

var (
	startTime = time.Date(2025, 06, 02, 0, 0, 0, 0, ctz.Brazil)
	endTime   = time.Date(2025, 06, 8, 23, 59, 59, 0, ctz.Brazil)
	coinAward = map[int]int{
		1:  500000,
		2:  300000,
		3:  200000,
		4:  100000,
		5:  100000,
		6:  60000,
		7:  60000,
		8:  60000,
		9:  60000,
		10: 60000,
		11: 40000,
		12: 40000,
		13: 40000,
		14: 40000,
		15: 40000,
		16: 40000,
		17: 40000,
		18: 40000,
		19: 40000,
		20: 40000,
		21: 20000,
		22: 20000,
		23: 20000,
		24: 20000,
		25: 20000,
		26: 20000,
		27: 20000,
		28: 20000,
		29: 20000,
		30: 20000,
		31: 20000,
		32: 20000,
		33: 20000,
		34: 20000,
		35: 20000,
		36: 20000,
		37: 20000,
		38: 20000,
		39: 20000,
		40: 20000,
	}
)

func (m *Manager) GetRank(ctx context.Context, rankingDay, userId string) (*GiftLuckyInfo, error) {
	var (
		res = GiftLuckyInfo{
			StartAt:  startTime.Unix(),
			EndAt:    endTime.Unix(),
			RankInfo: make([]UserRank, 0),
		}
		t = time.Now().In(ctz.Brazil)
	)

	if rankingDay == "yesterday" {
		t = t.AddDate(0, 0, -1)
	}

	// 获取排行榜
	rank := m.getRank(ctx, t)

	if len(rank) > 0 {
		for i, v := range rank {
			rankUserId := v.Member.(string)
			uac, _ := m.ug.Account(ctx, rankUserId)
			userRank := UserRank{
				Rank:  i + 1,
				User:  uac,
				Value: int64(v.Score),
			}

			res.RankInfo = append(res.RankInfo, userRank)
			if rankUserId == userId {
				res.MyRank = &userRank
			}
		}

		if res.MyRank == nil {
			// 获取自己排名信息
			userRank, userValue := m.getUserRankInfo(ctx, t, userId)
			uac, _ := m.ug.Account(ctx, userId)
			res.MyRank = &UserRank{
				Rank:  userRank,
				User:  uac,
				Value: userValue,
			}
		}
	}

	return &res, nil
}

func (m *Manager) GetWidget(ctx context.Context) (*GiftLuckyInfo, error) {
	var (
		res = GiftLuckyInfo{
			StartAt:  startTime.Unix(),
			EndAt:    endTime.Unix(),
			RankInfo: make([]UserRank, 0),
		}
		t = time.Now().In(ctz.Brazil)
	)

	// 获取排行榜
	rank := m.getRank(ctx, t)
	if len(rank) > 0 {
		for i, v := range rank {
			if i+1 > 3 {
				break
			}
			rankUserId := v.Member.(string)
			uac, _ := m.ug.Account(ctx, rankUserId)
			userRank := UserRank{
				Rank:  i + 1,
				User:  uac,
				Value: int64(v.Score),
			}

			res.RankInfo = append(res.RankInfo, userRank)
		}
	}

	return &res, nil
}

// 同分情况，先到者在前
func (m *Manager) getRank(ctx context.Context, t time.Time) []redis.Z {
	var rank []redis.Z

	rankKey := fmt.Sprintf(keySuperLuckyStarActivityRank, t.Format("********"))
	rank, err := m.rc.ZRevRangeWithScores(ctx, rankKey, 0, 39).Result()
	if err != nil {
		return rank
	}

	var userIds []string
	for _, v := range rank {
		userIds = append(userIds, v.Member.(string))
	}

	var updateMap = map[string]int64{}
	u := m.rc.HMGet(ctx, fmt.Sprintf(keySuperLuckyStarUpdateTime, t.Format("********")), userIds...).Val()
	for i, v := range userIds {
		item := u[i]
		if str, ok := item.(string); ok {
			str2Int, _ := strconv.ParseInt(str, 10, 64)
			updateMap[v] = str2Int
		}
	}

	sort.Slice(rank, func(i, j int) bool {
		if rank[i].Score == rank[j].Score {
			return updateMap[rank[i].Member.(string)] < updateMap[rank[j].Member.(string)]
		}
		return rank[i].Score > rank[j].Score
	})

	return rank
}

// getUserRankInfo 返回我的排名与积分
func (m *Manager) getUserRankInfo(ctx context.Context, t time.Time, userId string) (int, int64) {
	rankKey := fmt.Sprintf(keySuperLuckyStarActivityRank, t.Format("********"))

	rank, err := m.rc.ZRevRank(ctx, rankKey, userId).Result()
	if err != nil {
		return 0, 0
	}
	value, _ := m.rc.ZScore(ctx, rankKey, userId).Result()

	return int(rank + 1), int64(value)
}

func (m *Manager) SendReward(ctx context.Context) error {
	t := time.Now().AddDate(0, 0, -1).In(ctz.Brazil)
	m.log.Info("超级幸运星奖励发送开始", zap.Int64("time", t.Unix()))
	if t.Before(startTime) || t.After(endTime) {
		m.log.Error("超级幸运星奖励发送失败:不在活动时间", zap.Int64("time", t.Unix()))
		return biz.NewError(biz.ErrInvalidParam, "不在活动时间")
	}

	rewardKey := fmt.Sprintf(keySuperLuckyStarActivityReward, t.Format("********"))
	if m.rc.Exists(ctx, rewardKey).Val() != 0 {
		m.log.Info("超级幸运星奖励发送失败:已发放完成", zap.Int64("time", t.Unix()))
		return biz.NewError(biz.ErrInvalidParam, "奖励已经发放")
	}

	// 获取榜单，发放奖励
	rank := m.getRank(ctx, t)
	if len(rank) > 0 {
		for k, v := range rank {
			rankNum := k + 1
			userId := v.Member.(string)
			coinReward := coinAward[rankNum]

			// 发放金币
			if err := m.fm.Income(ctx, userId, fund.JTypeRewards, fund.PTypeDiamond, coinReward); err != nil {
				m.log.Info("超级幸运星奖励发送失败:调用加币方法错误",
					zap.String("userId", userId),
					zap.Int("rank", rankNum),
					zap.Int("coinReward", coinReward),
					zap.Int64("time", t.Unix()),
					zap.Error(err))
				continue
			}

			// 发放座驾
			var vehicleId string
			if rankNum == 1 {
				vehicleId = vehicle.Airplane1
			} else if rankNum == 2 {
				vehicleId = vehicle.Roadster2
			} else if rankNum == 3 {
				vehicleId = vehicle.Roadster1
			}
			if vehicleId != "" {
				if err := m.dm.SetVehicle(ctx, userId, vehicleId, time.Now().AddDate(0, 0, 1)); err != nil {
					return err
				}
			}

			// 发放头像框
			if err := m.dm.SetAvatarBorder(ctx, userId, superLuckyStarAvatarBorder, time.Now().AddDate(0, 0, 1)); err != nil {
				return err
			}

			if rankNum <= 10 {
				// 发放铭牌
				if err := m.dm.SetBadge(ctx, userId, superLuckyStarBadge, time.Now().AddDate(0, 0, 1)); err != nil {
					return err
				}
			}

			// 发送消息
			if err := m.im.SendSystemNoticeTextToUser(
				ctx,
				userId,
				fmt.Sprintf(
					"Parabéns! Você alcançou o %dº lugar no ranking do evento [Super Estrela]. A recompensa de %d coins já foi enviada a sua conta.",
					rankNum,
					coinReward,
				),
			); err != nil {
				m.log.Error("超级幸运星奖励私信通知失败",
					zap.Error(err),
					zap.String("userId", userId),
					zap.Int64("time", t.Unix()))
			}

			m.log.Info("超级幸运星奖励发送成功",
				zap.String("userId", userId),
				zap.Int("rank", rankNum),
				zap.Int("coinReward", coinReward),
				zap.Int64("time", t.Unix()))
		}
	}

	m.rc.SetEX(ctx, rewardKey, 1, ttlSuperLuckyStarActivityReward)
	m.log.Info("超级幸运星奖励发送完成", zap.Int64("time", t.Unix()))

	return nil
}
