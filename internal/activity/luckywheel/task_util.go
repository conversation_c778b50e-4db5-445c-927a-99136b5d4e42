package luckywheel

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/jinzhu/now"
)

type taskId string

const (
	taskWait = 0
	taskDone = 1
	taskOver = 2
)

const (
	keyTaskDone = "LUCKYWHEEL:TASK:DONE:%s:%s:%s" // date, taskId, userId
	keyTaskStep = "LUCKYWHEEL:TASK:STEP:%s:%s:%s" // date, taskId, userId
)

type taskInfo struct {
	chance   int
	stepUnit int
	dayMax   int
}

func (t taskInfo) getChance() int {
	if t.chance <= 0 {
		return 1
	}
	return t.chance
}

func (t taskInfo) getStepUnit() int {
	if t.stepUnit <= 0 {
		return 1
	}
	return t.stepUnit
}

func (t taskInfo) getDayMax() int {
	if t.dayMax <= 0 {
		return 1
	}
	return t.dayMax
}

func (t taskInfo) multiple() bool {
	return t.getDayMax()/t.getChance() > 1
}

func (m *Manager) newChance(ctx context.Context, at time.Time, userId string, taskId taskId) (int, error) {
	curr, err := m.getTaskStep(ctx, at, userId, taskId)
	if err != nil {
		return 0, err
	}
	return curr / tasks[taskId].getStepUnit(), nil
}

func (m *Manager) canExecTask(ctx context.Context, at time.Time, userId string, taskId taskId) (bool, error) {
	task := tasks[taskId]

	status, err := m.rc.Get(ctx, fmt.Sprintf(keyTaskDone, dateOf(at), taskId, userId)).Int()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			if task.multiple() {
				goto checkStep
			}
			return true, nil
		}
		return false, err
	}

	if !task.multiple() {
		return false, nil
	}

	if status == taskDone {
		return false, nil
	}

checkStep:
	if nc, _ := m.newChance(ctx, at, userId, taskId); nc < 1 {
		return false, nil
	}

	got, err := m.getTaskRecv(ctx, at, userId, taskId)
	if err != nil {
		return false, fmt.Errorf("getTaskRecv failed: %w", err)
	}
	return got < task.getDayMax(), nil
}

func (m *Manager) markTaskDone(ctx context.Context, at time.Time, userId string, taskId taskId) error {
	return m.markTaskStatus(ctx, at, userId, taskId, taskDone)
}

func (m *Manager) markTaskOver(ctx context.Context, at time.Time, userId string, taskId taskId) (int, error) {
	if err := m.markTaskStatus(ctx, at, userId, taskId, taskOver); err != nil {
		return 0, err
	}
	var (
		task   = tasks[taskId]
		chance int
	)
	if task.multiple() {
		recv, _ := m.getTaskRecv(ctx, at, userId, taskId)
		chance, _ = m.newChance(ctx, at, userId, taskId)
		if dayMax := task.getDayMax() - recv; chance > dayMax {
			chance = dayMax
		}
		if _, err := m.incTaskStep(ctx, at, userId, taskId, -chance*task.getStepUnit()); err != nil {
			return 0, err
		}
	} else {
		chance = task.getChance()
	}
	return chance, m.addChance(ctx, at, userId, chance)
}

func (m *Manager) markTaskStatus(ctx context.Context, at time.Time, userId string, taskId taskId, status int) error {
	return m.rc.Set(ctx, fmt.Sprintf(keyTaskDone, dateOf(at), taskId, userId), status, dayTTL(at)).Err()
}

func (m *Manager) incTaskStep(ctx context.Context, at time.Time, userId string, taskId taskId, amount int) (int64, error) {
	key := fmt.Sprintf(keyTaskStep, dateOf(at), taskId, userId)
	txp := m.rc.Pipeline()
	ret1 := txp.IncrBy(ctx, key, int64(amount))
	txp.Expire(ctx, key, dayTTL(at))
	if _, err := txp.Exec(ctx); err != nil {
		return 0, err
	}
	return ret1.Val(), nil
}

func (m *Manager) getTaskStep(ctx context.Context, at time.Time, userId string, taskId taskId) (int, error) {
	return m.rc.Get(ctx, fmt.Sprintf(keyTaskStep, dateOf(at), taskId, userId)).Int()
}

func dayTTL(t time.Time) time.Duration {
	t = t.In(tz)
	return now.New(t).EndOfDay().Sub(t).Truncate(time.Second) + time.Second
}
