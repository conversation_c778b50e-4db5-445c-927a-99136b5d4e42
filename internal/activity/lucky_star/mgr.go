package lucky_star

import (
	"context"
	"fmt"
	"gitlab.sskjz.com/overseas/live/osl/internal/im"
	"sort"
	"strconv"
	"time"

	"github.com/go-redis/redis/v8"
	"gitlab.sskjz.com/go/cc"
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/internal/gift"
	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"go.uber.org/zap"
)

type Manager struct {
	ug    user.Getter
	gm    *gift.Manager
	rc    *redi.Client
	fm    *fund.Manager
	im    *im.Manager
	log   *zap.Logger
	cache cc.Cache[int, string]
}

func newManager(ug user.Getter, gm *gift.Manager, rc *redi.Client, fm *fund.Manager, im *im.Manager, log *zap.Logger) *Manager {
	m := &Manager{
		ug:  ug,
		gm:  gm,
		rc:  rc,
		fm:  fm,
		im:  im,
		log: log,
	}

	m.cache = cc.New[int, string](
		32, cc.Simple,
		cc.Expiration(time.Minute),
		cc.LoaderExpireFunc(func(giftId int) (string, time.Duration, error) {
			luckyStar, err := m.getLuckyStar(giftId)
			if err != nil {
				return "", 0, err
			}

			now := time.Now().In(ctz.Brazil)
			endOfDay := time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 0, now.Location())
			secondsLeft := endOfDay.Sub(now).Seconds()

			if secondsLeft < 60 {
				return luckyStar, time.Second * time.Duration(secondsLeft), err

			}
			return luckyStar, time.Minute, err
		}),
		cc.ExportStats("lucky.star.cache"),
	)

	return m
}

var (
	startTime = time.Date(2024, 10, 14, 0, 0, 0, 0, ctz.Brazil)
	endTime   = time.Date(2024, 10, 27, 23, 59, 59, 0, ctz.Brazil)
	giftList  = []int{19, 6, 8, 9, 10, 11, 12, 14, 16, 18}
	giftAward = map[int]int{
		19: 3,
		6:  5,
		8:  8,
		9:  10,
		10: 15,
		11: 20,
		12: 25,
		14: 30,
		16: 50,
		18: 100,
	}
)

func (m *Manager) GetInfo(ctx context.Context, rankingDay, userId string) (*LuckyInfo, error) {
	var (
		res = LuckyInfo{
			StartAt:  startTime.Unix(),
			EndAt:    endTime.Unix(),
			RankInfo: make([]GiftLuckyInfo, 0, len(giftList)),
		}
		t = time.Now().In(ctz.Brazil)
	)

	if rankingDay == "yesterday" {
		t = t.AddDate(0, 0, -1)
	}

	for _, giftId := range giftList {
		giftInfo, err := m.gm.GiftById(giftId)
		if err != nil {
			return nil, err
		}
		gli := GiftLuckyInfo{
			GiftId:       giftId,
			GiftImageUrl: giftInfo.ImageUrl,
			Award:        giftAward[giftId],
		}
		rank := m.getRank(ctx, t, giftId)
		if len(rank) > 0 {
			top1 := rank[0]
			top1UserId := top1.Member.(string)
			top1Uac, _ := m.ug.Account(ctx, top1UserId)
			myTimes := m.getMyLuckyTimes(ctx, t, giftId, userId)
			gli.LuckyStar = &UserRank{
				User:       top1Uac,
				LuckyTimes: int(top1.Score),
			}
			gli.MyRanking = &UserRank{
				LuckyTimes: myTimes,
			}
		}

		res.RankInfo = append(res.RankInfo, gli)
	}

	return &res, nil
}

// 同分情况，先到者在前
func (m *Manager) getRank(ctx context.Context, t time.Time, giftId int) []redis.Z {
	var rank []redis.Z

	rankKey := fmt.Sprintf(keyLuckyStarActivityRank, t.Format("********"), giftId)
	rank, err := m.rc.ZRevRangeWithScores(ctx, rankKey, 0, 9).Result()
	if err != nil {
		return rank
	}

	var userIds []string
	for _, v := range rank {
		userIds = append(userIds, v.Member.(string))
	}

	var updateMap = map[string]int64{}
	u := m.rc.HMGet(ctx, fmt.Sprintf(keyLuckyStarUpdateTime, t.Format("********"), giftId), userIds...).Val()
	for i, v := range userIds {
		item := u[i]
		if str, ok := item.(string); ok {
			str2Int, _ := strconv.ParseInt(str, 10, 64)
			updateMap[v] = str2Int
		}
	}

	sort.Slice(rank, func(i, j int) bool {
		if rank[i].Score == rank[j].Score {
			return updateMap[rank[i].Member.(string)] < updateMap[rank[j].Member.(string)]
		}
		return rank[i].Score > rank[j].Score
	})

	return rank
}

func (m *Manager) getMyLuckyTimes(ctx context.Context, t time.Time, giftId int, userId string) int {
	var times float64
	rankKey := fmt.Sprintf(keyLuckyStarActivityRank, t.Format("********"), giftId)
	times, err := m.rc.ZScore(ctx, rankKey, userId).Result()
	if err != nil {
		return int(times)
	}

	return int(times)
}

func (m *Manager) getLuckyStar(giftId int) (string, error) {
	rank := m.getRank(context.Background(), time.Now().In(ctz.Brazil).AddDate(0, 0, -1), giftId)
	if len(rank) > 0 {
		return rank[0].Member.(string), nil
	}
	return "", nil
}

func (m *Manager) IsLuckyStar(ctx context.Context, giftId int, userId string) (bool, error) {
	luckyStar, err := m.cache.Get(giftId)
	if err != nil {
		return false, err
	}

	return luckyStar == userId, nil
}

func (m *Manager) SendReward(ctx context.Context) error {
	t := time.Now().AddDate(0, 0, -1).In(ctz.Brazil)
	m.log.Info("幸运之星奖励发送开始", zap.Int64("time", t.Unix()))
	if t.Before(startTime) || t.After(endTime) {
		m.log.Error("幸运之星奖励发送失败:不在活动时间", zap.Int64("time", t.Unix()))
		return biz.NewError(biz.ErrInvalidParam, "不在活动时间")
	}

	rewardKey := fmt.Sprintf(keyLuckyStarActivityReward, t.Format("********"))
	if m.rc.Exists(ctx, rewardKey).Val() != 0 {
		m.log.Info("幸运之星奖励发送失败:已发放完成", zap.Int64("time", t.Unix()))
		return biz.NewError(biz.ErrInvalidParam, "奖励已经发放")
	}

	// 获取榜单，发放奖励
	for _, giftId := range giftList {
		giftInfo, err := m.gm.GiftById(giftId)
		if err != nil {
			m.log.Error("幸运之星奖励发送失败:获取礼物信息失败", zap.Int64("time", t.Unix()))
			continue
		}

		rank := m.getRank(ctx, t, giftId)
		if len(rank) > 0 {
			top1 := rank[0]
			top1UserId := top1.Member.(string)
			reward := giftAward[giftId] * 10000
			if err := m.fm.Income(ctx, top1UserId, fund.JTypeRewards, fund.PTypeDiamond, reward); err != nil {
				m.log.Info("幸运之星奖励发送失败:调用加币方法错误",
					zap.Uint("giftId", giftInfo.ID),
					zap.String("giftName", giftInfo.Name),
					zap.String("userId", top1UserId),
					zap.Int("reward", reward),
					zap.Int64("time", t.Unix()),
					zap.Error(err))
				continue
			}

			// 发送消息
			if err := m.im.SendSystemNoticeTextToUser(
				ctx,
				top1UserId,
				fmt.Sprintf(
					"Parabéns, você ganhou %d coins de bônus no evento \"Estrela sortuda\", os coins já foram enviados para a sua conta!",
					reward,
				),
			); err != nil {
				m.log.Error("幸运之星奖励私信通知失败",
					zap.Error(err),
					zap.Uint("giftId", giftInfo.ID),
					zap.String("giftName", giftInfo.Name),
					zap.String("userId", top1UserId),
					zap.Int("reward", reward),
					zap.Int64("time", t.Unix()))
			}

			m.log.Info("幸运之星奖励发送成功",
				zap.Uint("giftId", giftInfo.ID),
				zap.String("giftName", giftInfo.Name),
				zap.String("userId", top1UserId),
				zap.Int("reward", reward),
				zap.Int64("time", t.Unix()))
		} else {
			m.log.Info("幸运之星奖励发送失败:无幸运之星",
				zap.Uint("giftId", giftInfo.ID),
				zap.String("giftName", giftInfo.Name),
				zap.Int64("time", t.Unix()))
		}
	}

	m.rc.SetEX(ctx, rewardKey, 1, ttlLuckyStarActivityReward)
	m.log.Info("幸运之星奖励发送完成", zap.Int64("time", t.Unix()))

	return nil
}
