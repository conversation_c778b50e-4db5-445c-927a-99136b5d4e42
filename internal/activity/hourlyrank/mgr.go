package hourlyrank

import (
	"context"
	"fmt"
	"github.com/samber/lo"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/internal/live"
	"gitlab.sskjz.com/overseas/live/osl/internal/ranklist"
	"time"

	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/im"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"go.uber.org/zap"
)

type Manager struct {
	fm  *fund.Manager
	ug  user.Getter
	imm *im.Manager
	lm  *live.Manager
	rm  *ranklist.Manager
	rc  *redi.Client
	log *zap.Logger
}

func newManager(fm *fund.Manager, ug user.Getter, rc *redi.Client, imm *im.Manager, lm *live.Manager, rm *ranklist.Manager, log *zap.Logger) *Manager {
	m := &Manager{
		fm:  fm,
		ug:  ug,
		imm: imm,
		lm:  lm,
		rm:  rm,
		rc:  rc,
		log: log,
	}

	return m
}

type StartEndAndStage struct {
	StartTime  time.Time
	EndTime    time.Time
	Period     []int
	Stage      string
	Connection bool
}

var (
	rewardMap = map[int]int{
		1: 50000,
		2: 20000,
		3: 10000,
	}
	startEndTime = []StartEndAndStage{
		{
			StartTime: time.Date(2025, 04, 14, 0, 0, 0, 0, ctz.Brazil),
			EndTime:   time.Date(2025, 04, 20, 23, 59, 59, 0, ctz.Brazil),
			Period: []int{
				0, 19, 20, 21, 22, 23,
			},
			Stage:      "20250414",
			Connection: false,
		},
		{
			StartTime: time.Date(2025, 04, 21, 0, 0, 0, 0, ctz.Brazil),
			EndTime:   time.Date(2025, 04, 27, 23, 59, 59, 0, ctz.Brazil),
			Period: []int{
				0, 19, 20, 21, 22, 23,
			},
			Stage:      "20250421",
			Connection: true,
		},
		{
			StartTime: time.Date(2025, 04, 28, 0, 0, 0, 0, ctz.Brazil),
			EndTime:   time.Date(2025, 05, 04, 23, 59, 59, 0, ctz.Brazil),
			Period: []int{
				0, 19, 20, 21, 22, 23,
			},
			Stage:      "20250428",
			Connection: true,
		},
		{
			StartTime: time.Date(2025, 05, 05, 0, 0, 0, 0, ctz.Brazil),
			EndTime:   time.Date(2025, 05, 11, 23, 59, 59, 0, ctz.Brazil),
			Period: []int{
				0, 19, 20, 21, 22, 23,
			},
			Stage:      "20250505",
			Connection: true,
		},
		{
			StartTime: time.Date(2025, 05, 12, 0, 0, 0, 0, ctz.Brazil),
			EndTime:   time.Date(2025, 05, 18, 23, 59, 59, 0, ctz.Brazil),
			Period: []int{
				0, 19, 20, 21, 22, 23,
			},
			Stage:      "20250512",
			Connection: true,
		},
		{
			StartTime: time.Date(2025, 05, 19, 0, 0, 0, 0, ctz.Brazil),
			EndTime:   time.Date(2025, 05, 25, 23, 59, 59, 0, ctz.Brazil),
			Period: []int{
				0, 19, 20, 21, 22, 23,
			},
			Stage:      "20250519",
			Connection: true,
		},
		{
			StartTime: time.Date(2025, 05, 26, 0, 0, 0, 0, ctz.Brazil),
			EndTime:   time.Date(2025, 06, 01, 23, 59, 59, 0, ctz.Brazil),
			Period: []int{
				0, 19, 20, 21, 22, 23,
			},
			Stage:      "20250526",
			Connection: true,
		},
		{
			StartTime: time.Date(2025, 06, 02, 0, 0, 0, 0, ctz.Brazil),
			EndTime:   time.Date(2025, 06, 8, 23, 59, 59, 0, ctz.Brazil),
			Period: []int{
				0, 19, 20, 21, 22, 23,
			},
			Stage:      "20250602",
			Connection: true,
		},
	}
)

func (m *Manager) GetSES(t time.Time) StartEndAndStage {
	t = t.In(ctz.Brazil)
	for _, ses := range startEndTime {
		if t.After(ses.StartTime) && t.Before(ses.EndTime) {
			return ses
		}
	}
	return StartEndAndStage{}
}

// InPeriod 是否在榜单期间
func (m *Manager) InPeriod(t time.Time) bool {
	t = t.In(ctz.Brazil)
	for _, ses := range startEndTime {
		if t.After(ses.StartTime) && t.Before(ses.EndTime) {
			if ses.Connection == false &&
				t.Month() == ses.StartTime.Month() &&
				t.Day() == ses.StartTime.Day() &&
				t.Hour() == 0 {
				// 排除第一天的0点
				return false
			}
			return lo.Contains(ses.Period, t.Hour())
		}
	}
	return false
}

// GetRewardNum 获取奖励金额
func (m *Manager) GetRewardNum(rank int) int {
	return rewardMap[rank]
}

// RankingCurrent 当前时段榜单
func (m *Manager) RankingCurrent(ctx context.Context, hostUserId string) (*UserRank, []UserRank, error) {
	var (
		now      = time.Now().In(ctz.Brazil)
		hostRank *UserRank
		top3     []UserRank
	)
	// 每个时段（小时）
	hourlyRankList := m.rm.GetHourRank(ctx, now)
	for k, v := range hourlyRankList {
		if k+1 > 3 {
			continue
		}
		userId := v.Member.(string)
		uac, err := m.ug.Account(ctx, userId)
		if err != nil {
			return nil, nil, err
		}
		ur := UserRank{
			Rank:   k + 1,
			Value:  int64(v.Score),
			User:   uac,
			Reward: m.GetRewardNum(k + 1),
		}
		top3 = append(top3, ur)
	}

	if hostUserId != "" {
		hostUac, err := m.ug.Account(ctx, hostUserId)
		if err == nil {
			rank, score, gap := m.rm.GetHourlyRankAndGap(ctx, hostUserId)
			hostRank = &UserRank{
				Rank:   rank,
				Value:  score,
				Gap:    gap,
				Reward: m.GetRewardNum(rank),
				User:   hostUac,
			}
		}
	}

	return hostRank, top3, nil
}

// RankingHistory 历史榜单
func (m *Manager) RankingHistory(ctx context.Context) ([]DayRank, error) {
	var (
		history = make([]DayRank, 0)
		now     = time.Now().In(ctz.Brazil)
	)

	// 昨天是否是活动时间
	yesterdaySes := m.GetSES(now.AddDate(0, 0, -1))

	// 今天是否是活动时间
	todaySes := m.GetSES(now)

	if todaySes.Stage != "" {
		// 今天是活动时间
		// 从本阶段活动第一天开始遍历
		t1 := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 1, ctz.Brazil)
		for t1.After(todaySes.StartTime) {
			// 每天
			var dr DayRank
			dr.Day = t1
			for _, v := range todaySes.Period {
				var (
					periodT = t1.Add(time.Duration(v) * time.Hour)
					top3    []UserRank
				)
				// 排除第一天的0点
				if todaySes.Connection == false &&
					periodT.Month() == todaySes.StartTime.Month() &&
					periodT.Day() == todaySes.StartTime.Day() &&
					periodT.Hour() == 0 {
					continue
				}
				// 过滤current榜单
				if periodT.Add(time.Hour).After(now) {
					continue
				}
				// 每个时段（小时）
				hourlyRankList := m.rm.GetHourRank(ctx, periodT)
				for k, v := range hourlyRankList {
					if k+1 > 3 {
						continue
					}
					userId := v.Member.(string)
					uac, err := m.ug.Account(ctx, userId)
					if err != nil {
						return nil, err
					}
					top3 = append(top3, UserRank{
						Rank:    k + 1,
						Value:   int64(v.Score),
						User:    uac,
						Reward:  m.GetRewardNum(k + 1),
						IsValid: m.rewardIsValid(ctx, userId, periodT),
					})
				}
				dr.PeriodRank = append(dr.PeriodRank, PeriodRank{
					StartTime: periodT,
					EndTime:   periodT.Add(time.Hour),
					Top3:      top3,
				})
			}

			history = append(history, dr)
			t1 = t1.AddDate(0, 0, -1)
		}
	}

	// 跨周期活动
	if yesterdaySes.Stage != "" && yesterdaySes.Stage != todaySes.Stage {
		// 获取昨天的历史榜单
		t := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, ctz.Brazil).AddDate(0, 0, -1)
		var dr DayRank
		dr.Day = t
		for _, v := range yesterdaySes.Period {
			var (
				periodT = t.Add(time.Duration(v) * time.Hour)
				top3    []UserRank
			)
			// 周期结束时间在当前之后过滤
			if periodT.Add(time.Hour).After(now) {
				continue
			}
			// 每个时段（小时）
			hourlyRankList := m.rm.GetHourRank(ctx, periodT)
			for k, v := range hourlyRankList {
				if k+1 > 3 {
					continue
				}
				userId := v.Member.(string)
				uac, err := m.ug.Account(ctx, userId)
				if err != nil {
					return nil, err
				}
				top3 = append(top3, UserRank{
					Rank:    k + 1,
					Value:   int64(v.Score),
					User:    uac,
					Reward:  m.GetRewardNum(k + 1),
					IsValid: m.rewardIsValid(ctx, userId, periodT),
				})
			}
			dr.PeriodRank = append(dr.PeriodRank, PeriodRank{
				StartTime: periodT,
				EndTime:   periodT.Add(time.Hour),
				Top3:      top3,
			})
		}

		history = append(history, dr)
	}

	return history, nil
}

func (m *Manager) rewardIsValid(ctx context.Context, userId string, t time.Time) bool {
	if m.rc.Exists(ctx, fmt.Sprintf(keyHourlyRankActivityRewardValid, t.Format("**********"), userId)).Val() != 0 {
		return false
	}
	return true
}

func (m *Manager) SendReward(ctx context.Context, t time.Time) error {
	if !m.InPeriod(t) {
		m.log.Info("小时榜活动发奖:对应时间没有榜单场次", zap.Time("at", t))
		return nil
	}

	m.log.Info("小时榜活动发奖:开始发奖", zap.Time("at", t))

	hour := time.Date(t.Year(), t.Month(), t.Day(), t.Hour(), 0, 0, 0, ctz.Brazil)
	// 查找对应场次前三
	ranks := m.rm.GetHourRank(ctx, hour)
	if len(ranks) == 0 {
		m.log.Info("小时榜活动发奖:无需发奖", zap.Time("at", t))
		return nil
	}

	for k, v := range ranks {
		if k > 2 {
			break
		}

		// 发放钻石
		award := m.GetRewardNum(k + 1)
		if award == 0 {
			continue
		}

		userId := v.Member.(string)
		// 判断用户开播时间
		liveValidDuration, err := m.lm.GetLiveValidDuration(ctx, userId, hour, hour.Add(time.Hour))
		if err != nil {
			m.log.Error("小时榜活动发奖:获取开播时间错误",
				zap.Error(err),
				zap.Int("rank", k+1),
				zap.String("userId", userId),
				zap.Time("at", t),
			)
			continue
		}

		// 直播小于15分钟
		if liveValidDuration < 15*60 {
			// redis记录
			m.rc.SetEX(ctx, fmt.Sprintf(keyHourlyRankActivityRewardValid, hour.Format("**********"), userId), 1, ttlHourlyRankActivityRewardValid)
			// 私信消息
			if err := m.imm.SendSystemNoticeTextToUser(
				ctx,
				userId,
				fmt.Sprintf(
					"Você ficou em %dº lugar das %sh %s/%s no evento [Batalha de horas]. Entretanto, devido à transmissão ao vivo ter durado menos de 15 minutos durante essa hora, não foi possível conceder a recompensa em cristais.",
					k+1,
					t.Format("15"),
					t.Format("02"),
					t.Format("01"),
				),
			); err != nil {
				m.log.Error("小时榜活动发奖私信通知失败",
					zap.Error(err),
					zap.Int("award", award),
					zap.String("userId", userId),
					zap.Int64("time", t.Unix()))
			}

			m.log.Info("小时榜活动发奖:开播时间不达标",
				zap.Int("rank", k+1),
				zap.String("userId", userId),
				zap.Int("award", award),
				zap.Int64("liveValidDuration", liveValidDuration),
				zap.Time("at", t),
			)
			continue
		}

		// 发放水晶
		if err := m.fm.Income(ctx, userId, fund.JTypeRewards, fund.PTypeFruits, award); err != nil {
			m.log.Error("小时榜活动发奖:发放水晶失败",
				zap.Error(err),
				zap.Int("rank", k+1),
				zap.String("userId", userId),
				zap.Int("award", award),
			)
			return err
		}

		// 私信消息
		if err := m.imm.SendSystemNoticeTextToUser(
			ctx,
			userId,
			fmt.Sprintf(
				"Parabéns! Você ficou em %dº lugar das %sh %s/%s no evento [Batalha de horas]. A recompensa de %d cristais foi enviada à sua conta.",
				k+1,
				t.Format("15"),
				t.Format("02"),
				t.Format("01"),
				award,
			),
		); err != nil {
			m.log.Error("小时榜活动发奖私信通知失败",
				zap.Error(err),
				zap.Int("award", award),
				zap.String("userId", userId),
				zap.Int64("time", t.Unix()))
		}

		m.log.Info("小时榜活动发奖:发奖成功",
			zap.Int("rank", k+1),
			zap.String("userId", userId),
			zap.Int("award", award),
			zap.Int64("liveValidDuration", liveValidDuration),
			zap.Time("at", t),
		)
	}

	m.log.Info("小时榜活动发奖:发奖完成", zap.Time("at", t))

	return nil
}
