package launch

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/jinzhu/now"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

var (
	keyRank = "ZSET:ACT:LAUNCH:%s"
	ttlRank = time.Hour * 24 * 15

	keyRankReceive = "STR:ACT:L:RANK:R:%s:%s" // userId:day
	ttlRankReceive = time.Hour * 24 * 7
)

var (
	// 奖励
	awards = map[int]int64{
		1:  1000000,
		2:  500000,
		3:  300000,
		4:  100000,
		5:  100000,
		6:  100000,
		7:  100000,
		8:  100000,
		9:  100000,
		10: 100000,
	}
)

type Rank struct {
	No      int
	UserId  string
	Diamond int64
	Award   int64
}

func (m *Manager) getTodayRankKey(nn *now.Now) string {
	return fmt.Sprintf(keyRank, nn.BeginningOfDay().Format("20060102"))
}

func (m *Manager) getYesterdayRankKey(nn *now.Now) string {
	return fmt.Sprintf(keyRank, nn.AddDate(0, 0, -1).Format("20060102"))
}

const (
	luaScript = `
	local key = KEYS[1]
	local userId = ARGV[1]
	local increment = tonumber(ARGV[2])
	local timeFactor = tonumber(ARGV[3])
	local ttl = tonumber(ARGV[4])

	local currentScore = redis.call("ZSCORE", key, userId)
	if not currentScore then
		currentScore = 0
	else
		currentScore = tonumber(currentScore)
	end

	local newScore = math.floor(currentScore) + increment + timeFactor
	redis.call("ZADD", key, newScore, userId)
	redis.call("EXPIRE", key, ttl)
	return newScore
`
)

var ls = redis.NewScript(luaScript)

func (m *Manager) incrDiamond(ctx context.Context, nn *now.Now, userId string, diamond int64) error {
	key := m.getTodayRankKey(nn)

	leftSeconds, err := strconv.ParseFloat(fmt.Sprintf("0.%d", int(nn.EndOfDay().Sub(nn.Time).Seconds())), 64)

	if err != nil {
		return err
	}

	_, err = m.rc.EvalSha(ctx, ls.Hash(), []string{key}, userId, diamond, leftSeconds, ttlRank).Result()

	if err != nil {
		return err
	}

	return nil
}

// 获取排名
func (m *Manager) getRank(ctx context.Context, key string) ([]Rank, error) {
	res, err := m.rc.ZRevRangeWithScores(ctx, key, 0, 9).Result()

	if err != nil {
		return nil, err
	}

	rank := make([]Rank, len(res))

	for i, v := range res {
		rank[i] = Rank{
			No:      i + 1,
			UserId:  v.Member.(string),
			Diamond: int64(v.Score),
		}
	}

	return rank, nil
}

// 获取某个用户的排名和奖励
func (m *Manager) getUserRank(ctx context.Context, key, userId string) (*Rank, error) {
	score, err := m.rc.ZScore(ctx, key, userId).Result()

	if err != nil {
		if err != redis.Nil {
			return &Rank{}, err
		}
	}

	no, err := m.rc.ZRevRank(ctx, key, userId).Result()

	if err != nil {
		if err != redis.Nil {
			return &Rank{}, err
		}

		no = 100
	}

	rank := &Rank{
		No:      int(no) + 1,
		UserId:  userId,
		Diamond: int64(score),
		Award:   0,
	}

	if rank.No <= 10 {
		rank.Award = awards[rank.No]
	}

	return rank, nil
}

func (m *Manager) GetTodayRank(ctx context.Context, nn *now.Now) ([]Rank, error) {
	return m.getRank(ctx, m.getTodayRankKey(nn))
}

func (m *Manager) GetYesterdayRank(ctx context.Context, nn *now.Now) ([]Rank, error) {
	return m.getRank(ctx, m.getYesterdayRankKey(nn))
}

func (m *Manager) GetUserTodayRank(ctx context.Context, nn *now.Now, userId string) (*Rank, error) {
	return m.getUserRank(ctx, m.getTodayRankKey(nn), userId)
}

func (m *Manager) GetUserYesterdayRank(ctx context.Context, nn *now.Now, userId string) (*Rank, error) {
	return m.getUserRank(ctx, m.getYesterdayRankKey(nn), userId)
}

func (m *Manager) GetRankReceiveStatus(ctx context.Context, userId string, nn *now.Now) int {
	if m.rc.Exists(ctx, m.getRankReceiveKey(userId, nn.AddDate(0, 0, -1).Format("20060102"))).Val() == 1 {
		return StatusReceiveFinish
	}

	return StatusReceiveAccept
}

// 领取排名奖励
func (m *Manager) ReceiveRankAward(ctx context.Context, userId string, nn *now.Now) (int64, error) {
	if !IsInActivityLaunchPeriod(nn.AddDate(0, 0, -1)) {
		return 0, biz.NewError(biz.ErrBusiness, "not in the activity period")
	}

	rank, err := m.getUserRank(ctx, m.getYesterdayRankKey(nn), userId)

	if err != nil {
		m.log.Error(
			"领取送礼活动奖励失败",
			zap.Error(err),
			zap.String("userId", userId),
			zap.String("award", "rank"),
		)

		return 0, err
	}

	diamond := rank.Award

	if diamond == 0 {
		return 0, biz.NewError(biz.ErrBusiness, "You are not eligible for the rank award")
	}

	// 昨日是否已领取
	key := m.getRankReceiveKey(userId, nn.AddDate(0, 0, -1).Format("20060102"))

	if !m.rc.SetNX(ctx, key, 1, ttlRankReceive).Val() {
		return 0, biz.NewError(biz.ErrBusiness, "You have received the rank award")
	}

	tradeId := primitive.NewObjectID().Hex()

	err = m.fm.Income(
		ctx,
		userId,
		fund.JTypeRecharge,
		fund.PTypeDiamond,
		diamond,
		fund.WithTrade(tradeId),
		fund.WithTime(nn.Time),
	)

	if err != nil {
		m.log.Error(
			"领取送礼活动奖励失败",
			zap.Error(err),
			zap.String("userId", userId),
			zap.Int64("diamond", diamond),
			zap.String("tradeId", tradeId),
			zap.String("award", "rank"),
		)

		return 0, err
	}

	m.log.Info(
		"领取送礼活动奖励成功",
		zap.String("userId", userId),
		zap.Int64("diamond", diamond),
		zap.String("tradeId", tradeId),
		zap.String("award", "rank"),
	)

	return diamond, nil
}

func (m *Manager) getRankReceiveKey(userId string, day string) string {
	return fmt.Sprintf(keyRankReceive, userId, day)
}
