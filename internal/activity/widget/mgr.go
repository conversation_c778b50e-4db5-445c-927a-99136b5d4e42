package widget

import (
	"context"
	"github.com/bytedance/sonic"
	"github.com/samber/lo"
	"gitlab.sskjz.com/overseas/live/osl/internal/activity/blindbox_collect"
	"gitlab.sskjz.com/overseas/live/osl/internal/activity/coin_grab"
	"gitlab.sskjz.com/overseas/live/osl/internal/activity/hourlyrank"
	"gitlab.sskjz.com/overseas/live/osl/internal/activity/rebate"
	"gitlab.sskjz.com/overseas/live/osl/internal/activity/super_lucky_star"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/mixer"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"go.uber.org/zap"
	"time"
)

type Manager struct {
	hrm  *hourlyrank.Manager
	rm   *rebate.Manager
	cgm  *coin_grab.Manager
	bcm  *blindbox_collect.Manager
	slsm *super_lucky_star.Manager
	log  *zap.Logger
}

func newManager(hrm *hourlyrank.Manager, rm *rebate.Manager, cgm *coin_grab.Manager, bcm *blindbox_collect.Manager, slsm *super_lucky_star.Manager, log *zap.Logger) *Manager {
	m := &Manager{
		hrm:  hrm,
		rm:   rm,
		cgm:  cgm,
		bcm:  bcm,
		slsm: slsm,
		log:  log,
	}

	return m
}

func (m Manager) Poll(ctx context.Context, widgetId, anchorUserId string, uac *user.Account) (string, error) {
	var data string
	if uac == nil {
		return data, nil
	}

	// /api/v1/activity/hourlyrank/current接口数据
	if widgetId == "2025-hourlyrank-activity" {
		var (
			ranks                                  []hourlyRankUser
			startTime, endTime                     int64
			periodRemainSeconds, periodWaitSeconds int64
			now                                    = time.Now().In(ctz.Brazil)
		)
		ses := m.hrm.GetSES(now)
		if ses.Stage == "" {
			ses = m.hrm.GetSES(now.AddDate(0, 0, -1))
		}
		if ses.Stage != "" {
			startTime = ses.StartTime.Unix()
			endTime = ses.EndTime.Unix()
			// period status计算
			if ses.EndTime.After(now) {
				today := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, ctz.Brazil)
				for _, p := range ses.Period {
					pts := today.Add(time.Duration(p) * time.Hour)
					pte := today.Add(time.Duration(p+1) * time.Hour)
					if pte.Before(now) {
						continue
					}
					if ses.Connection == false &&
						pts.Month() == ses.StartTime.Month() &&
						pts.Day() == ses.StartTime.Day() &&
						pts.Hour() == 0 {
						continue
					}
					if pts.Before(now) && pte.After(now) {
						periodRemainSeconds = now.Truncate(time.Hour).Add(time.Hour).Unix()
						break
					}
					if pts.After(now) {
						periodWaitSeconds = pts.Unix()
						break
					}
				}
			}
		}

		if m.hrm.InPeriod(now) {
			_, rankList, err := m.hrm.RankingCurrent(ctx, anchorUserId)
			if err != nil {
				return data, err
			}

			ranks = make([]hourlyRankUser, 0, 3)
			for _, v := range rankList {
				if len(ranks) >= 3 {
					continue
				}
				ranks = append(ranks, hourlyRankUser{
					Rank:   v.Rank,
					Value:  v.Value,
					Reward: v.Reward,
					User:   mixer.User(ctx, v.User),
				})
			}
		}
		widgetData := hourlyRankWidget{
			StartTime:           startTime,
			EndTime:             endTime,
			PeriodStatus:        lo.Ternary(periodRemainSeconds > 0, 1, 0),
			PeriodWaitSeconds:   periodWaitSeconds,
			PeriodRemainSeconds: periodRemainSeconds,
			Ranks:               ranks,
		}
		data, err := sonic.Marshal(widgetData)
		return string(data), err
	}

	// activity/rebate/ranking
	if widgetId == "2025-rebate-activity" {
		t := time.Now().In(ctz.Brazil)
		var myRank rebateRankingUser
		userRank, _, err := m.rm.GetRanking(ctx, t, uac.UserId)
		if err != nil {
			return "", err
		}

		myRank = rebateRankingUser{
			Rank:  userRank.Rank,
			Value: userRank.Value,
		}

		widgetData := rebateWidget{
			MyRank: myRank,
		}
		data, err := sonic.Marshal(widgetData)
		return string(data), err
	}

	// /api/v1/activity/coingrab/widget
	if widgetId == "2025-coingrab-activity" {
		myRank, coinPoll := m.cgm.GetRoomWidget(ctx, uac.UserId)
		widgetData := coinGrabWidget{
			CoinPool: coinPoll,
			MyRank:   myRank,
		}
		data, err := sonic.Marshal(widgetData)
		return string(data), err
	}

	if widgetId == "2025-gift-wall" {
		p := m.bcm.GetSES(time.Now())
		widgetData := blindBoxCollectWidgetResp{
			CollectData: m.bcm.GetUserCollectData(ctx, p.Stage, anchorUserId),
		}
		data, err := sonic.Marshal(widgetData)
		return string(data), err
	}

	if widgetId == "2025-super-lucky-star" {
		p, err := m.slsm.GetWidget(ctx)
		if err != nil {
			return "", err
		}
		var top3 []superLuckyStarUserRank
		for _, v := range p.RankInfo {
			top3 = append(top3, superLuckyStarUserRank{
				Rank:  v.Rank,
				Value: v.Value,
				User:  mixer.User(ctx, v.User),
			})
		}
		widgetData := superLuckyStarWidgetResp{
			RankInfo: top3,
		}
		data, err := sonic.Marshal(widgetData)
		return string(data), err
	}

	return data, nil
}
