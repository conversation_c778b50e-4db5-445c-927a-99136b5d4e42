package valentines_day

import (
	"context"
	"fmt"
	"sort"
	"strconv"
	"time"

	"github.com/go-redis/redis/v8"
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/im"
	"gitlab.sskjz.com/overseas/live/osl/internal/props"
	"gitlab.sskjz.com/overseas/live/osl/internal/props/propc"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"go.uber.org/zap"
)

type Manager struct {
	pm  *props.Manager
	ug  user.Getter
	imm *im.Manager
	rc  *redi.Client
	log *zap.Logger
}

func newManager(ug user.Getter, rc *redi.Client, pm *props.Manager, imm *im.Manager, log *zap.Logger) *Manager {
	m := &Manager{
		pm:  pm,
		ug:  ug,
		imm: imm,
		rc:  rc,
		log: log,
	}

	return m
}

var (
	startTime = time.Date(2025, 02, 12, 0, 0, 0, 0, ctz.Brazil)
	endTime   = time.Date(2025, 02, 14, 23, 59, 59, 0, ctz.Brazil)
)

// InPeriod 是否在榜单期间，活动时间内每天的18:00 ~ 24:00 每个小时一个时段
func (m *Manager) InPeriod(t time.Time) bool {
	t = t.In(ctz.Brazil)
	if t.After(startTime) && t.Before(endTime) {
		if t.Hour() >= 18 && t.Hour() <= 23 {
			return true
		}
	}

	return false
}

// RankingHistory 历史榜单
func (m *Manager) RankingHistory() (PeriodRank, []PeriodRank) {
	var (
		lastPeriodTop3  PeriodRank
		otherPeriodTop3 []PeriodRank
		t               = time.Now().In(ctz.Brazil)
	)

	// 上时段
	var lastPeriodT time.Time
	lct := t.Add(-1 * time.Hour)
	for !lct.Before(startTime) {
		if m.InPeriod(lct) {
			lastPeriodT = lct
			break
		}
		lct = lct.Add(-1 * time.Hour)
	}

	if !lastPeriodT.IsZero() {
		lastPeriodTop10 := m.getRank(context.Background(), lastPeriodT)
		if len(lastPeriodTop10) > 0 {
			for k, v := range lastPeriodTop10 {
				if k > 2 {
					break
				}
				userId := v.Member.(string)
				userUac, _ := m.ug.Account(context.Background(), userId)
				lastPeriodTop3.Top3 = append(lastPeriodTop3.Top3, UserRank{
					Rank:  k + 1,
					Value: int(v.Score),
					User:  userUac,
				})
			}
		}
		lastPeriodTop3.StartTime = lastPeriodT.Truncate(time.Hour)
		lastPeriodTop3.EndTime = lastPeriodTop3.StartTime.Add(time.Hour)
	}

	// 所有,时间倒序
	ct := t.Add(-time.Hour).In(ctz.Brazil)
	for !ct.Before(startTime) && ct.Before(t.Truncate(time.Hour)) {
		if m.InPeriod(ct) {
			var otherPeriod PeriodRank
			otherPeriod.StartTime = ct.Truncate(time.Hour)
			otherPeriod.EndTime = otherPeriod.StartTime.Add(time.Hour)
			ctTop10 := m.getRank(context.Background(), ct)
			if len(ctTop10) > 0 {
				for k, v := range ctTop10 {
					if k > 2 {
						break
					}
					userId := v.Member.(string)
					userUac, _ := m.ug.Account(context.Background(), userId)
					otherPeriod.Top3 = append(otherPeriod.Top3, UserRank{
						Rank:  k + 1,
						Value: int(v.Score),
						User:  userUac,
					})
				}
			}
			otherPeriodTop3 = append(otherPeriodTop3, otherPeriod)
		}
		ct = ct.Add(-time.Hour)
	}

	return lastPeriodTop3, otherPeriodTop3
}

// RankingCurrent 当前榜单
func (m *Manager) RankingCurrent(ctx context.Context, userId string) (*UserRank, []UserRank, int64, error) {
	var (
		currentT            time.Time
		hostRank            *UserRank
		rankList            []UserRank
		periodRemainSeconds int64
	)
	t := time.Now().In(ctz.Brazil)
	if !m.InPeriod(t) {
		// 上时段
		lct := t.Add(-1 * time.Hour)
		for !lct.Before(startTime) {
			if m.InPeriod(lct) {
				currentT = lct
				periodRemainSeconds = 0
				break
			}
			lct = lct.Add(-1 * time.Hour)
		}
	} else {
		currentT = t
		periodRemainSeconds = int64(currentT.Truncate(time.Hour).Add(time.Hour).Sub(time.Now()).Seconds())
	}

	ranks := m.getRank(context.Background(), currentT)
	for k, r := range ranks {
		anchorId := r.Member.(string)

		uac, err := m.ug.Account(ctx, anchorId)
		if err != nil {
			return nil, nil, 0, err
		}

		userRank := UserRank{
			Rank:  k + 1,
			Value: int(r.Score),
			User:  uac,
		}

		if anchorId == userId {
			hostRank = &userRank
		}

		rankList = append(rankList, userRank)

	}

	if userId != "" && hostRank == nil {
		uac, err := m.ug.Account(ctx, userId)
		if err != nil {
			return nil, nil, 0, err
		}

		rank, value := m.getHostRank(ctx, currentT, userId)
		hostRank = &UserRank{
			Rank:  rank,
			Value: value,
			User:  uac,
		}
	}

	return hostRank, rankList, periodRemainSeconds, nil
}

func (m *Manager) GetPeriodSeconds() (int64, int64) {
	var periodRemainSeconds, rankStartSeconds int64
	t := time.Now().In(ctz.Brazil)
	if !m.InPeriod(t) {
		// 找下一个时段时段
		lct := t.Add(1 * time.Hour)
		for !lct.After(endTime) {
			if m.InPeriod(lct) {
				rankStartSeconds = int64(lct.Truncate(time.Hour).Sub(time.Now()).Seconds())
				break
			}
			lct = lct.Add(1 * time.Hour)
		}
	} else {
		periodRemainSeconds = int64(t.Truncate(time.Hour).Add(time.Hour).Sub(time.Now()).Seconds())
	}

	return periodRemainSeconds, rankStartSeconds
}

// 同分情况，先到者在前
func (m *Manager) getRank(ctx context.Context, t time.Time) []redis.Z {
	var rank []redis.Z

	rankKey := fmt.Sprintf(keyValentinesActivityRank, t.Format("2006010215"))
	rank, err := m.rc.ZRevRangeWithScores(ctx, rankKey, 0, 9).Result()
	if err != nil {
		return rank
	}

	var userIds []string
	for _, v := range rank {
		userIds = append(userIds, v.Member.(string))
	}

	var updateMap = map[string]int64{}
	u := m.rc.HMGet(ctx, fmt.Sprintf(keyValentinesUpdateTime, t.Format("2006010215")), userIds...).Val()
	for i, v := range userIds {
		item := u[i]
		if str, ok := item.(string); ok {
			str2Int, _ := strconv.ParseInt(str, 10, 64)
			updateMap[v] = str2Int
		}
	}

	sort.Slice(rank, func(i, j int) bool {
		if rank[i].Score == rank[j].Score {
			return updateMap[rank[i].Member.(string)] < updateMap[rank[j].Member.(string)]
		}
		return rank[i].Score > rank[j].Score
	})

	return rank
}

func (m *Manager) getHostRank(ctx context.Context, t time.Time, userId string) (int, int) {
	rankKey := fmt.Sprintf(keyValentinesActivityRank, t.Format("2006010215"))
	value, err := m.rc.ZScore(ctx, rankKey, userId).Result()
	if err != nil {
		return 0, 0
	}

	rank, err := m.rc.ZRevRank(ctx, rankKey, userId).Result()
	if err != nil {
		return 0, 0
	}
	return int(rank) + 1, int(value)
}

func (m *Manager) SendReward(ctx context.Context, t time.Time) error {
	if !m.InPeriod(t) {
		m.log.Info("情人节活动发奖:对应时间没有榜单场次", zap.Time("at", t))
		return nil
	}

	// 查找对应场次前三
	ranks := m.getRank(ctx, t)
	if len(ranks) == 0 {
		m.log.Info("情人节活动发奖:无需发奖", zap.Time("at", t))
		return nil
	}

	for k, v := range ranks {
		if k > 2 {
			break
		}
		var winGiftId int
		if k == 0 {
			winGiftId = 10047
		} else if k == 1 {
			winGiftId = 10046
		} else {
			winGiftId = 10045
		}

		userId := v.Member.(string)
		if err := m.pm.AddItem(ctx, time.Now(),
			userId, propc.GPropId(winGiftId), 1,
		); err != nil {
			m.log.Warn("发放礼物失败", zap.Error(err))
		}

		// 私信消息
		if err := m.imm.SendSystemNoticeTextToUser(
			ctx,
			userId,
			fmt.Sprintf(
				"[Notificação do evento]Parabéns por ter ficado em TOP%d em %s %s~%s. A recompensa foi enviada para sua mochila e é válida por 7 dias.",
				k+1,
				t.Format("01-02"),
				t.Format("15:00"),
				t.Add(time.Hour).Format("15:00"),
			),
		); err != nil {
			m.log.Error("情人节活动发奖私信通知失败",
				zap.Error(err),
				zap.Int("giftId", winGiftId),
				zap.String("userId", userId),
				zap.Int64("time", t.Unix()))
		}

		m.log.Info("情人节活动发奖成功", zap.Time("at", t), zap.String("userId", userId))
	}

	m.log.Info("情人节活动发奖完成", zap.Time("at", t))

	return nil
}
