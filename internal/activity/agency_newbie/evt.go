package agency_newbie

import (
	"context"
	"errors"
	"gitlab.sskjz.com/overseas/live/osl/internal/anchor"
	"gitlab.sskjz.com/overseas/live/osl/internal/evt"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/zap"
	"time"
)

func (m *Manager) onAgencyJoinVerify(
	ctx context.Context,
	evd *evt.AgencyMemberJoinEvt,
) error {
	// 判断是否是新主播
	evaluationInfo, err := m.acm.GetEvaluation(ctx, evd.UserId)
	if err != nil && !errors.Is(err, anchor.ErrEvaluationNotFound) {
		return err
	}

	if evaluationInfo != nil && evaluationInfo.Status == anchor.AnchorEvaluationStatusPass {
		return nil
	}

	// 获取阶段
	var stageId string
	for _, stageInfo := range m.GetActivityStageInfo() {
		if time.Now().In(ctz.Brazil).After(stageInfo.StartTime) && time.Now().In(ctz.Brazil).Before(stageInfo.EndTime) {
			stageId = stageInfo.StageId
			break
		}
	}

	if len(stageId) == 0 {
		return nil
	}

	// 查询是否已经存在
	var record AgencyNewbieAnchor
	if err := m.dbmc.Collection(AgencyNewbieAnchorCollectionName()).FindOne(ctx, bson.M{
		"stageId":  stageId,
		"agencyId": evd.AgencyId,
		"userId":   evd.UserId,
	}, options.FindOne()).Decode(&record); err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return err
	}

	// 写入记录
	if _, err := m.dbmc.Collection(AgencyNewbieAnchorCollectionName()).InsertOne(ctx, bson.M{
		"stageId":  stageId,
		"agencyId": evd.AgencyId,
		"userId":   evd.UserId,
		"status":   NewbieAnchorStatusNoVerify,
		"updateAt": time.Now(),
		"createAt": time.Now(),
	}); err != nil {
		return err
	}

	m.log.Info("公会拉新活动邀请新主播", zap.String("stage", stageId), zap.Int("agencyId", evd.AgencyId), zap.String("userId", evd.UserId))

	return nil
}

func (m *Manager) onAgencyQuitVerify(
	ctx context.Context,
	evd *evt.AgencyMemberQuitEvt,
) error {
	// 获取阶段
	var stageId string
	for _, stageInfo := range m.GetActivityStageInfo() {
		if time.Now().In(ctz.Brazil).After(stageInfo.StartTime) && time.Now().In(ctz.Brazil).Before(stageInfo.EndTime) {
			stageId = stageInfo.StageId
			break
		}
	}

	if len(stageId) == 0 {
		return nil
	}

	// 查询是否已经存在
	var record AgencyNewbieAnchor
	if err := m.dbmc.Collection(AgencyNewbieAnchorCollectionName()).FindOne(ctx, bson.M{
		"stageId":  stageId,
		"agencyId": evd.AgencyId,
		"userId":   evd.UserId,
	}, options.FindOne()).Decode(&record); err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil
		}
		return err
	}

	// 删除邀请人
	if _, err := m.dbmc.Collection(AgencyNewbieAnchorCollectionName()).DeleteOne(ctx, bson.M{
		"stageId":  stageId,
		"agencyId": evd.AgencyId,
		"userId":   evd.UserId,
	}); err != nil {
		return err
	}

	m.log.Info("公会拉新活动主播退出公会", zap.String("stage", stageId), zap.Int("agencyId", evd.AgencyId), zap.String("userId", evd.UserId))

	return nil
}

func (m *Manager) onSetNewAnchor(ctx context.Context, evd *evt.SetNewAnchorFlag) error {
	if !evd.Flag {
		return nil
	}

	// 获取阶段
	var stageId string
	for _, stageInfo := range m.GetActivityStageInfo() {
		if time.Now().In(ctz.Brazil).After(stageInfo.StartTime) && time.Now().In(ctz.Brazil).Before(stageInfo.EndTime) {
			stageId = stageInfo.StageId
			break
		}
	}

	if len(stageId) == 0 {
		return nil
	}

	// 查询是否已经存在
	var record AgencyNewbieAnchor
	if err := m.dbmc.Collection(AgencyNewbieAnchorCollectionName()).FindOne(ctx, bson.M{
		"stageId": stageId,
		"userId":  evd.UserId,
		"status":  NewbieAnchorStatusNoVerify,
	}, options.FindOne()).Decode(&record); err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil
		}
		return err
	}

	// 更新状态
	r, err := m.dbmc.Collection(AgencyNewbieAnchorCollectionName()).UpdateOne(
		ctx,
		bson.M{
			"stageId": stageId,
			"userId":  evd.UserId,
			"status":  NewbieAnchorStatusNoVerify,
		},
		bson.M{
			"$set": bson.M{
				"status":   NewbieAnchorStatusNoMission,
				"updateAt": time.Now(),
			},
		},
	)
	if err != nil {
		return err
	}

	if r.ModifiedCount > 0 {
		m.log.Info("公会拉新活动主播考核通过", zap.String("stage", stageId), zap.Int("agencyId", record.AgencyId), zap.String("userId", evd.UserId))
	}

	return nil
}

func (m *Manager) onNewAnchorMissionCompleted(ctx context.Context, evd *evt.SupportNewAnchorTask) error {
	t := evd.Date.In(ctz.Brazil)
	// 获取阶段
	var stageId string
	for _, stageInfo := range m.GetActivityStageInfo() {
		if t.After(stageInfo.StartTime) && t.Before(stageInfo.EndTime) {
			stageId = stageInfo.StageId
			break
		}
	}

	if len(stageId) == 0 {
		return nil
	}

	// 查询是否已经存在
	var record AgencyNewbieAnchor
	if err := m.dbmc.Collection(AgencyNewbieAnchorCollectionName()).FindOne(ctx, bson.M{
		"stageId": stageId,
		"userId":  evd.UserId,
		"status":  NewbieAnchorStatusNoMission,
	}, options.FindOne()).Decode(&record); err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil
		}
		return err
	}

	// 写入记录
	r, err := m.dbmc.Collection(AgencyNewbieAnchorCollectionName()).UpdateOne(
		ctx,
		bson.M{
			"stageId": stageId,
			"userId":  evd.UserId,
			"status":  NewbieAnchorStatusNoMission,
		},
		bson.M{
			"$set": bson.M{
				"updateAt": time.Now(),
				"status":   NewbieAnchorStatusEffective,
			},
		},
	)
	if err != nil {
		return err
	}

	if r.ModifiedCount > 0 {
		m.log.Info("公会拉新活动主播有效主播", zap.String("stage", stageId), zap.Int("agencyId", record.AgencyId), zap.String("userId", evd.UserId))
	}

	return nil
}
