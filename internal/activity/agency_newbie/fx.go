package agency_newbie

import (
	"github.com/samber/lo"
	"gitlab.sskjz.com/go/ev"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/overseas/live/osl/internal/agency"
	"gitlab.sskjz.com/overseas/live/osl/internal/anchor"
	"gitlab.sskjz.com/overseas/live/osl/internal/evt"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"go.mongodb.org/mongo-driver/bson"
)

func Provide(
	fm *fund.Manager,
	dbmc *db.MongoClient,
	acm *anchor.Manager,
	am *agency.Manager,
	vnd log.Vendor,
) (*Manager, error) {
	dbmc.SyncSchema(AgencyNewbieAnchorCollectionName(), 1,
		db.Indexer{Name: "stageId_agencyId_userId", Keys: bson.D{
			{Key: "stageId", Value: 1},
			{Key: "agencyId", Value: 1},
			{Key: "userId", Value: 1},
		}, Uniq: lo.ToPtr(true)},
	)
	dbmc.SyncSchema(AgencyNewbieReceiveCollectionName(), 1,
		db.Indexer{Name: "stageId_agencyId_userId", Keys: bson.D{
			{Key: "stageId", Value: 1},
			{Key: "agencyId", Value: 1},
			{Key: "userId", Value: 1},
		}, Uniq: lo.ToPtr(true)},
	)

	return newManager(fm, dbmc, acm, am, vnd.Scope("activity.agency_invite.mgr"))
}

func Invoke(
	evb ev.Bus,
	mgr *Manager,
) error {
	// 入会申请审核通过
	evb.Watch(evt.AgencyMemberJoin, "activity.member.join", ev.NewWatcher(mgr.onAgencyJoinVerify), ev.WithAsync())
	// 退出公会事件
	evb.Watch(evt.AgencyMemberQuit, "activity.member.quit", ev.NewWatcher(mgr.onAgencyQuitVerify), ev.WithAsync())
	// 主播审核通过事件
	evb.Watch(evt.NewAnchorFlagSet, "activity.agency.verify", ev.NewWatcher(mgr.onSetNewAnchor), ev.WithAsync())
	// 主播任务完成事件
	evb.Watch(evt.NewAnchorSupportTask, "activity.agency.task", ev.NewWatcher(mgr.onNewAnchorMissionCompleted), ev.WithAsync())

	return nil
}
