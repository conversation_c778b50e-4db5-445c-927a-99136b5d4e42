package rlaa

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/jinzhu/now"
	"go.uber.org/zap"
)

var (
	keyRankSnapshot = "STR:ACT:RLAA:SNAPSHOT:%s" // anchor/agency
	ttlRankSnapshot = time.Hour                  // 1小时
)

// snapshotRank 快照排名
func (m *Manager) snapshotRank(ctx context.Context, nn *now.Now) error {
	roles := []RankRole{RankRoleAnchor, RankRoleAgency}

	for _, role := range roles {
		res, err := m.getRank2(ctx, m.getRankKey(nn.Time, role), 100)

		if err == nil {
			snapshot := make(map[string]int)

			for _, v := range res {
				snapshot[v.UserId] = v.No
			}

			// 保存快照
			data, err := json.Marshal(snapshot)

			if err != nil {
				m.log.Error("快照数据序列化失败", zap.Error(err), zap.String("role", string(role)))

				continue
			}

			err = m.rc.Set(ctx, m.getSnapshotRankKey(role), string(data), ttlRankSnapshot).Err()

			if err != nil {
				m.log.Error("快照数据保存失败", zap.Error(err), zap.String("role", string(role)))

				continue
			}
		}
	}

	return nil
}

func (m *Manager) getSnapshotRankMap(ctx context.Context, role RankRole) (map[string]int, error) {
	ret := make(map[string]int)

	res, err := m.rc.Get(ctx, m.getSnapshotRankKey(role)).Result()

	if err != nil {
		if err == redis.Nil {
			return ret, nil
		}

		return nil, err
	}

	if res == "" {
		return ret, nil
	}

	// 解析快照数据
	err = json.Unmarshal([]byte(res), &ret)

	if err != nil {
		return nil, err
	}

	return ret, nil
}

func (m *Manager) getSnapshotRankKey(role RankRole) string {
	return fmt.Sprintf(keyRankSnapshot, role)
}
