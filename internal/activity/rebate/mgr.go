package rebate

import (
	"context"
	"errors"
	"fmt"
	"github.com/go-redis/redis/v8"
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/internal/im"
	"gitlab.sskjz.com/overseas/live/osl/internal/props"
	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/zap"
	"sort"
	"strconv"
	"time"
)

type Manager struct {
	pm   *props.Manager
	ug   user.Getter
	imm  *im.Manager
	fm   *fund.Manager
	rc   *redi.Client
	dm   *redi.Mutex
	dbmc *db.MongoClient
	log  *zap.Logger
}

func newManager(ug user.Getter, rc *redi.Client, dm *redi.Mutex, pm *props.Manager, imm *im.Manager, fm *fund.Manager, dbmc *db.MongoClient, log *zap.Logger) *Manager {
	m := &Manager{
		pm:   pm,
		ug:   ug,
		imm:  imm,
		fm:   fm,
		rc:   rc,
		dm:   dm,
		dbmc: dbmc,
		log:  log,
	}

	return m
}

var (
	StartTime = time.Date(2025, 05, 26, 0, 0, 0, 0, ctz.Brazil)
	EndTime   = time.Date(2025, 05, 28, 23, 59, 59, 0, ctz.Brazil)
	RankAward = map[int]int{
		1:  500000,
		2:  240000,
		3:  120000,
		4:  80000,
		5:  60000,
		6:  50000,
		7:  20000,
		8:  10000,
		9:  5000,
		10: 2000,
	}
)

func (m *Manager) AwardReceive(ctx context.Context, t time.Time, userId string, rebateAward, rankAward int) error {
	// mongo 判断是否已领取
	if m.IsReceive(ctx, t, userId, rebateAward, rankAward) {
		return biz.NewError(biz.ErrBusiness, "rewards received")
	}

	r, err := m.dbmc.Collection(ActivityRebateReceiveCollectionName()).UpdateOne(
		ctx,
		bson.M{
			"userId": userId,
			"date":   t.Format("********"),
		},
		bson.M{
			"$set": bson.M{
				"isReceive": true,
			},
		},
	)
	if err != nil {
		return err
	}
	if r.ModifiedCount == 0 {
		m.log.Error("activity rebate award receive update mongo err",
			zap.String("userId", userId),
		)
		return biz.NewError(biz.ErrBusiness, "rewards received")
	}

	var totalAward = rebateAward + rankAward
	// 发放金币
	if err := m.fm.Income(ctx, userId, fund.JTypeRewards, fund.PTypeDiamond, totalAward); err != nil {
		m.log.Error("activity rebate award receive err",
			zap.Error(err),
			zap.String("userId", userId),
			zap.Int("award", totalAward),
		)
		return err
	}

	m.log.Info("限时返利活动奖励领取成功",
		zap.String("userId", userId),
		zap.String("t", t.Format("********")),
		zap.Int("award", totalAward),
	)

	return nil
}

func (m *Manager) IsReceive(ctx context.Context, t time.Time, userId string, rebateAward, rankAward int) bool {
	var r ActivityRebateReceive
	err := m.dbmc.Collection(ActivityRebateReceiveCollectionName()).FindOne(
		ctx,
		bson.M{
			"userId": userId,
			"date":   t.Format("********"),
		},
	).Decode(&r)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			if IsOpen(t) {
				// 表中没有写入表中
				if _, err := m.dbmc.Collection(ActivityRebateReceiveCollectionName()).InsertOne(ctx, bson.M{
					"userId":      userId,
					"date":        t.Format("********"),
					"rebateAward": rebateAward,
					"rankAward":   rankAward,
					"totalAward":  rebateAward + rankAward,
					"createAt":    time.Now(),
				}); err != nil {
					return true
				}
			}
			return false
		} else {
			return true
		}
	}

	return r.IsReceive
}

func (m *Manager) AwardReceiveRecord(ctx context.Context, t time.Time, userId string) ([]AwardReceiveRecord, error) {
	var record []ActivityRebateReceive
	cur, err := m.dbmc.Collection(ActivityRebateReceiveCollectionName()).Find(ctx, bson.M{"userId": userId}, options.Find().SetSort(bson.M{"_id": -1}))
	if err != nil {
		return nil, err
	}
	if err := cur.All(ctx, &record); err != nil {
		return nil, err
	}

	var res []AwardReceiveRecord
	for _, v := range record {
		// 过滤非本次活动时间的记录
		rt, err := time.ParseInLocation("********", v.Date, ctz.Brazil)
		if err != nil {
			continue
		}
		if !IsOpen(rt.Add(time.Hour)) {
			continue
		}

		var status int
		if v.IsReceive {
			status = 1
		} else {
			if time.Now().In(ctz.Brazil).AddDate(0, 0, -1).Format("********") == v.Date {
				status = 0
			} else {
				status = -1
			}
		}
		if v.TotalAward == 0 {
			status = 2
		}
		day, _ := time.ParseInLocation("********", v.Date, ctz.Brazil)
		res = append(res, AwardReceiveRecord{
			Date:   day.Format("2006.01.02"),
			Award:  v.TotalAward,
			Status: status,
		})
	}

	return res, nil
}

// GetRanking 获取榜单
func (m *Manager) GetRanking(ctx context.Context, t time.Time, userId string) (*UserRank, []UserRank, error) {
	var (
		myRank   *UserRank
		rankList []UserRank
	)

	ranks := m.getRank(context.Background(), t)
	for k, r := range ranks {
		anchorId := r.Member.(string)
		uac, err := m.ug.Account(ctx, anchorId)
		if err != nil {
			return nil, nil, err
		}

		userRank := UserRank{
			Rank:  k + 1,
			Value: int(r.Score),
			User:  uac,
		}

		if anchorId == userId {
			myRank = &userRank
		}

		rankList = append(rankList, userRank)

	}

	if userId != "" && myRank == nil {
		uac, err := m.ug.Account(ctx, userId)
		if err != nil {
			return nil, nil, err
		}

		rank, value := m.GetRankByUserId(ctx, t, userId)
		myRank = &UserRank{
			Rank:  rank,
			Value: value,
			User:  uac,
		}
	}

	return myRank, rankList, nil
}

func (m *Manager) GetRankByUserId(ctx context.Context, t time.Time, userId string) (int, int) {
	rankKey := fmt.Sprintf(keyRebateActivityRank, t.Format("********"))
	value, err := m.rc.ZScore(ctx, rankKey, userId).Result()
	if err != nil {
		return 0, 0
	}

	rank, err := m.rc.ZRevRank(ctx, rankKey, userId).Result()
	if err != nil {
		return 0, 0
	}
	return int(rank) + 1, int(value)
}

func (m *Manager) GetTop10Rank(ctx context.Context, t time.Time) []redis.Z {
	return m.getRank(ctx, t)
}

// 同分情况，先到者在前
func (m *Manager) getRank(ctx context.Context, t time.Time) []redis.Z {
	var rank []redis.Z

	rankKey := fmt.Sprintf(keyRebateActivityRank, t.Format("********"))
	rank, err := m.rc.ZRevRangeWithScores(ctx, rankKey, 0, 9).Result()
	if err != nil {
		return rank
	}

	var userIds []string
	for _, v := range rank {
		userIds = append(userIds, v.Member.(string))
	}

	var updateMap = map[string]int64{}
	u := m.rc.HMGet(ctx, fmt.Sprintf(keyRebateUpdateTime, t.Format("********")), userIds...).Val()
	for i, v := range userIds {
		item := u[i]
		if str, ok := item.(string); ok {
			str2Int, _ := strconv.ParseInt(str, 10, 64)
			updateMap[v] = str2Int
		}
	}

	sort.Slice(rank, func(i, j int) bool {
		if rank[i].Score == rank[j].Score {
			return updateMap[rank[i].Member.(string)] < updateMap[rank[j].Member.(string)]
		}
		return rank[i].Score > rank[j].Score
	})

	return rank
}

// 生成记录
func (m *Manager) genRecord(ctx context.Context, t time.Time) error {
	if !IsOpen(t) {
		return nil
	}

	m.log.Info("限时返利活动记录榜单开始")

	// 获取所有榜单
	var rank []redis.Z
	rankKey := fmt.Sprintf(keyRebateActivityRank, t.Format("********"))
	rank, err := m.rc.ZRevRangeWithScores(ctx, rankKey, 0, -1).Result()
	if err != nil {
		m.log.Error("限时返利活动获取榜单失败", zap.Error(err))
		return err
	}

	var userIds []string
	for i, v := range rank {
		if i > 20 {
			break
		}
		userIds = append(userIds, v.Member.(string))
	}

	var updateMap = map[string]int64{}
	u := m.rc.HMGet(ctx, fmt.Sprintf(keyRebateUpdateTime, t.Format("********")), userIds...).Val()

	for i, v := range userIds {
		item := u[i]
		if str, ok := item.(string); ok {
			str2Int, _ := strconv.ParseInt(str, 10, 64)
			updateMap[v] = str2Int
		}
	}

	sort.Slice(rank, func(i, j int) bool {
		if rank[i].Score == rank[j].Score {
			return updateMap[rank[i].Member.(string)] < updateMap[rank[j].Member.(string)]
		}
		return rank[i].Score > rank[j].Score
	})

	for r, v := range rank {
		userId := v.Member.(string)
		award := int(v.Score)
		rankAward := RankAward[r+1]
		m.IsReceive(ctx, t, userId, award, rankAward)
	}

	m.log.Info("限时返利活动记录榜单完成")

	return nil
}
