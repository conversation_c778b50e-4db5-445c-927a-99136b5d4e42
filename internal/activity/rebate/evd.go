package rebate

import (
	"context"
	"errors"
	"fmt"
	"github.com/go-redis/redis/v8"
	"gitlab.sskjz.com/overseas/live/osl/internal/evt"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
	"go.uber.org/zap"
	"math"
	"time"
)

func (m *Manager) onSendGift(ctx context.Context, evd *evt.SendGift) error {
	if evd.GiftId == 0 || evd.Count <= 0 || evd.UserId == "" {
		return nil
	}

	// 活动时间判断
	if !IsOpen(evd.At) {
		return nil
	}

	if !evd.Gift.Lucky {
		return nil
	}

	// 用户级别锁
	l, err := m.dm.Lock(ctx, fmt.Sprintf(keyLockUserRank, evd.UserId))
	if err != nil {
		return err
	}
	defer l.MustUnlock()

	var value = float64(evd.Diamond) * 0.01
	// 小数部分处理
	leftoverKey := fmt.Sprintf(keyRebateLeftover, evd.At.In(ctz.Brazil).Format("20060102"), evd.UserId)
	leftover, err := m.rc.Get(ctx, leftoverKey).Float64()
	if err != nil && !errors.Is(err, redis.Nil) {
		return err
	}
	value += leftover
	finalValue, finalLeftover := math.Modf(value)
	if finalLeftover == 0 {
		m.rc.Del(ctx, leftoverKey)
	} else {
		m.rc.SetEX(ctx, leftoverKey, fmt.Sprintf("%.2f", finalLeftover), ttlRebateLeftover)
	}
	if finalValue < 1 {
		return nil
	}

	m.log.Info("rebate onSendGift", zap.Any("evd", evd))

	if err := m.rankUpdate(ctx, evd.At, evd.UserId, finalValue); err != nil {
		m.log.Error("rebate onSendGift update rank err", zap.Error(err), zap.Any("evd", evd))
		return err
	}

	return nil
}

func (m *Manager) onGamePaid(ctx context.Context, evd *evt.PaidInGame) error {
	if evd.Amount <= 0 || evd.UserId == "" {
		return nil
	}

	// 活动时间判断
	if !IsOpen(evd.At) {
		return nil
	}

	// 用户级别锁
	l, err := m.dm.Lock(ctx, fmt.Sprintf(keyLockUserRank, evd.UserId))
	if err != nil {
		return err
	}
	defer l.MustUnlock()

	var value = float64(evd.Amount) * 0.005
	// 小数部分处理
	leftoverKey := fmt.Sprintf(keyRebateLeftover, evd.At.In(ctz.Brazil).Format("20060102"), evd.UserId)
	leftover, err := m.rc.Get(ctx, leftoverKey).Float64()
	if err != nil && !errors.Is(err, redis.Nil) {
		return err
	}
	value += leftover
	finalValue, finalLeftover := math.Modf(value)
	if finalLeftover == 0 {
		m.rc.Del(ctx, leftoverKey)
	} else {
		m.rc.SetEX(ctx, leftoverKey, fmt.Sprintf("%.3f", finalLeftover), ttlRebateLeftover)
	}
	if finalValue < 1 {
		return nil
	}

	if err := m.rankUpdate(ctx, evd.At, evd.UserId, finalValue); err != nil {
		m.log.Error("rebate onGamePaid update rank err", zap.Error(err), zap.Any("evd", evd))
		return err
	}

	return nil
}

func (m *Manager) rankUpdate(ctx context.Context, t time.Time, userId string, value float64) error {
	rankKey := fmt.Sprintf(keyRebateActivityRank, t.In(ctz.Brazil).Format("20060102"))
	err := m.rc.ZIncrBy(ctx, rankKey, value, userId).Err()
	if err != nil {
		return err
	}
	m.rc.Expire(ctx, rankKey, ttlRebateActivityRank)

	// 记录用户分数最后更新时间
	updateKey := fmt.Sprintf(keyRebateUpdateTime, t.In(ctz.Brazil).Format("20060102"))
	m.rc.HSet(ctx, updateKey, userId, t.Unix())
	m.rc.Expire(ctx, updateKey, ttlRebateUpdateTime)

	return nil
}
