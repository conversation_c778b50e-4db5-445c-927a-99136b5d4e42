package coin_grab

import (
	"context"
	"errors"
	"fmt"
	"github.com/go-redis/redis/v8"
	"gitlab.sskjz.com/overseas/live/osl/internal/evt"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
	"go.uber.org/zap"
	"math"
	"time"
)

func (m *Manager) onWinGame(ctx context.Context, evd *evt.WinGame) error {
	if evd.Amount <= 0 || evd.UserId == "" {
		return nil
	}

	// 活动时间判断
	if !IsOpen(evd.At) {
		return nil
	}

	var value = float64(evd.Amount) * 0.001
	// 小数部分处理
	leftoverKey := fmt.Sprintf(keyCoingrabLeftover, evd.At.In(ctz.Brazil).Format("20060102"), evd.UserId)
	leftover, err := m.rc.Get(ctx, leftoverKey).Float64()
	if err != nil && !errors.Is(err, redis.Nil) {
		return err
	}
	value += leftover
	finalValue, finalLeftover := math.Modf(value)
	if finalLeftover == 0 {
		m.rc.Del(ctx, leftoverKey)
	} else {
		m.rc.SetEX(ctx, leftoverKey, fmt.Sprintf("%.3f", finalLeftover), ttlCoingrabLeftover)
	}
	if finalValue < 1 {
		return nil
	}

	if err := m.rankUpdate(ctx, evd.At, evd.UserId, float64(finalValue)); err != nil {
		m.log.Error("rebate onGamePaid update rank err", zap.Error(err), zap.Any("evd", evd))
		return err
	}

	// 增加奖池
	total, _ := m.rc.IncrBy(ctx, fmt.Sprintf(keyCoinPool, evd.At.In(ctz.Brazil).Format("20060102")), PreValueIncrCoin*int64(finalValue)).Result()
	// 记录日志
	m.log.Info("增加总奖池",
		zap.String("date", evd.At.In(ctz.Brazil).Format("20060102")),
		zap.Int64("incr", PreValueIncrCoin*int64(finalValue)),
		zap.Int64("pool", total),
	)

	return nil
}

func (m *Manager) rankUpdate(ctx context.Context, t time.Time, userId string, value float64) error {
	rankKey := fmt.Sprintf(keyCoinGrabActivityRank, t.In(ctz.Brazil).Format("20060102"))
	err := m.rc.ZIncrBy(ctx, rankKey, value, userId).Err()
	if err != nil {
		return err
	}
	m.rc.Expire(ctx, rankKey, ttlCoinGrabActivityRank)

	// 记录用户分数最后更新时间
	updateKey := fmt.Sprintf(keyCoinGrabUpdateTime, t.In(ctz.Brazil).Format("20060102"))
	m.rc.HSet(ctx, updateKey, userId, t.Unix())
	m.rc.Expire(ctx, updateKey, ttlCoinGrabUpdateTime)

	return nil
}
