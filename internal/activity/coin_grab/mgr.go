package coin_grab

import (
	"context"
	"errors"
	"fmt"
	"github.com/go-redis/redis/v8"
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/internal/im"
	"gitlab.sskjz.com/overseas/live/osl/internal/props"
	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
	"math"
	"sort"
	"strconv"
	"time"
)

type Manager struct {
	pm   *props.Manager
	ug   user.Getter
	imm  *im.Manager
	fm   *fund.Manager
	rc   *redi.Client
	dbmc *db.MongoClient
	log  *zap.Logger
}

func newManager(ug user.Getter, rc *redi.Client, pm *props.Manager, imm *im.Manager, fm *fund.Manager, dbmc *db.MongoClient, log *zap.Logger) *Manager {
	m := &Manager{
		pm:   pm,
		ug:   ug,
		imm:  imm,
		fm:   fm,
		rc:   rc,
		dbmc: dbmc,
		log:  log,
	}

	return m
}

var (
	StartTime    = time.Date(2025, 05, 29, 0, 0, 0, 0, ctz.Brazil)
	EndTime      = time.Date(2025, 06, 01, 23, 59, 59, 0, ctz.Brazil)
	awardPercent = map[int]float64{
		1:  0.15, //15%
		2:  0.12, //12%
		3:  0.08, //8%
		4:  0.05, //5%
		5:  0.05, //5%
		6:  0.03, //3%
		7:  0.03, //3%
		8:  0.03, //3%
		9:  0.03, //3%
		10: 0.03, //3%
		11: 0.02, //2%
		12: 0.02, //2%
		13: 0.02, //2%
		14: 0.02, //2%
		15: 0.02, //2%
		16: 0.02, //2%
		17: 0.02, //2%
		18: 0.02, //2%
		19: 0.02, //2%
		20: 0.02, //2%
		21: 0.01, //1%
		22: 0.01, //1%
		23: 0.01, //1%
		24: 0.01, //1%
		25: 0.01, //1%
		26: 0.01, //1%
		27: 0.01, //1%
		28: 0.01, //1%
		29: 0.01, //1%
		30: 0.01, //1%
		31: 0.01, //1%
		32: 0.01, //1%
		33: 0.01, //1%
		34: 0.01, //1%
		35: 0.01, //1%
		36: 0.01, //1%
		37: 0.01, //1%
		38: 0.01, //1%
		39: 0.01, //1%
		40: 0.01, //1%
	}
)

// RewardReceive 领取奖励
func (m *Manager) RewardReceive(ctx context.Context, userId string) (int, int64, error) {
	t := time.Now().In(ctz.Brazil).AddDate(0, 0, -1)
	// 活动时间判断
	if !IsOpen(t) {
		return 0, 0, biz.NewError(biz.ErrBusiness, "no award")
	}

	var rank int
	top50 := m.getRank(ctx, t)
	if len(top50) > 0 {
		for i, v := range top50 {
			rankUserId := v.Member.(string)
			if rankUserId == userId {
				rank = i + 1
				break
			}
		}
	}
	if rank < 1 || rank > len(awardPercent) {
		return 0, 0, biz.NewError(biz.ErrBusiness, "no award")
	}

	if m.IsReceive(ctx, t, userId) {
		return 0, 0, biz.NewError(biz.ErrBusiness, "received")
	}

	coinPool, err := m.GetCoinPool(ctx, t)
	if err != nil {
		return 0, 0, err
	}

	percent := awardPercent[rank]
	if percent == 0 {
		return 0, 0, biz.NewError(biz.ErrBusiness, "no award")
	}

	reward := int64(math.Ceil(float64(coinPool) * percent))
	if reward == 0 {
		m.log.Error("coin grab receive err, no reward", zap.String("userId", userId), zap.Int("rank", rank))
		return 0, 0, biz.NewError(biz.ErrBusiness, "no award")
	}

	// 写入表中
	if _, err := m.dbmc.Collection(ActivityCoinGrabReceiveCollectionName()).InsertOne(ctx, bson.M{
		"userId":   userId,
		"date":     t.Format("********"),
		"rank":     rank,
		"coinPool": coinPool,
		"percent":  percent,
		"reward":   reward,
		"createAt": time.Now(),
	}); err != nil {
		return 0, 0, err
	}

	// 发放奖励
	if err := m.fm.Income(ctx, userId, fund.JTypeRewards, fund.PTypeDiamond, reward); err != nil {
		m.log.Error("抢金赛活动奖励发送失败",
			zap.Error(err),
			zap.String("userId", userId),
			zap.String("t", t.Format("********")),
			zap.Int64("coinPool", coinPool),
			zap.Int("rank", rank),
			zap.Int64("reward", reward),
		)
		return 0, 0, err
	}

	m.log.Info("抢金赛活动奖励领取成功",
		zap.String("userId", userId),
		zap.String("t", t.Format("********")),
		zap.Int64("coinPool", coinPool),
		zap.Int("rank", rank),
		zap.Int64("reward", reward),
	)

	return rank, reward, nil
}

func (m *Manager) IsReceive(ctx context.Context, t time.Time, userId string) bool {
	var r ActivityCoinGrabReceive
	err := m.dbmc.Collection(ActivityCoinGrabReceiveCollectionName()).FindOne(
		ctx,
		bson.M{
			"userId": userId,
			"date":   t.Format("********"),
		},
	).Decode(&r)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return false
		} else {
			return true
		}
	}

	return true
}

// GetRoomWidget 返回总奖池金币数，我的排名
func (m *Manager) GetRoomWidget(ctx context.Context, userId string) (int, int64) {
	t := time.Now().In(ctz.Brazil)
	var userRank int
	userRank, _ = m.getUserRankInfo(ctx, t, userId)
	if userRank != 0 && userRank < 50 {
		top50 := m.getRank(ctx, t)
		if len(top50) > 0 {
			for i, v := range top50 {
				rankUserId := v.Member.(string)
				if rankUserId == userId {
					userRank = i + 1
					break
				}
			}
		}
	}
	pool, _ := m.GetCoinPool(ctx, t)
	return userRank, pool
}

func (m *Manager) GetRank(ctx context.Context, rankingDay, userId string) (*CoinGrabInfo, error) {
	var (
		res = CoinGrabInfo{
			StartAt:  StartTime.Unix(),
			EndAt:    EndTime.Unix(),
			RankInfo: make([]UserRank, 0),
		}
		t = time.Now().In(ctz.Brazil)
	)

	if rankingDay == "yesterday" {
		t = t.AddDate(0, 0, -1)
	}

	// 获取总奖池
	coinPool, err := m.GetCoinPool(ctx, t)
	if err != nil {
		return nil, err
	}

	res.CoinPool = coinPool

	// 获取排行榜
	rank := m.getRank(ctx, t)
	if len(rank) > 0 {
		for i, v := range rank {
			rankUserId := v.Member.(string)
			uac, _ := m.ug.Account(ctx, rankUserId)
			var reward int64
			percent := awardPercent[i+1]
			if percent != 0 {
				reward = int64(math.Ceil(float64(coinPool) * percent))
			}
			userRank := UserRank{
				Rank:   i + 1,
				User:   uac,
				Value:  int64(v.Score),
				Reward: reward,
			}

			res.RankInfo = append(res.RankInfo, userRank)
			if rankUserId == userId {
				res.MyRank = &userRank
			}
		}

		if res.MyRank == nil {
			// 获取自己排名信息
			userRank, userValue := m.getUserRankInfo(ctx, t, userId)
			uac, _ := m.ug.Account(ctx, userId)
			var reward int64
			if userRank > 0 {
				percent := awardPercent[userRank]
				if percent != 0 {
					reward = int64(math.Ceil(float64(coinPool) * percent))
				}
			}
			res.MyRank = &UserRank{
				Rank:   userRank,
				User:   uac,
				Value:  userValue,
				Reward: reward,
			}
		}
	}

	return &res, nil
}

// getUserRankInfo 返回我的排名与积分
func (m *Manager) getUserRankInfo(ctx context.Context, t time.Time, userId string) (int, int64) {
	rankKey := fmt.Sprintf(keyCoinGrabActivityRank, t.Format("********"))

	rank, err := m.rc.ZRevRank(ctx, rankKey, userId).Result()
	if err != nil {
		return 0, 0
	}
	value, _ := m.rc.ZScore(ctx, rankKey, userId).Result()

	return int(rank + 1), int64(value)
}

// 同分情况，先到者在前
func (m *Manager) getRank(ctx context.Context, t time.Time) []redis.Z {
	var rank []redis.Z

	rankKey := fmt.Sprintf(keyCoinGrabActivityRank, t.Format("********"))
	rank, err := m.rc.ZRevRangeWithScores(ctx, rankKey, 0, 49).Result()
	if err != nil {
		return rank
	}

	var userIds []string
	for _, v := range rank {
		userIds = append(userIds, v.Member.(string))
	}

	var updateMap = map[string]int64{}
	u := m.rc.HMGet(ctx, fmt.Sprintf(keyCoinGrabUpdateTime, t.Format("********")), userIds...).Val()
	for i, v := range userIds {
		item := u[i]
		if str, ok := item.(string); ok {
			str2Int, _ := strconv.ParseInt(str, 10, 64)
			updateMap[v] = str2Int
		}
	}

	sort.Slice(rank, func(i, j int) bool {
		if rank[i].Score == rank[j].Score {
			return updateMap[rank[i].Member.(string)] < updateMap[rank[j].Member.(string)]
		}
		return rank[i].Score > rank[j].Score
	})

	return rank
}

// GetCoinPool 获取总奖池
func (m *Manager) GetCoinPool(ctx context.Context, t time.Time) (int64, error) {
	r, err := m.rc.Get(ctx, fmt.Sprintf(keyCoinPool, t.Format("********"))).Int64()
	if err != nil && !errors.Is(err, redis.Nil) {
		return 0, err
	}
	return r, nil
}

func (m *Manager) initCoinPool(ctx context.Context) error {
	t := time.Now().In(ctz.Brazil).AddDate(0, 0, 1)
	if IsOpen(t) {
		exists, err := m.rc.Exists(ctx, fmt.Sprintf(keyCoinPool, t.Format("********"))).Result()
		if err != nil {
			m.log.Error("抢金赛初始化奖池:获取奖池错误", zap.String("date", t.In(ctz.Brazil).Format("********")))
			return err
		}
		if exists > 0 {
			m.log.Info("抢金赛初始化奖池:奖池已存在", zap.String("date", t.In(ctz.Brazil).Format("********")))
			return nil
		}
		m.rc.SetEX(context.Background(), fmt.Sprintf(keyCoinPool, t.Format("********")), InitPoolCoin, ttlCoinPool*2)
		m.log.Info("抢金赛初始化奖池:成功", zap.String("date", t.In(ctz.Brazil).Format("********")))
	}

	return nil
}

func (m *Manager) imNotify(ctx context.Context) error {
	t := time.Now().In(ctz.Brazil).AddDate(0, 0, -1)
	if IsOpen(t) {
		// 获取总奖池
		coinPool, err := m.GetCoinPool(ctx, t)
		if err != nil {
			return err
		}

		// 获取排行榜
		rank := m.getRank(ctx, t)
		if len(rank) > 0 {
			for i, v := range rank {
				if i >= len(awardPercent) {
					break
				}
				rankUserId := v.Member.(string)
				var reward int64
				percent := awardPercent[i+1]
				if percent != 0 {
					reward = int64(math.Ceil(float64(coinPool) * percent))
				}
				if reward == 0 {
					continue
				}

				// 发送消息
				if err := m.imm.SendSystemNoticeTextToUser(
					ctx,
					rankUserId,
					fmt.Sprintf(
						"Parabéns! Você ficou em %d° lugar na Liga de players de ontem e ganhou %d coins. Por favor, colete na página do evento.",
						i+1,
						reward,
					),
				); err != nil {
					m.log.Error("抢金赛结榜消息发送失败",
						zap.Error(err),
						zap.String("userId", rankUserId),
						zap.Int64("reward", reward),
						zap.Int64("time", t.Unix()))
				}

				m.log.Info("抢金赛结榜消息发送成功",
					zap.String("userId", rankUserId),
					zap.Int64("reward", reward),
					zap.Int64("time", t.Unix()))
			}
		}
	}

	return nil
}
