package face

import (
	"context"
	"fmt"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/google/uuid"
	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/dbg"
	"gitlab.sskjz.com/overseas/live/osl/pkg/dun"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

var (
	ErrFaceCheck          = biz.NewError(biz.ErrFaceCheck, "face check failed")
	ErrFaceNotMatch       = biz.NewError(biz.ErrFaceNotMatch, "face not match")
	ErrFaceExist          = biz.NewError(biz.ErrFaceExist, "face exist")
	ErrTicketNotFound     = biz.NewError(biz.ErrFaceTicket, "ticket not found")
	ErrTicketInvalid      = biz.NewError(biz.ErrFaceTicket, "ticket invalid")
	ErrTicketVerifyFailed = biz.NewError(biz.ErrFaceTicket, "ticket verify failed")
)

const (
	keyUserTicket = "STR:FACE:TKT:%s"
	ttlUserTicket = time.Minute * 60
)

// 人脸检测录入
func (m *Manager) Check(ctx context.Context, userId, token, scene string) (string, error) {
	dataId := uuid.NewString()

	logger := m.log.With(
		zap.String("userId", userId),
		zap.String("scene", scene),
		zap.String("token", token),
		zap.String("dataId", dataId),
	)

	var ticket string

	l, err := m.dm.Lock(ctx, fmt.Sprintf("LOCK:FACE:CHECK:%s", userId))

	if err != nil {
		return ticket, fmt.Errorf("lock failed: %w", err)
	}

	defer l.MustUnlock()

	// 活体检测
	lpCheckResult, err := m.df.LpCheck(ctx, token, dun.WithDataId(dataId))

	if err != nil {
		logger.Debug("活体检测失败", zap.Error(err))

		return ticket, ErrFaceCheck
	}

	// 查询用户认证状态
	acc, err := m.um.Account(ctx, userId)

	if err != nil {
		logger.Error("查询用户认证状态失败", zap.Error(err))

		return ticket, ErrFaceCheck
	}

	picType := dun.PicType(lpCheckResult.PicType)
	avatar := lpCheckResult.Avatar
	name := fmt.Sprintf("%d", acc.NumId)

	logger = logger.With(
		zap.String("avatar", avatar),
		zap.String("name", name),
	)

	// 先检索
	matches, err := m.df.Check(ctx, picType, avatar, dun.WithDataId(dataId))

	if err != nil {
		logger.Debug("人脸检索失败", zap.Error(err))

		return ticket, ErrFaceCheck
	}

	logger = logger.With(
		zap.Any("matches", matches),
	)

	if acc.Status.Verified() {
		// 当前为已认证账号，则比对
		ok := false

		for _, match := range matches {
			if match.FaceId == userId {
				ok = true
				break
			}
		}

		if !ok && dbg.Ing() {
			ok = devAllowedFace(ctx, m.rc, userId)
		}

		if !ok {
			logger.Debug("人脸不匹配")

			return ticket, ErrFaceNotMatch
		}
	} else {
		if userId == "df9bb4ff4a784feea55bef4c23cb1f4e" {
			matches = []dun.FaceMatch{}
		}

		// ********和********撞脸
		if userId == "1af65648b1ff406bae9076d2ab440f5b" {
			tmp := []dun.FaceMatch{}
			for _, match := range matches {
				if match.FaceId != "85ffb0d1f47e4d1d85f00406e82be563" {
					tmp = append(tmp, match)
				}
			}

			matches = tmp
		}

		// 人脸被认证过账号数量是否超过限制
		if len(matches) >= m.faceLimitAccountNum() {
			// 检查一遍有没有是当前账号的，但是状态没变的
			for _, match := range matches {
				if match.FaceId == userId {
					m.um.Update(ctx, userId, user.SetStatus(user.StatusVerified))
				}
			}

			logger.Debug("人脸已被认证")

			return ticket, ErrFaceExist
		}

		// 注册
		if err := m.df.Add(ctx, userId, name, picType, avatar, dun.WithDataId(dataId)); err != nil {
			logger.Debug("人脸注册失败", zap.Error(err))

			return ticket, ErrFaceCheck
		}

		if err := m.um.Update(ctx, userId, user.SetStatus(user.StatusVerified)); err != nil {
			logger.Debug("更新人脸认证状态失败", zap.Error(err))

			return ticket, ErrFaceCheck
		}
	}

	// 生成ticket
	ticket, err = m.generateTicket(ctx, userId)

	if err != nil {
		return ticket, ErrFaceCheck
	}

	logger.Info("人脸检测成功", zap.String("ticket", ticket))

	_, err = m.dbmc.Collection(FaceCollectionName()).InsertOne(
		ctx,
		Face{
			Id:        primitive.NewObjectID(),
			UserId:    userId,
			Scene:     scene,
			FaceUrl:   avatar,
			CreatedAt: time.Now(),
		},
	)

	if err != nil {
		logger.Error("插入人脸记录失败", zap.Error(err))
	}

	return ticket, nil
}

// 校验人脸检测tiket
func (m *Manager) VerifyTicket(ctx context.Context, userId, ticket string) error {
	if err := m.ValidTicket(ctx, userId, ticket); err != nil {
		return err
	}
	return m.ResetTicket(ctx, userId)
}

func (m *Manager) ValidTicket(ctx context.Context, userId, ticket string) error {
	if dbg.Ing() && ticket == "www" {
		return nil
	}

	key := fmt.Sprintf(keyUserTicket, userId)

	res, err := m.rc.Get(ctx, key).Result()

	if err != nil {
		if err == redis.Nil {
			return ErrTicketNotFound
		}

		return ErrTicketVerifyFailed
	}

	if res != ticket {
		return ErrTicketInvalid
	}

	return nil
}

func (m *Manager) ResetTicket(ctx context.Context, userId string) error {
	key := fmt.Sprintf(keyUserTicket, userId)
	return m.rc.Del(ctx, key).Err()
}

func (m *Manager) generateTicket(ctx context.Context, userId string) (string, error) {
	ticket := uuid.NewString()

	key := fmt.Sprintf(keyUserTicket, userId)

	err := m.rc.Set(ctx, key, ticket, ttlUserTicket).Err()

	if err != nil {
		return "", err
	}

	return ticket, nil
}

func (m *Manager) faceLimitAccountNum() int {
	if dbg.Ing() {
		return 200
	}

	return 1
}
