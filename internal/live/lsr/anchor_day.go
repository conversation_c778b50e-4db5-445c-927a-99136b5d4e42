package lsr

import (
	"context"
	"time"

	"github.com/jinzhu/now"
	"go.mongodb.org/mongo-driver/bson"
)

func (m *Manager) GetLiveSummaryDay(ctx context.Context, userId string, day time.Time) (*LiveSummaryDay, error) {
	res := m.dbmc.Collection(LiveSummaryDayCollectionName()).FindOne(
		ctx,
		bson.M{
			"userId": userId,
			"day":    now.New(day).BeginningOfDay(),
		},
	)

	if res.Err() != nil {
		return nil, res.Err()
	}

	var record LiveSummaryDay

	if err := res.Decode(&record); err != nil {
		return nil, err
	}

	return &record, nil
}
