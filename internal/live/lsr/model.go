package lsr

import (
	"time"

	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type LiveSummaryDay struct {
	Id            primitive.ObjectID `bson:"_id"`
	UserId        string             `bson:"userId"`        // 主播ID
	Day           time.Time          `bson:"day"`           // 日期 降序索引
	Date          string             `bson:"date"`          // 日期 yyyyMMdd
	AudCount      int64              `bson:"audCount"`      // 观看人数 每一场直播结束时增加
	AudFanCount   int64              `bson:"audFanCount"`   // 观众粉丝数 每一场直播结束时增加
	NewFanCount   int64              `bson:"newFanCount"`   // 新增粉丝数 每一场直播结束时增加
	Duration      int64              `bson:"duration"`      // 总有效直播时长（所有秒数和），单位秒
	PauseDuration int64              `bson:"pauseDuration"` // 总有效暂停时长（所有秒数和），单位秒
	LuckDiamond   int64              `bson:"luckDiamond"`   // 钻石 幸运礼物收入
	LuckIncome    fund.Decimal       `bson:"luckIncome"`    // 水晶 幸运礼物收入
	GiftDiamond   int64              `bson:"giftDiamond"`   // 钻石 特效礼物收入
	GiftIncome    fund.Decimal       `bson:"giftIncome"`    // 水晶 特效礼物收入
	GiftUserCount int64              `bson:"giftUserCount"` // 送礼人数
	FClubDiamond  int64              `bson:"fClubDiamond"`  // 钻石 粉丝团流水
	FClubIncome   fund.Decimal       `bson:"fClubIncome"`   // 水晶 粉丝团收入
	Salary        int64              `bson:"salary"`        // 当日薪资（水晶），每日结算时最终确定
	CTZ           string             `bson:"ctz"`           // 时区, 例如：ID:Asia/Jakarta
}

func LiveSummaryDayCollectionName() string {
	return "live.summary.day"
}

// 主播在公会的天维度数据
type LiveSummaryAgencyDay struct {
	Id                 primitive.ObjectID `bson:"_id"`
	UserId             string             `bson:"userId"`             // 主播ID
	Day                string             `bson:"day"`                // 日期 yyyyMMdd，汇总键
	IsNewAnchor        bool               `bson:"isNewAnchor"`        // 是否新主播
	LuckDiamond        int64              `bson:"luckDiamond"`        // 幸运礼物流水，钻石
	GiftDiamond        int64              `bson:"giftDiamond"`        // 特效礼物流水，钻石
	FClubDiamond       int64              `bson:"fClubDiamond"`       // 粉丝团流水，钻石
	LuckIncome         fund.Decimal       `bson:"luckIncome"`         // 幸运礼物收入，水晶
	GiftIncome         fund.Decimal       `bson:"giftIncome"`         // 特效礼物收入，水晶
	FClubIncome        fund.Decimal       `bson:"fClubIncome"`        // 粉丝团收入，水晶
	SalaryIncome       fund.Decimal       `bson:"salaryIncome"`       // 薪资收入，水晶
	AgencyIncome       fund.Decimal       `bson:"agencyIncome"`       // 公会礼物收入，水晶
	AgencySalaryIncome fund.Decimal       `bson:"agencySalaryIncome"` // 公会薪资收入，水晶
	Duration           int64              `bson:"duration"`           // 总有效直播时长（所有秒数和），单位秒
	GiftUserCount      int64              `bson:"giftUserCount"`      // 送礼人数
	AgencyId           int64              `bson:"agencyId"`           // 公会ID
	CTZ                string             `bson:"ctz"`                // 时区, 例如：ID:Asia/Jakarta
	StartTime          time.Time          `bson:"startTime"`          // 数据统计开始时间
	EndTime            time.Time          `bson:"endTime"`            // 数据统计结束时间
}

func LiveSummaryAgencyDayCollectionName() string {
	return "live.summary.agency.day"
}
