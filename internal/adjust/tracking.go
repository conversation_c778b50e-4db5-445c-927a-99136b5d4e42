package adjust

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/go-redis/redis/v8"
	"gitlab.sskjz.com/overseas/live/osl/pkg/app"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
)

const (
	keyTempNetwork = "ADJUST:NETWORK:%s" // adId
	ttlTempNetwork = time.Hour
)

func (s *Manager) saveTemp(ctx context.Context, adId, network string) error {
	return s.rc.Set(ctx, fmt.Sprintf(keyTempNetwork, adId), network, ttlTempNetwork).Err()
}

func (s *Manager) getTemp(ctx context.Context, adId string) (network string, err error) {
	network, err = s.rc.Get(ctx, fmt.Sprintf(keyTempNetwork, adId)).Result()
	if errors.Is(err, redis.Nil) {
		err = nil
	}
	return
}

func (s *Manager) tmpNetwork(ctx context.Context, adId string) Network {
	network, _ := s.getTemp(ctx, adId)
	return Network(network)
}

func (s *Manager) onRegister(ctx context.Context, uac *user.Account) error {
	if g := api.Unwrap(ctx); g != nil {
		adId := app.DeviceId(g)
		if network, _ := s.getTemp(ctx, adId); network != "" {
			return s.touch2(ctx, uac.UserId, adId, network)
		}
	}
	return nil
}
