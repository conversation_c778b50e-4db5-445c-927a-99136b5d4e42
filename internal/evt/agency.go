package evt

const (
	AgencyApply       = "agency.apply"        // AgencyApplyEvt
	AgencyVerify      = "agency.verify"       // AgencyVerifyEvt
	AgencyMemberApply = "agency.member.apply" // AgencyMemberApplyEvt
	AgencyInviteEdit  = "agency.invite.edit"  // AgencyInviteEditEvt
	AgencyDelete      = "agency.delete"       // AgencyDeleteEvt
	AgencyMemberJoin  = "agency.member.join"  // AgencyMemberJoinEvt 用户加入公会事件
	AgencyMemberQuit  = "agency.member.quit"  // AgencyMemberQuitEvt 用户退出公会事件
)

type AgencyApplyEvt struct {
	UserId     string
	InviteCode string
}

type AgencyVerifyEvt struct {
	UserId          string
	InviteCode      string
	VerifyStatus    int
	AgencyId        int
	Manage          bool
	ContactWhatsapp string
}

type AgencyMemberApplyEvt struct {
	UserId  string // 申请用户ID
	ChiefId string // 公会长ID
}

type AgencyInviteEditEvt struct {
	AgencyId           int    // 公会id
	AgencyShowId       string // 公会showid
	OriginalInviteCode string // 原邀请码
	CurrentInviteCode  string // 现邀请码
}

type AgencyDeleteEvt struct {
	AgencyId int // 公会id
}

type AgencyMemberJoinEvt struct {
	AgencyId int    // 公会表主键id
	UserId   string // 退出公会的用户
}

type AgencyMemberQuitEvt struct {
	AgencyId int    // 公会表主键id
	UserId   string // 退出公会的用户
}
