package evt

const (
	EvReportCommit = "report.commit"
	EvReportSolve  = "report.solve"
)

type ReportCommit struct {
	UserId       string
	Target       int
	TargetUserId string
	TargetId     string
	TypeId       int
}

const (
	ReportLiveMinorTypeId = 2006
)

type ReportTarget int

const (
	ReportTypeMoment        ReportTarget = 1
	ReportTypeLive          ReportTarget = 2
	ReportTypeIM            ReportTarget = 3
	ReportTypeUser          ReportTarget = 4
	ReportTypeMomentComment ReportTarget = 5
)

type ReportOperation int

// 1001动态删除 1002动态自见 2001直播间封禁 3001禁发私信 4001重置头像 4002重置昵称 4003重置签名 4004封号 4005用户禁言 5001评论删除
const (
	OperationMomentDel         ReportOperation = 1001
	OperationMomentSelfVisible ReportOperation = 1002
	OperationRoomBan           ReportOperation = 2001
	OperationRoomStop          ReportOperation = 2002
	OperationImBan             ReportOperation = 3001
	OperationResetAvatar       ReportOperation = 4001
	OperationResetNickname     ReportOperation = 4002
	OperationResetSign         ReportOperation = 4003
	OperationUserBan           ReportOperation = 4004
	OperationUserMute          ReportOperation = 4005
	OperationCommentDel        ReportOperation = 5001
)

type ReportSolve struct {
	UserId         string                  // 举报人用户id
	Target         ReportTarget            // 被举报对象
	TargetId       string                  // 动态id、直播间id、userId、userId、评论id
	TargetUserId   string                  // 被举报对象的userid
	Operation      map[ReportOperation]int // map的键为操作，map的值为封禁时间，分钟级，永久为-1，默认为0
	TargetUserLang string                  // 被举报用户语言
	ReportType     string                  // 被举报类型
	OperatorId     string                  // 操作人userId
}
