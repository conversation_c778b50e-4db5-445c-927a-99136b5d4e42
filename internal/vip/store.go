package vip

import (
	"context"

	"gitlab.sskjz.com/go/cc"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
)

type Store interface {
	GetUser(ctx context.Context, userId string) (*User, error)
}

type store struct {
	mc    *db.MongoClient
	cache cc.Cache[string, *User]
}

func (s *store) GetUser(ctx context.Context, userId string) (*User, error) {
	return s.cache.Get(userId)
}

func (s *store) InvaildUser(ctx context.Context, userId string) {
	s.cache.Remove(userId)
}
