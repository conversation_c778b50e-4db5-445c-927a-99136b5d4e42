package room

import (
	"fmt"

	"gitlab.sskjz.com/overseas/live/osl/pkg/protocol"
)

type TextMsg struct {
	Level   protocol.PayChatLevel
	Content string
	Quotes  []string
}

type StickerMsg struct {
	Sticker string
}

type ChatMsg struct {
	Text    *TextMsg
	Sticker *StickerMsg
}

func (c *ChatMsg) String() string {
	if c.Text != nil {
		return fmt.Sprintf("Text:%d:%s", c.Text.Level, c.Text.Content)
	}
	return "Sticker:" + c.Sticker.Sticker
}

func NewTextMsg(content string, level protocol.PayChatLevel, quotes []string) *ChatMsg {
	return &ChatMsg{
		Text: &TextMsg{
			Content: content,
			Level:   level,
			Quotes:  quotes,
		},
	}
}

func NewStickerMsg(sticker string) *ChatMsg {
	return &ChatMsg{
		Sticker: &StickerMsg{
			Sticker: sticker,
		},
	}
}
