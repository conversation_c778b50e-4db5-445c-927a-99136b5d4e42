package interact

import (
	"fmt"
	"time"

	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/protocol"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func PayChatLogCollection(t time.Time) string {
	return fmt.Sprintf("chat.logs.%s", t.Local().Format("200601"))
}

type PayChatLog struct {
	ID        primitive.ObjectID    `bson:"_id"`
	UserId    string                `bson:"userId"`
	Content   string                `bson:"content"`
	Level     protocol.PayChatLevel `bson:"level"`
	CreatedAt time.Time             `bson:"createdAt"`
}

var payChatLogIndexers = []db.Indexer{
	{
		Name: "userId_createdAt",
		Keys: bson.D{
			{Key: "userId", Value: 1},
			{Key: "createdAt", Value: -1},
		},
	},
}
