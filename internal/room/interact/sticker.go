package interact

const (
	StickerNielianshengji      = 1
	StickerDaxiangmao          = 2
	StickerCaihongtu           = 3
	StickerPaopaotoutao        = 4
	StickerLarijianbianmojing  = 5
	StickerRangnifacaideyingwu = 6
)

type StickerMapping func(giftId int, pow int) (stickerId int)

func StaticMapping(stickerId int) StickerMapping {
	return func(giftId int, pow int) int {
		return stickerId
	}
}

func LuckPointMapping(stickerId int, minPow int) StickerMapping {
	return func(giftId int, pow int) int {
		if pow >= minPow {
			return stickerId
		}
		return 0
	}
}

/*
10096：捏脸 ---  捏脸
10097：泡泡 ---  泡泡
10098：朋克兔
10099：大象帽
10100：表情墨镜
*/
var (
	StickerGifts = map[int]StickerMapping{
		11:    LuckPointMapping(StickerRangnifacaideyingwu, 500),
		10096: StaticMapping(StickerNielianshengji),
		// 10097: StaticMapping(StickerPaopaotoutao),
		10098: StaticMapping(StickerCaihongtu),
		10099: StaticMapping(StickerDaxiangmao),
		10100: StaticMapping(StickerLarijianbianmojing),
	}
)

func GetStickerId(giftId int, pow int) int {
	m, ok := StickerGifts[giftId]
	if !ok {
		return 0
	}
	return m(giftId, pow)
}
