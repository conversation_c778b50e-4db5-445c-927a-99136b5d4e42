package snapshot

import (
	"context"

	"gitlab.sskjz.com/go/cron"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/overseas/live/osl/internal/agency"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/env"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"go.uber.org/zap"
)

func Invoke(sch *cron.Scheduler, db *db.Client, ug user.Getter, fm *fund.Manager, vnd log.Vendor) {
	if !env.Manage() {
		return
	}

	logger := vnd.Scope("fund.snapshot")

	task := sch.Exclusive("agency.chief.snapshot", func(ctx context.Context) error {
		var agencies []*agency.Agency
		if err := db.Where("status = ?", 0).Find(&agencies).Error; err != nil {
			return err
		}
		for _, agc := range agencies {
			if b, _ := fm.Take(ctx, agc.ChiefId); b != nil {
				if acc, _ := ug.Account(ctx, b.UserId); acc != nil {
					logger.Info("balance snapshot", zap.String("userId", b.UserId),
						zap.String("showId", acc.ShowId), zap.String("nickname", acc.Nickname),
						zap.Int64("diamonds", b.Diamonds.IntPart()),
						zap.Float64("fruits", b.Fruits.InexactFloat64()),
					)
				}
			}
		}
		return nil
	})

	_, _ = sch.CronWithSeconds("0 0 3 * * 1").Do(task)
}
