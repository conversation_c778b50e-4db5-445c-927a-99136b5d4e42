package game

import (
	"gitlab.sskjz.com/overseas/live/osl/pkg/app"
)

var gameBlackUserIds []string = []string{
	"f98641461dc443aea6462cf448a22860", // android 审核
	"85af179c051b4c27b67fc7e9d68440e5", // ios 审核
	"d427c017d10b4ac7915936d6d5ce9925", // ios 审核 巴西
}

var devUserShowIds []string = []string{
	"10251056", // 陈阳
}

var gameWhiteShowIds []string = []string{
	"10088821",
	"10146785",
	"10455597",
	"10463540",
}

var gameBlackShowIds []string = []string{
	"10765749",
	"10991102",
	"10326968",
	"10133407",
	"10171188",
	"10354612",
	"10319235",
}

func Open(hdr app.HdrGetter) bool {
	return app.CVGTE(hdr, app.V110, 22)
}
