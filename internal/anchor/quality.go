package anchor

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/samber/lo"
	"gitlab.sskjz.com/overseas/live/osl/internal/agency"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/internal/live"
	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/zap"
)

type GradeTask struct {
	Duration     time.Duration // 需要直播时长
	AnchorIncome fund.Decimal  // 主播薪资，水晶
	AgencyIncome fund.Decimal  // 公会长薪资，水晶
}

type GradeTaskMap map[string]GradeTask

func (m GradeTaskMap) Get(grade string) (GradeTask, bool) {
	task, ok := m[grade]

	if !ok {
		return GradeTask{}, false
	}

	return task, true
}

// 优质主播薪资等级
var (
	gradeTasks = GradeTaskMap{
		AnchorQualitySalaryGradeA:  {Duration: time.Hour * 3, AnchorIncome: fund.New(50000), AgencyIncome: fund.New(10000)},
		AnchorQualitySalaryGradeS:  {Duration: time.Hour * 3, AnchorIncome: fund.New(100000), AgencyIncome: fund.New(20000)},
		AnchorQualitySalaryGradeSS: {Duration: time.Hour * 4, AnchorIncome: fund.New(150000), AgencyIncome: fund.New(30000)},
	}
)

// 发放水晶时的流水详细描述
const (
	anchorDetailRemark = `Suporte do Novo Host`
	agencyDetailRemark = `Suporte do Host`
)

// 系统消息文案
const (
	// 主播收到优质主播薪资通知
	anchorNotice = `Parabéns por completar a tarefa de apoio de novo host e ganhou %d cristais adicionais. A recompensa foi enviada para sua conta, por favor verifique!`
	// 公会长收到优质主播汇总薪资通知
	agencyNotice = `Parabéns por seu host completar a live e você ganhou %d cristais. A recompensa foi enviada para sua conta, por favor verifique! `
	// 公会长收到优质主播代收薪资通知
	agencyNoticeBehalf = `Parabéns por seu host completar a live e ganhou %d cristais adicionais. O suporte foi enviado para sua conta, por favor verifique!`
)

// 优质主播薪资数据列表
func (m *Manager) ManageQualitySalaryList(
	ctx context.Context,
	userId, agencyUserId, fromDay, toDay string, // yyyyMMdd
	page, pageSize int64,
) ([]AnchorQualitySalary, int64, error) {
	collection := m.db.Collection(AnchorQualitySalaryCollectionName())

	filter := bson.M{
		"$and": []bson.M{
			{"day": bson.M{"$gte": fromDay}},
			{"day": bson.M{"$lte": toDay}},
		},
	}

	if userId != "" {
		filter["userId"] = userId
	}

	if agencyUserId != "" {
		filter["agencyUserId"] = agencyUserId
	}

	var total int64

	total, err := collection.CountDocuments(ctx, filter)

	if err != nil {
		return nil, 0, err
	}

	// 下载数据
	if pageSize > 10000 {
		if total > pageSize {
			return nil, 0, biz.NewError(biz.ErrBusiness, "下载数据量超过限制，减少查询天数")
		}
	}

	var list []AnchorQualitySalary

	cur, err := collection.Find(
		ctx,
		filter,
		options.Find().SetSkip((page-1)*pageSize).SetLimit(pageSize).SetSort(bson.D{
			{Key: "day", Value: -1},
			{Key: "luckDiamond", Value: -1},
		}),
	)

	if err != nil {
		return nil, 0, err
	}

	defer cur.Close(ctx)

	err = cur.All(ctx, &list)

	if err != nil {
		return nil, 0, err
	}

	return list, total, err
}

// 优质主播日薪资集合列表
func (m *Manager) ManageQualitySalaryGroupList(ctx context.Context, page, pageSize int64) ([]AnchorQualitySalaryGroup, int64, error) {
	collection := m.db.Collection(AnchorQualitySalaryGroupCollectionName())

	filter := bson.M{}

	var list []AnchorQualitySalaryGroup

	cur, err := collection.Find(
		ctx,
		filter,
		options.
			Find().
			SetSkip((page-1)*pageSize).
			SetLimit(pageSize).
			SetSort(bson.D{
				{Key: "day", Value: -1},
			}),
	)

	if err != nil {
		return nil, 0, err
	}

	defer cur.Close(ctx)

	err = cur.All(ctx, &list)

	if err != nil {
		return nil, 0, err
	}

	var total int64

	total, err = collection.CountDocuments(ctx, filter)

	if err != nil {
		return nil, 0, err
	}

	return list, total, nil
}

func (m *Manager) ManageQualitySalaryGroupIssue(ctx context.Context, auditUserId, day string) error {
	res, err := m.db.Collection(AnchorQualitySalaryGroupCollectionName()).UpdateOne(
		ctx,
		bson.M{
			"day":    day,
			"status": AnchorQualitySalaryStatusPending,
		},
		bson.M{
			"$set": bson.M{
				"status":  AnchorQualitySalaryStatusDoing,
				"auditBy": auditUserId,
				"auditAt": time.Now(),
			},
		},
	)

	if err != nil {
		return err
	}

	if res.MatchedCount == 0 {
		return errors.New("quality salary issue not found")
	}

	if res.ModifiedCount == 0 {
		return errors.New("quality salary issue not modified")
	}

	return m.submitQualitySalaryIssueTask(ctx, day)
}

// 后台设置优质主播
// 0点～1点不允许设置或者取消：结算时数据不变动
func (m *Manager) ManageSetAnchorQuality(ctx context.Context, anchorId, grade, setType string, payee AnchorQualityPayee) error {
	l, err := m.dm.Lock(ctx, fmt.Sprintf("STR:MUTEX:ANCHOR:QUALITY:%s", anchorId))
	if err != nil {
		return err
	}
	defer l.MustUnlock()

	nh := time.Now().In(ctz.Brazil).Hour()

	if nh == 0 {
		return errors.New("0点到1点不允许设置或者取消，正在结算优质主播薪资")
	}

	logger := m.log.With(
		zap.String("anchorId", anchorId),
		zap.String("grade", grade),
		zap.String("setType", setType),
		zap.String("payee", string(payee)),
	)

	switch setType {
	case "add":
		// 是否有公会
		_, err := m.am.GetAgencyByMember(anchorId)

		if err != nil {
			if err == agency.ErrNotJoinAgency {
				return errors.New("主播未加入公会")
			}

			return fmt.Errorf("获取主播公会信息失败: %w", err)
		}

		// 设置优质主播
		if !lo.Contains([]string{AnchorQualitySalaryGradeA, AnchorQualitySalaryGradeS, AnchorQualitySalaryGradeSS}, grade) {
			return errors.New("定级级别错误")
		}

		if !lo.Contains([]AnchorQualityPayee{AnchorQualityPayeeAnchor, AnchorQualityPayeeAgency}, payee) {
			return errors.New("payee error")
		}

		room, err := m.lm.RoomByUserId(ctx, anchorId)

		if err != nil {
			return err
		}

		err = m.lm.AddFlag(ctx, room.Id.Hex(), live.RoomFlagQuality)

		if err != nil {
			logger.Error("设置优质主播失败", zap.Error(err))

			return err
		}

		err = m.TerminateSupport(ctx, anchorId, "标记为优质主播", TerminateReasonTypeQuality, "")

		if err != nil {
			logger.Error("取消新主播扶持失败", zap.Error(err))
		}

		set := bson.M{
			"grade":     grade,
			"payee":     payee,
			"gradeAt":   time.Now(),
			"updatedAt": time.Now(),
		}

		// 获取原值
		var data AnchorQuality

		err = m.db.Collection(AnchorQualityCollectionName()).FindOne(
			ctx,
			bson.M{
				"_id": anchorId,
			},
		).Decode(&data)

		if err != nil {
			if err != mongo.ErrNoDocuments {
				return err
			}
		} else {
			// 如果等级与原等级相同，则不更新 gradeAt 字段
			if data.Grade == grade {
				delete(set, "gradeAt")
			}
		}

		_, err = m.db.Collection(AnchorQualityCollectionName()).UpdateOne(
			ctx,
			bson.M{
				"_id": anchorId,
			},
			bson.M{
				"$set": set,
				"$setOnInsert": bson.M{
					"createdAt": time.Now(),
				},
			},
			options.Update().SetUpsert(true),
		)

		if err != nil {
			logger.Error("设置优质主播失败", zap.Error(err))

			return err
		}
	case "remove":
		// 取消优质主播
		err = m.removeAnchorQuality(ctx, anchorId)

		if err != nil {
			logger.Error("取消优质主播失败", zap.Error(err))

			return err
		}
	default:
		return errors.New("setType error")
	}

	logger.Info("设置优质主播")

	return nil
}

func (m *Manager) removeAnchorQuality(ctx context.Context, anchorId string) error {
	room, err := m.lm.RoomByUserId(ctx, anchorId)

	if err != nil {
		return fmt.Errorf("获取主播房间信息失败: %w", err)
	}

	// 取消优质主播
	err = m.lm.RemoveFlag(ctx, room.Id.Hex(), live.RoomFlagQuality)

	if err != nil {
		m.log.Error("取消优质主播失败1", zap.Error(err), zap.String("anchorId", anchorId))
	}

	_, err = m.db.Collection(AnchorQualityCollectionName()).DeleteOne(
		ctx,
		bson.M{
			"_id": anchorId,
		},
	)

	if err != nil {
		m.log.Error("取消优质主播失败2", zap.Error(err), zap.String("anchorId", anchorId))
	}

	return err
}

var (
	ErrAnchorQualityNotFound = errors.New("主播优质主播信息不存在")
)

func (m *Manager) GetAnchorQuality(ctx context.Context, anchorId string) (*AnchorQuality, error) {
	var data AnchorQuality

	err := m.db.Collection(AnchorQualityCollectionName()).FindOne(
		ctx,
		bson.M{
			"_id": anchorId,
		},
	).Decode(&data)

	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, ErrAnchorQualityNotFound
		}

		return nil, fmt.Errorf("获取主播优质主播信息失败: %w", err)
	}

	return &data, nil
}
