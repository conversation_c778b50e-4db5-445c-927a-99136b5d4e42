package anchor

import (
	"context"
	"fmt"
	"time"

	"github.com/jinzhu/now"
	"gitlab.sskjz.com/overseas/live/osl/internal/agency"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/zap"
	"golang.org/x/time/rate"
)

var (
	settleLimiter = rate.NewLimiter(rate.Every(time.Second/100), 100)
)

func (m *Manager) SettleAnchorQualityTask() error {
	ctx := context.Background()

	nn := now.New(time.Now().In(ctz.Brazil))

	settleStartTime := nn.BeginningOfDay().AddDate(0, 0, -1)
	settleEndTime := nn.EndOfDay().AddDate(0, 0, -1)

	// 结算日期
	settleDay := settleStartTime.Format("20060102")
	lastSettleDay := settleStartTime.AddDate(0, 0, -1).Format("20060102")

	var err error

	defer func() {
		if err != nil {
			m.log.Error("settle anchor quality", zap.Error(err))
		}
	}()

	filter := bson.M{
		"$or": []bson.M{
			{"settleDay": bson.M{"$exists": false}},
			{"settleDay": ""},
			{"settleDay": lastSettleDay},
		},
	}

	for {
		// for循环已执行超过30分钟，退出循环，并报错
		if time.Since(nn.Time) > time.Minute*30 {
			m.log.Error("当前已超过优质主播结算时间")

			return fmt.Errorf("当前已超过优质主播结算时间")
		}

		cnt, err := m.db.Collection(AnchorQualityCollectionName()).CountDocuments(
			ctx,
			filter,
		)

		if err != nil {
			return fmt.Errorf("count anchor quality: %w", err)
		}

		if cnt == 0 {
			m.log.Info("没有需要结算的优质主播")

			break
		}

		err = m.SettleAnchorQuality(
			ctx,
			filter,
			settleDay,
			settleStartTime,
			settleEndTime,
		)

		if err != nil {
			return fmt.Errorf("settle anchor quality: %w", err)
		}
	}

	r1, err := m.QualityAggregate1(ctx, settleDay)

	if err != nil {
		m.log.Error(
			"优质主播薪资汇总失败",
			zap.Error(err),
			zap.String("settleDay", settleDay),
		)

		return fmt.Errorf("quality aggregate 1: %w", err)
	}

	r2, err := m.QualityAggregate2(ctx, settleDay)

	if err != nil {
		m.log.Error(
			"优质主播薪资汇总失败",
			zap.Error(err),
			zap.String("settleDay", settleDay),
		)

		return fmt.Errorf("quality aggregate 2: %w", err)
	}

	groupCollection := m.db.Collection(AnchorQualitySalaryGroupCollectionName())

	cnt, err := groupCollection.CountDocuments(
		ctx,
		bson.M{
			"day": settleDay,
			"status": bson.M{
				"$in": []AnchorQualitySalaryStatus{
					AnchorQualitySalaryStatusDoing,
					AnchorQualitySalaryStatusDone,
				},
			},
		},
	)

	if err != nil {
		m.log.Error(
			"查询优质主播薪资汇总失败",
			zap.Error(err),
			zap.String("settleDay", settleDay),
		)
		return fmt.Errorf("count anchor quality salary group: %w", err)
	}

	if cnt > 0 {
		m.log.Info(
			"优质主播薪资汇总已存在",
			zap.Int64("count", cnt),
			zap.String("settleDay", settleDay),
		)

		return nil
	}

	// 汇总所有数据到group
	_, err = groupCollection.UpdateOne(
		ctx,
		bson.M{
			"day": settleDay,
		},
		bson.M{
			"$set": bson.M{
				"ctz":              ctz.New(ctz.BR, ctz.Brazil.String()),
				"luckDiamond":      r1.LuckDiamond,
				"giftDiamond":      r1.GiftDiamond,
				"anchorGradeCount": r2.AnchorGradeCount,
				"anchorIncome":     r1.AnchorIncome,
				"agencyIncome":     r1.AgencyIncome,
				"status":           AnchorQualitySalaryStatusPending,
			},
		},
		options.Update().SetUpsert(true),
	)

	if err != nil {
		m.log.Error(
			"更新优质主播薪资汇总失败",
			zap.Error(err),
			zap.String("settleDay", settleDay),
		)

		return fmt.Errorf("update anchor quality salary group: %w", err)
	}

	m.log.Info(
		"更新优质主播薪资汇总成功",
		zap.String("settleDay", settleDay),
	)

	return nil
}

func (m *Manager) SettleAnchorQuality(
	ctx context.Context,
	filter bson.M,
	settleDay string,
	settleStartTime, settleEndTime time.Time,
) error {
	logger := m.log.With(
		zap.String("settleDay", settleDay),
		zap.Time("settleStartTime", settleStartTime),
		zap.Time("settleEndTime", settleEndTime),
	)

	qualityCollection := m.db.Collection(AnchorQualityCollectionName())

	// 读取优质主播表
	cur, err := qualityCollection.Find(
		ctx,
		filter,
		options.Find().SetLimit(1000),
	)

	if err != nil {
		return fmt.Errorf("find anchor quality: %w", err)
	}

	defer cur.Close(ctx)

	if cur.RemainingBatchLength() == 0 {
		logger.Info("没有需要结算的优质主播")

		return nil
	}

	aqsc := m.db.Collection(AnchorQualitySalaryCollectionName())

	for cur.Next(ctx) {
		settleLimiter.Wait(ctx)

		var aq AnchorQuality

		err = cur.Decode(&aq)

		if err != nil {
			logger.Error(
				"decode anchor quality",
				zap.Error(err),
			)

			continue
		}

		userId := aq.Id

		// 已经有薪资记录的不处理
		if cnt, err := aqsc.CountDocuments(
			ctx,
			bson.M{
				"userId": userId,
				"day":    settleDay,
			},
		); err != nil {
			logger.Error(
				"find anchor quality salary",
				zap.String("userId", userId),

				zap.Error(err),
			)

			continue
		} else if cnt > 0 {
			continue
		}

		// 根据主播等级获取任务
		grade := aq.Grade
		gradeAt := aq.GradeAt

		task, ok := gradeTasks[grade]

		if !ok {
			logger.Error(
				"grade not found",
				zap.String("userId", userId),
				zap.String("grade", grade),
			)

			continue
		}

		// 获取主播有效直播时长
		durationSeconds, err := m.lm.GetLiveValidDuration(ctx, userId, settleStartTime, settleEndTime)

		if err != nil {
			logger.Error(
				"get live valid duration",
				zap.String("userId", userId),
				zap.Error(err),
			)

			continue
		}

		duration := time.Duration(durationSeconds) * time.Second

		var luckDiamond, giftDiamond, anchorIncome, agencyIncome int64

		// 完成任务时长要求
		if duration >= task.Duration {
			anchorIncome = task.AnchorIncome.IntPart()
			agencyIncome = task.AgencyIncome.IntPart()
		}

		// 获取流水数据
		data, err := m.lsrm.GetLiveSummaryDay(ctx, userId, settleStartTime)

		if err == nil {
			luckDiamond = data.LuckDiamond
			giftDiamond = data.GiftDiamond
		} else {
			if err != mongo.ErrNoDocuments {
				logger.Error(
					"get live summary day",
					zap.String("userId", userId),
					zap.Error(err),
				)

				continue
			}
		}

		var agencyId int64
		// 公会长ID
		var agencyUserId string

		// 按结算时的所属公会（可能在0点几分的时候退出了公会）
		aa, err := m.am.GetAgencyByMember(userId)

		if err != nil {
			if err == agency.ErrNotJoinAgency {
				// 优质主播没有公会异常记录一下
				logger.Error(
					"优质主播没有加入公会",
					zap.String("userId", userId),
					zap.Error(err),
				)
			} else {
				logger.Error(
					"get agency by member",
					zap.String("userId", userId),
					zap.Error(err),
				)

				continue
			}
		} else {
			agencyId = int64(aa.ID)
			agencyUserId = aa.ChiefId
		}

		// 当优质主播为公会长时，不发放优质主播公会长工资
		if agencyUserId == userId {
			agencyIncome = 0
		}

		loggerNew := logger.With(
			zap.String("agencyUserId", agencyUserId),
			zap.Int64("luckDiamond", luckDiamond),
			zap.Int64("giftDiamond", giftDiamond),
			zap.Int64("anchorIncome", anchorIncome),
			zap.Int64("agencyIncome", agencyIncome),
			zap.Duration("duration", duration),
		)

		_, err = aqsc.InsertOne(
			ctx,
			AnchorQualitySalary{
				Id:           primitive.NewObjectID(),
				UserId:       userId,
				Day:          settleDay,
				CTZ:          ctz.New(ctz.BR, ctz.Brazil.String()),
				AgencyId:     agencyId,
				AgencyUserId: agencyUserId,
				Grade:        grade,
				GradeAt:      gradeAt,
				LuckDiamond:  luckDiamond,
				GiftDiamond:  giftDiamond,
				Duration:     int64(duration.Seconds()),
				AnchorIncome: anchorIncome,
				AgencyIncome: agencyIncome,
				Payee:        aq.Payee,
				Status:       AnchorQualitySalaryStatusPending,
				UT:           primitive.NewObjectID().Hex(),
			},
		)

		if err != nil {
			loggerNew.Error("插入优质主播薪资失败", zap.Error(err))

			continue
		}

		loggerNew.Info("插入优质主播薪资成功")

		if _, err = qualityCollection.UpdateOne(
			ctx,
			bson.M{
				"_id": userId,
			},
			bson.M{
				"$set": bson.M{
					"settleDay": settleDay,
					"updatedAt": time.Now().In(ctz.Brazil),
				},
			},
		); err != nil {
			loggerNew.Error("更新优质主播结算日期失败", zap.Error(err))
		}
	}

	return nil
}
