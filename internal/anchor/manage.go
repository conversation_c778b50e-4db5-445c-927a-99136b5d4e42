package anchor

import (
	"context"
	"fmt"
	"time"

	"github.com/jinzhu/now"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/zap"
)

// 后台接口

// 流水考核列表
func (m *Manager) ManangeFundList(
	ctx context.Context,
) {
}

// 扶持政策列表
func (m *Manager) ManageSupportList(
	ctx context.Context,
	anchorIds []string,
	startTime, endTime time.Time,
	source string,
	status *int,
	page int64,
	pageSize int64,
) ([]AnchorEvaluationSupport, int64, error) {
	var total int64

	filter := bson.M{}

	if len(anchorIds) > 0 {
		filter["_id"] = bson.M{"$in": anchorIds}
	}

	if source != "" {
		filter["source"] = source
	}

	if status != nil {
		filter["status"] = *status
	}

	timeConditions := bson.A{}

	if !startTime.IsZero() {
		timeConditions = append(timeConditions, bson.M{"createdAt": bson.M{"$gte": startTime}})
	}

	if !endTime.IsZero() {
		timeConditions = append(timeConditions, bson.M{"createdAt": bson.M{"$lte": endTime}})
	}

	if len(timeConditions) > 0 {
		filter["$and"] = timeConditions
	}

	cursor, err := m.db.Collection(AnchorEvaluationSupportCollectionName()).Find(
		ctx,
		filter,
		options.Find().SetSort(bson.M{"createdAt": -1}).SetSkip((page-1)*pageSize).SetLimit(pageSize),
	)

	if err != nil {
		return nil, total, err
	}

	var ret []AnchorEvaluationSupport

	err = cursor.All(ctx, &ret)

	if err != nil {
		return nil, total, err
	}

	total, err = m.db.Collection(AnchorEvaluationSupportCollectionName()).CountDocuments(ctx, filter)

	if err != nil {
		return nil, total, err
	}

	return ret, total, nil
}

// 扶持政策详情
func (m *Manager) ManageSupportDayList(
	ctx context.Context,
	anchorId string,
) ([]AnchorEvaluationSupportDay, error) {
	var ret []AnchorEvaluationSupportDay

	cursor, err := m.db.Collection(AnchorEvaluationSupportDayCollectionName()).Find(
		ctx,
		bson.M{
			"userId": anchorId,
		},
		options.Find().SetSort(bson.M{"date": 1}),
	)

	if err != nil {
		return nil, err
	}

	defer cursor.Close(ctx)

	err = cursor.All(ctx, &ret)

	if err != nil {
		return nil, err
	}

	return ret, nil
}

// 取消扶持
func (m *Manager) TerminateSupport(
	ctx context.Context,
	userId string,
	reason string,
	reasonType int,
	noticeContent string,
) error {
	nt := now.With(time.Now().In(ctz.Brazil))

	ur, err := m.db.Collection(AnchorEvaluationSupportCollectionName()).UpdateOne(
		ctx,
		bson.M{
			"_id": userId,
			"status": bson.M{
				"$in": []int{
					AnchorEvaluationSupportStatusNotStart,
					AnchorEvaluationSupportStatusPending,
					AnchorEvaluationSupportStatusPause,
				},
			},
		},
		bson.M{
			"$set": bson.M{
				"terminateTime": nt.Time,
				"status":        AnchorEvaluationSupportStatusTerminate,
				"reason":        reason,
				"reasonType":    reasonType,
				"updatedAt":     nt.Time,
			},
		},
	)

	m.delSupoprtKey(ctx, userId)

	// 发送通知
	if noticeContent != "" {
		m.imm.SendSystemNoticeTextToUser(
			ctx,
			userId,
			noticeContent,
		)
	}

	if err == nil && ur.MatchedCount > 0 && ur.ModifiedCount > 0 {
		m.log.Info(
			"终止扶持",
			zap.String("userId", userId),
			zap.String("reason", reason),
			zap.Int("count", reasonType),
			zap.Any("response", ur),
			zap.Error(err),
		)
	}

	return err
}

// 暂停扶持
func (m *Manager) PauseSupport(
	ctx context.Context,
	userId string,
) error {
	nt := now.With(time.Now().In(ctz.Brazil))

	sp, err := m.StatusSupport(ctx, userId)

	if err != nil {
		return err
	}

	if !sp.Aes.IsPending() {
		return fmt.Errorf("只有进行中状态才能暂停")
	}

	ur, err := m.db.Collection(AnchorEvaluationSupportCollectionName()).UpdateOne(
		ctx,
		bson.M{
			"_id":    userId,
			"status": AnchorEvaluationSupportStatusPending,
		},
		bson.M{
			"$set": bson.M{
				"pauseDate": nt.BeginningOfDay(),
				"pauseTime": nt.Time,
				"status":    AnchorEvaluationSupportStatusPause,
				"updatedAt": nt.Time,
			},
		},
	)

	m.delSupoprtKey(ctx, userId)

	m.log.Info(
		"暂停扶持",
		zap.String("userId", userId),
		zap.Any("response", ur),
		zap.Error(err),
	)

	return err
}

// 恢复扶持
func (m *Manager) ResumeSupport(
	ctx context.Context,
	userId string,
) error {
	nt := now.With(time.Now().In(ctz.Brazil))

	sp, err := m.StatusSupport(ctx, userId)

	if err != nil {
		return err
	}

	if !sp.Aes.IsPause() {
		return fmt.Errorf("只有暂停状态才能恢复")
	}

	// 恢复的数据
	supportSet := bson.M{
		"pauseDate": time.Time{},
		"pauseTime": time.Time{},
		"status":    AnchorEvaluationSupportStatusPending,
		"updatedAt": nt.Time,
	}

	// 暂停日期
	pauseDate := now.New(sp.Aes.PauseDate.In(ctz.Brazil)).BeginningOfDay()
	// 恢复日期
	resumeStartDate := now.With(nt.In(ctz.Brazil)).BeginningOfDay()
	// 已经扶持的天数
	var hasSupportDays int
	for _, v := range sp.List {
		if !v.Date.After(pauseDate) {
			hasSupportDays++
		}
	}
	// 结束时间
	endTime := sp.Aes.EndTime.In(ctz.Brazil)
	// 最后一天暂停只能当天恢复，次日之后恢复不会创建新的天
	remainDays := SupportDays - hasSupportDays
	if remainDays > 0 {
		endTime = resumeStartDate.AddDate(0, 0, remainDays)
		// 重新计算结束时间
		supportSet["endTime"] = endTime
	}

	collection1 := m.db.Collection(AnchorEvaluationSupportCollectionName())
	collection2 := m.db.Collection(AnchorEvaluationSupportDayCollectionName())

	// 事务
	return m.db.TryTxn(ctx, func(ctx context.Context) error {
		ur, err := collection1.UpdateOne(
			ctx,
			bson.M{
				"_id":    userId,
				"status": AnchorEvaluationSupportStatusPause,
				"pauseTime": bson.M{
					"$lt": nt.Time, // 暂停时间在当前操作时间之前
				},
			},
			bson.M{
				"$set": supportSet,
			},
		)

		if err != nil {
			return err
		}

		if ur.MatchedCount == 0 || ur.ModifiedCount == 0 {
			return fmt.Errorf("没有更新到数据")
		}

		cur, err := collection2.Find(
			ctx,
			bson.M{
				"userId": userId,
			},
			options.Find().SetSort(bson.M{"date": 1}),
		)

		if err != nil {
			return err
		}

		defer cur.Close(ctx)

		var list []AnchorEvaluationSupportDay

		err = cur.All(ctx, &list)

		if err != nil {
			return err
		}

		delIds := make([]primitive.ObjectID, 0)

		// 暂停和恢复在同一天，不用处理天数据
		if resumeStartDate.After(pauseDate) {
			for _, v := range list {
				// 暂停时间之前的数据不变化
				if v.Date.After(pauseDate) {
					delIds = append(delIds, v.Id)
				}
			}
		}

		if len(delIds) > 0 {
			_, err := collection2.DeleteMany(
				ctx,
				bson.M{
					"_id": bson.M{"$in": delIds},
				},
			)

			if err != nil {
				return err
			}

			cursorDay := 0
			// 生成新的天数
			for i := hasSupportDays + 1; i <= SupportDays; i++ {
				var awards []SupportAward

				if i <= 5 {
					awards = SupportAwardsB5
				} else {
					awards = SupportAwardsA5
				}

				_, err := m.db.Collection(AnchorEvaluationSupportDayCollectionName()).InsertOne(
					ctx,
					&AnchorEvaluationSupportDay{
						Id:          primitive.NewObjectID(),
						UserId:      userId,
						Date:        resumeStartDate.AddDate(0, 0, cursorDay),
						Day:         i,
						Duration:    0,
						LuckDiamond: 0,
						Awards:      awards,
					},
				)

				if err != nil {
					return err
				}

				cursorDay++
			}
		}

		// 重新生成有效期
		m.rc.Set(ctx, fmt.Sprintf(keySupportPending, userId), 1, time.Until(endTime))

		m.log.Info(
			"恢复扶持",
			zap.String("userId", userId),
			zap.Any("response", ur),
			zap.Time("pauseDate", pauseDate),
			zap.Time("startTime", resumeStartDate),
			zap.Time("endTime", endTime),
			zap.Int("remainDays", remainDays),
			zap.Int("hasSupportDays", hasSupportDays),
			zap.Any("supportSet", supportSet),
			zap.Error(err),
		)

		return nil
	})
}
