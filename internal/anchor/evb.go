package anchor

import (
	"context"
	"fmt"
	"time"

	"github.com/jinzhu/now"
	"gitlab.sskjz.com/overseas/live/osl/internal/evt"
	"gitlab.sskjz.com/overseas/live/osl/internal/live"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

func (m *Manager) onLiveStart(ctx context.Context, evd *evt.StartLive) error {
	af, err := m.GetAnchorFlags(evd.UserId)

	if err != nil {
		return err
	}

	if af.IsNewAnchorFlag() {
		return m.lm.AddFlag(ctx, evd.RoomId, live.RoomFlagNewAnchor)
	}

	return m.lm.RemoveFlag(ctx, evd.RoomId, live.RoomFlagNewAnchor)
}

func (m *Manager) onGiftSentAdvanced(ctx context.Context, evd *evt.SendGiftAdvanced) error {
	if !evd.Gift.Lucky {
		return nil
	}

	anchorId := evd.AnchorId

	logger := m.log.With(
		zap.String("anchorId", anchorId),
		zap.Int("diamond", evd.Diamond),
	)

	// 流水考核主播
	if m.rc.Exists(ctx, fmt.Sprintf(keyFundExamine, anchorId)).Val() == 1 {
		m.db.Collection(AnchorEvaluationFundCollectionName()).UpdateOne(
			ctx,
			bson.M{
				"_id":    anchorId,
				"status": AnchorEvaluationStatusPending,
			},
			bson.M{
				"$inc": bson.M{
					"luckDiamond": evd.Diamond,
				},
			},
		)

		logger.Info("新主播流水考核")
	}

	// 扶持政策主播
	if m.rc.Exists(ctx, fmt.Sprintf(keySupportPending, anchorId)).Val() == 1 {
		todayDate := now.With(time.Now().In(ctz.Brazil)).BeginningOfDay()

		// 当日的幸运礼物流水值
		m.db.Collection(AnchorEvaluationSupportDayCollectionName()).UpdateOne(
			ctx,
			bson.M{
				"userId": anchorId,
				"date":   todayDate,
			},
			bson.M{
				"$inc": bson.M{
					"luckDiamond": evd.Diamond,
				},
			},
		)

		logger.Info("新主播扶持")
	}

	return nil
}

// 主播退出公会
func (m *Manager) onAgencyMemberQuit(ctx context.Context, evd *evt.AgencyMemberQuitEvt) error {
	err := m.removeAnchorQuality(ctx, evd.UserId)

	if err != nil {
		m.log.Error("退出公会取消优质主播失败",
			zap.Error(err),
			zap.String("userId", evd.UserId),
			zap.Int64("agencyId", int64(evd.AgencyId)),
		)
	}

	m.log.Info("退出公会取消优质主播成功",
		zap.String("userId", evd.UserId),
		zap.Int64("agencyId", int64(evd.AgencyId)),
	)

	return err
}
