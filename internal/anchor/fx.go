package anchor

import (
	"context"

	"gitlab.sskjz.com/go/cc"
	"gitlab.sskjz.com/go/cron"
	"gitlab.sskjz.com/go/dq"
	"gitlab.sskjz.com/go/ev"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/go/mq"
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/agency"
	"gitlab.sskjz.com/overseas/live/osl/internal/evt"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund/order"
	"gitlab.sskjz.com/overseas/live/osl/internal/im"
	"gitlab.sskjz.com/overseas/live/osl/internal/live"
	"gitlab.sskjz.com/overseas/live/osl/internal/live/lsr"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/env"
	"go.mongodb.org/mongo-driver/bson"
)

func Provide(
	dbmc *db.MongoClient,
	rc *redi.Client,
	dm *redi.Mutex,
	lm *live.Manager,
	fm *fund.Manager,
	fom *order.Manager,
	imm *im.Manager,
	am *agency.Manager,
	lsrm *lsr.Manager,
	ev ev.Bus,
	syn cc.Sync,
	dqm *dq.Master,
	vnd log.Vendor,
) (*Manager, error) {
	dbmc.SyncSchema(AnchorEvaluationCollectionName(), 1,
		db.Indexer{Name: "userId", Keys: bson.D{
			{Key: "userId", Value: 1},
		}},
		db.Indexer{Name: "userIp", Keys: bson.D{
			{Key: "userIp", Value: 1},
		}},
		db.Indexer{Name: "userDevice", Keys: bson.D{
			{Key: "userDevice", Value: 1},
		}},
	)

	createAnchorEvaluationSupport(dbmc)
	createAnchorEvaluationSupportDayIndexer(dbmc)
	createAnchorEvaluationFund(dbmc)
	createAnchorQualityIndexer(dbmc)
	createAnchorQualitySalaryIndexer(dbmc)
	createAnchorQualitySalaryGroupIndexer(dbmc)

	m := newManager(dbmc, rc, dm, lm, fm, fom, imm, am, lsrm, ev, syn, vnd.Scope("anchor.mgr"))

	m.initQualitySalaryIssueTask(dqm)

	return m, nil
}

func Invoke(
	sch *cron.Scheduler,
	evb ev.Bus,
	mgr *Manager,
) {
	// 开播事件
	evb.Watch(evt.LiveStart, "anchor.mgr", ev.NewWatcher(mgr.onLiveStart))
	// 退出公会
	evb.Watch(evt.AgencyMemberQuit, "anchor.mgr.amq", ev.NewWatcher(mgr.onAgencyMemberQuit))
}

func InvokeInAPI(sch *cron.Scheduler, evq mq.Queue, mgr *Manager) {
	if !env.APIServer() {
		return
	}

	// 送礼事件
	evt.Watch(evq, evt.GiftSendAdvanced, "anchor.mgr.gift", mgr.onGiftSentAdvanced,
		mq.MaxConcurrency(32), mq.LogCost("evt.gift.send.advanced"),
	)

	// 流水考核检查
	{
		task := sch.Exclusive("anchor.mgr.fund.inspect", func(context.Context) error {
			mgr.inspectFund()

			return nil
		})

		// 每分钟检查
		sch.Cron("*/1 * * * *").Do(task)
	}

	// 扶持任务检查
	{
		task := sch.Exclusive("anchor.mgr.support.inspect", func(context.Context) error {
			mgr.inspectSupport()

			return nil
		})

		// 每分钟检查
		sch.Cron("*/1 * * * *").Do(task)
	}
}

func InvokeInScheduler(sch *cron.Scheduler, evq mq.Queue, mgr *Manager) {
	if !env.Scheduler() {
		return
	}

	// 注册优质主播薪资发放任务
	{
		mgr.registerQualitySalaryIssueTask()
	}

	// 优质主播
	{
		task := sch.Exclusive("anchor.mgr.quality", func(context.Context) error {
			return mgr.SettleAnchorQualityTask()
		})

		sch.Cron("5 3 * * *").Do(task)
	}
}
