package anchor

import (
	"context"
	"time"

	"github.com/jinzhu/now"
	"github.com/samber/lo"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type AnchorFlags struct {
	Id               primitive.ObjectID `bson:"_id"`
	UserId           string             `bson:"userId"` // 主播ID
	IsNewAnchor      bool               `bson:"isNewAnchor"`
	NewAnchorEndTime time.Time          `bson:"newAnchorEndTime"`
	CreateAt         time.Time          `bson:"createAt"`
}

func (f *AnchorFlags) IsNewAnchorFlag() bool {
	return f.IsNewAnchor && time.Now().Before(f.NewAnchorEndTime)
}

// HasBeenEvaluated 是否已经被考核通过
func (f *AnchorFlags) HasBeenEvaluated() bool {
	return f.IsNewAnchor
}

func AnchorFlagsCollectionName() string {
	return "anchor.flags"
}

const (
	AnchorEvaluationPhaseDocument = "document" // 提交资料
	AnchorEvaluationPhaseFund     = "fund"     // 流水考核
	AnchorEvaluationPhaseSupport  = "support"  // 新主播扶持

	AnchorEvaluationStatusPending  = 1  // 待审核
	AnchorEvaluationStatusPass     = 2  // 审核通过
	AnchorEvaluationStatusReject   = -1 // 审核不通过
	AnchorEvaluationStatusNotAllow = -2 // 不允许再次申请
)

// 新主播考核表
type AnchorEvaluation struct {
	Id          primitive.ObjectID `bson:"_id"`
	UserId      string             `bson:"userId"`      // 主播ID
	Phase       string             `bson:"phase"`       // 阶段 document:提交资料 fund:流水考核 support:新主播扶持
	Screenshot  string             `bson:"screenshot"`  // 直播截图
	Video       string             `bson:"video"`       // 直播录屏
	Attachment  string             `bson:"attachment"`  // 附件
	UserIp      string             `bson:"userIp"`      // 用户提交时的ip
	UserDevice  string             `bson:"userDevice"`  // 用户提交时的设备号
	Status      int                `bson:"status"`      // -2:不允许再申请 -1:审核不通过 1:待审核 2:审核通过
	Reason      string             `bson:"reason"`      // 审核不通过原因
	FromFund    bool               `bson:"fromFund"`    // 通过流水考核
	AuditUserId string             `bson:"auditUserId"` // 审核人ID
	AuditedAt   time.Time          `bson:"auditedAt"`   // 审核时间
	CreatedAt   time.Time          `bson:"createdAt"`
	UpdatedAt   time.Time          `bson:"updatedAt"`
}

func (ae *AnchorEvaluation) IsOldRecord() bool {
	if IsV2(ae.CreatedAt) {
		return false
	}

	// 创建时间不是V2的
	// 最后审核时间在V2之后，说明可以进行V2的扶持政策
	if IsV2(ae.AuditedAt) {
		return false
	}

	// 之前有结果的数据，算作老数据
	if lo.Contains([]int{AnchorEvaluationStatusPass, AnchorEvaluationStatusNotAllow}, ae.Status) {
		return true
	}

	return false
}

func AnchorEvaluationCollectionName() string {
	return "anchor.evaluation"
}

const (
	AnchorEvaluationSupportStatusTerminate = -1 // 终止
	AnchorEvaluationSupportStatusNotStart  = 0  // 待开启
	AnchorEvaluationSupportStatusPending   = 1  // 进行中
	AnchorEvaluationSupportStatusPause     = 2  // 已暂停
	AnchorEvaluationSupportStatusEnd       = 3  // 已结束

	TerminateReasonTypeQuality = 1 // 优质主播终止
	TerminateReasonTypeCancel  = 2 // 取消资格终止

	AnchorEvaluationSupportSourceDocument = "document" // 资料考核
	AnchorEvaluationSupportSourceFund     = "fund"     // 流水考核
)

// 新主播扶持表，每天凌晨刷新记录表
type AnchorEvaluationSupport struct {
	Id            string    `bson:"_id" json:"id"`                      // 主播ID
	StartDate     time.Time `bson:"startDate" json:"startDate"`         // 开始时间 零点时间
	PauseDate     time.Time `bson:"pauseDate" json:"pauseDate"`         // 暂停时间 零点时间
	PauseTime     time.Time `bson:"pauseTime" json:"pauseTime"`         // 暂停时间
	TerminateTime time.Time `bson:"terminateTime" json:"terminateTime"` // 终止时间
	EndTime       time.Time `bson:"endTime" json:"endTime"`             // 结束时间
	Status        int       `bson:"status" json:"status"`               // -1终止 0待开启 1进行中 2已暂停 3已结束
	Reason        string    `bson:"reason" json:"reason"`               // 终止原因
	ReasonType    int       `bson:"reasonType" json:"reasonType"`       // 终止原因类型
	Source        string    `bson:"source" json:"source"`               // 来源 document:资料考核 fund:流水考核
	CreatedAt     time.Time `bson:"createdAt" json:"createdAt"`         // 创建时间，即审核（资料/流水）通过时间
	UpdatedAt     time.Time `bson:"updatedAt" json:"updatedAt"`         // 更新时间
}

func (aes *AnchorEvaluationSupport) CanReceive() bool {
	return aes.Status == AnchorEvaluationSupportStatusPending || (aes.IsEnd() && time.Now().Before(aes.EndTime.AddDate(0, 0, 7)))
}

// 是否已结束（终止不算结束）
func (aes *AnchorEvaluationSupport) IsEnd() bool {
	// 暂停的不算结束
	if aes.IsPause() {
		return false
	}

	// 待开启不算结束
	if aes.Status == AnchorEvaluationSupportStatusNotStart {
		return false
	}

	if aes.Status == AnchorEvaluationSupportStatusEnd {
		return true
	}

	if aes.Status == AnchorEvaluationSupportStatusPending {
		return !aes.EndTime.IsZero() && time.Now().After(aes.EndTime)
	}

	return false
}

func (aes *AnchorEvaluationSupport) IsPending() bool {
	return aes.Status == AnchorEvaluationSupportStatusPending
}

func (aes *AnchorEvaluationSupport) IsPause() bool {
	return aes.Status == AnchorEvaluationSupportStatusPause
}

func AnchorEvaluationSupportCollectionName() string {
	return "anchor.evaluation.support"
}

func createAnchorEvaluationSupport(dbmc *db.MongoClient) {
	dbmc.DB().CreateCollection(context.Background(), AnchorEvaluationSupportCollectionName())
}

const (
	SupportDayStatusNotStart   = 1
	SupportDayStatusNotDone    = 2 // 未完成
	SupportDayStatusCanReceive = 3
	SupportDayStatusReceived   = 4
)

// 每日记录（增加统计数据）
type AnchorEvaluationSupportDay struct {
	Id            primitive.ObjectID `bson:"_id" json:"id"`
	UserId        string             `bson:"userId" json:"userId"`               // 主播ID
	Date          time.Time          `bson:"date" json:"date"`                   // 日期 2025-01-01 00:00:00
	Day           int                `bson:"day" json:"day"`                     // 第几天
	Duration      int64              `bson:"duration" json:"duration"`           // 直播时长，单位秒
	LuckDiamond   int64              `bson:"luckDiamond" json:"luckDiamond"`     // 幸运礼物流水，金币
	Awards        []SupportAward     `bson:"awards" json:"awards"`               // 奖励
	Received      bool               `bson:"received" json:"received"`           // 是否已领取奖励
	ReceivedAward *SupportAward      `bson:"receivedAward" json:"receivedAward"` // 已领取的奖励
	ReceivedAt    time.Time          `bson:"receivedAt" json:"receivedAt"`       // 领取时间
	Immutable     bool               `bson:"immutable" json:"immutable"`         // 数据更新完毕，不再变更
}

func (aes *AnchorEvaluationSupportDay) Award() (int, *SupportAward) {
	var ret SupportAward
	var key int

	for k, award := range aes.Awards {
		if award.Duration <= aes.Duration && award.LuckDiamond <= aes.LuckDiamond {
			ret = award
			key = k
		}
	}

	if ret.Award == 0 {
		return key, nil
	}

	return key, &ret
}

func (aes *AnchorEvaluationSupportDay) Status() int {
	if aes.Received {
		return SupportDayStatusReceived
	}

	nn := now.New(time.Now().In(ctz.Brazil))
	todayTime := nn.BeginningOfDay()
	date := aes.Date.In(ctz.Brazil)

	if nn.Time.Before(date) {
		return SupportDayStatusNotStart
	}

	key, award := aes.Award()

	if award == nil {
		return SupportDayStatusNotDone
	}

	// 单个奖励的当天可领取
	if len(aes.Awards) == 1 {
		return SupportDayStatusCanReceive
	}

	// 多个奖励的可以领取今天之前的
	if todayTime.After(date) {
		return SupportDayStatusCanReceive
	}

	// 多个奖励的，当天的必须是全部档位全部完成才可以领取
	if key == len(aes.Awards)-1 {
		return SupportDayStatusCanReceive
	}

	return SupportDayStatusNotDone
}

func AnchorEvaluationSupportDayCollectionName() string {
	return "anchor.evaluation.support.day"
}

func createAnchorEvaluationSupportDayIndexer(dbmc *db.MongoClient) {
	dbmc.SyncSchema(AnchorEvaluationSupportDayCollectionName(), 1,
		db.Indexer{Name: "userId_date", Keys: bson.D{
			{Key: "userId", Value: 1},
			{Key: "date", Value: 1},
		}, Uniq: lo.ToPtr(true)},
	)
}

const (
	AnchorEvaluationFundStatusNotStart = 0 // 待开启
	AnchorEvaluationFundStatusPending  = 1 // 进行中
	AnchorEvaluationFundStatusPass     = 2 // 考核通过
	AnchorEvaluationFundStatusReject   = 3 // 考核不通过
)

type AnchorEvaluationFund struct {
	Id          string    `bson:"_id"`         // 主播ID
	Status      int       `bson:"status"`      // 0待开启 1进行中 2考核通过 3考核不通过
	StartTime   time.Time `bson:"startTime"`   // 考核开始时间
	EndTime     time.Time `bson:"endTime"`     // 考核截止时间
	LuckDiamond int64     `bson:"luckDiamond"` // 流水，金币
	Target      int64     `bson:"target"`      // 流水目标，金币
	CreatedAt   time.Time `bson:"createdAt"`   // 创建时间
	UpdatedAt   time.Time `bson:"updatedAt"`   // 更新时间
}

func (aef *AnchorEvaluationFund) RemainDay() int {
	duration := time.Until(aef.EndTime)

	return int(duration.Hours()/24) + 1
}

func AnchorEvaluationFundCollectionName() string {
	return "anchor.evaluation.fund"
}

func createAnchorEvaluationFund(dbmc *db.MongoClient) {
	dbmc.DB().CreateCollection(context.Background(), AnchorEvaluationFundCollectionName())
}

// 优质主播薪资状态
type AnchorQualitySalaryStatus int

const (
	AnchorQualitySalaryStatusDefault AnchorQualitySalaryStatus = 0    // 未知默认值
	AnchorQualitySalaryStatusPending AnchorQualitySalaryStatus = 1    // 待发放
	AnchorQualitySalaryStatusDoing   AnchorQualitySalaryStatus = 2    // 发放中
	AnchorQualitySalaryStatusDone    AnchorQualitySalaryStatus = 10   // 已发放
	AnchorQualitySalaryGradeA        string                    = "A"  // A级
	AnchorQualitySalaryGradeS        string                    = "S"  // S级
	AnchorQualitySalaryGradeSS       string                    = "SS" // SS级
)

var (
	AnchorQualitySalaryGradeList = []string{
		AnchorQualitySalaryGradeA,
		AnchorQualitySalaryGradeS,
		AnchorQualitySalaryGradeSS,
	}
)

type AnchorQualityPayee string

const (
	AnchorQualityPayeeAnchor AnchorQualityPayee = "anchor" // 主播
	AnchorQualityPayeeAgency AnchorQualityPayee = "agency" // 公会长
)

// 优质主播
type AnchorQuality struct {
	Id        string             `bson:"_id" json:"-"`           // 主播ID
	Grade     string             `bson:"grade" json:"grade"`     // 定级级别，A/S/SS
	Payee     AnchorQualityPayee `bson:"payee" json:"payee"`     // 发放对象，anchor:主播，agency:公会长
	SettleDay string             `bson:"settleDay" json:"-"`     // 最近结算日期，yyyyMMdd
	GradeAt   time.Time          `bson:"gradeAt" json:"gradeAt"` // 定级时间
	CreatedAt time.Time          `bson:"createdAt" json:"-"`     // 创建时间
	UpdatedAt time.Time          `bson:"updatedAt" json:"-"`     // 更新时间
}

func AnchorQualityCollectionName() string {
	return "anchor.quality"
}

// 索引
func createAnchorQualityIndexer(dbmc *db.MongoClient) {
	dbmc.SyncSchema(AnchorQualityCollectionName(), 1,
		db.Indexer{Name: "settleDay", Keys: bson.D{
			{Key: "settleDay", Value: -1},
		}},
	)
}

// 优质主播薪资表以及数据
type AnchorQualitySalary struct {
	Id           primitive.ObjectID        `bson:"_id"`
	UserId       string                    `bson:"userId"`       // 主播ID
	Day          string                    `bson:"day"`          // 日期 yyyyMMdd
	CTZ          string                    `bson:"ctz"`          // 时区, 例如：ID:Asia/Jakarta
	AgencyId     int64                     `bson:"agencyId"`     // 公会ID
	AgencyUserId string                    `bson:"agencyUserId"` // 公会长ID
	Grade        string                    `bson:"grade"`        // 定级级别，A/S/SS
	GradeAt      time.Time                 `bson:"gradeAt"`      // 定级时间
	LuckDiamond  int64                     `bson:"luckDiamond"`  // 幸运礼物流水，金币
	GiftDiamond  int64                     `bson:"giftDiamond"`  // 特效礼物流水，金币
	Duration     int64                     `bson:"duration"`     // 总有效直播时长（所有秒数和），单位秒
	AnchorIncome int64                     `bson:"anchorIncome"` // 优质主播主播薪资收入，水晶
	AgencyIncome int64                     `bson:"agencyIncome"` // 优质主播公会薪资收入，水晶
	Payee        AnchorQualityPayee        `bson:"payee"`        // 主播薪资发放对象，anchor:主播，agency:公会长
	Status       AnchorQualitySalaryStatus `bson:"status"`       // 状态
	UT           string                    `bson:"ut"`           // 修改token，更新时比对，防止修改了薪资被其他人按老的数据发放
}

func AnchorQualitySalaryCollectionName() string {
	return "anchor.quality.salary"
}

// 索引
func createAnchorQualitySalaryIndexer(dbmc *db.MongoClient) {
	dbmc.SyncSchema(AnchorQualitySalaryCollectionName(), 1,
		db.Indexer{Name: "userId_day", Keys: bson.D{
			{Key: "userId", Value: 1},
			{Key: "day", Value: -1},
		}, Uniq: lo.ToPtr(true)},
		db.Indexer{Name: "agencyUserId_day", Keys: bson.D{
			{Key: "agencyUserId", Value: 1},
			{Key: "day", Value: -1},
		}},
		db.Indexer{
			Name: "day_-1_luckDiamond_-1",
			Keys: bson.D{
				{Key: "day", Value: -1},
				{Key: "luckDiamond", Value: -1},
			},
		},
	)
}

// 优质主播奖励发放汇总表
type AnchorQualitySalaryGroup struct {
	Id               primitive.ObjectID        `bson:"_id"`
	Day              string                    `bson:"day"`              // 日期 yyyyMMdd
	CTZ              string                    `bson:"ctz"`              // 时区, 例如：ID:Asia/Jakarta
	LuckDiamond      int64                     `bson:"luckDiamond"`      // 幸运礼物总流水，金币
	GiftDiamond      int64                     `bson:"giftDiamond"`      // 特效礼物总流水，金币
	AnchorGradeCount map[string]int            `bson:"anchorGradeCount"` // 各级别获得薪资的主播数量
	AnchorIncome     int64                     `bson:"anchorIncome"`     // 优质主播主播总薪资收入，水晶
	AgencyIncome     int64                     `bson:"agencyIncome"`     // 优质主播公会总薪资收入，水晶
	Status           AnchorQualitySalaryStatus `bson:"status"`           // 状态
	AuditAt          time.Time                 `bson:"auditAt"`          // 审核时间
	AuditBy          string                    `bson:"auditBy"`          // 审核人
}

func AnchorQualitySalaryGroupCollectionName() string {
	return "anchor.quality.salary.group"
}

func createAnchorQualitySalaryGroupIndexer(dbmc *db.MongoClient) {
	dbmc.SyncSchema(AnchorQualitySalaryGroupCollectionName(), 1,
		db.Indexer{Name: "day", Keys: bson.D{
			{Key: "day", Value: 1},
		}, Uniq: lo.ToPtr(true)},
	)
}

// 聚合薪资数据结构
type AnchorQualitySalaryAggregate struct {
	LuckDiamond      int64          `bson:"luckDiamond"`      // 幸运礼物流水，金币
	GiftDiamond      int64          `bson:"giftDiamond"`      // 特效礼物流水，金币
	Duration         int64          `bson:"duration"`         // 总有效直播时长（所有秒数和），单位秒
	AnchorIncome     int64          `bson:"anchorIncome"`     // 优质主播主播薪资收入，水晶
	AgencyIncome     int64          `bson:"agencyIncome"`     // 优质主播公会薪资收入，水晶
	AnchorGradeCount map[string]int `bson:"anchorGradeCount"` // 各级别获得薪资的主播数量
}
