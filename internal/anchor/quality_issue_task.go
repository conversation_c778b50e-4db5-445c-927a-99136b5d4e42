package anchor

import (
	"context"
	"errors"
	"fmt"

	"gitlab.sskjz.com/go/dq"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund/order"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

type QualitySalaryIssueTask struct {
	Day string
}

func (m *Manager) initQualitySalaryIssueTask(dm *dq.Master) {
	m.qit = dq.NewWith[*QualitySalaryIssueTask](dm, "quality.salary.issue.task")
}

func (m *Manager) registerQualitySalaryIssueTask() {
	m.qit.Register(m.executeQualitySalaryIssueTask, dq.LogCost("quality.salary.issue.task.execute"))
}

func (m *Manager) submitQualitySalaryIssueTask(ctx context.Context, day string) error {
	err := m.qit.Submit(ctx, 0, &QualitySalaryIssueTask{Day: day})

	logger := m.log.With(
		zap.String("day", day),
	)

	if err != nil {
		logger.Error("提交优质主播发放优质主播薪资任务", zap.Error(err))

		return err
	}

	logger.Info("提交优质主播发放优质主播薪资任务")

	return nil
}

func (m *Manager) executeQualitySalaryIssueTask(ctx context.Context, task *QualitySalaryIssueTask) error {
	return m.doQualitySalaryIssue(ctx, task.Day)
}

type AgencyStatistic struct {
	AnchorCount       int64
	TotalAgencyIncome int64 // 公会长自己应得的薪资
	PayeeAnchorCount  int64 // 公会长代收的主播薪资数量
	PayeeAgencyIncome int64 // 公会长代收的主播薪资
}

func (m *Manager) doQualitySalaryIssue(ctx context.Context, day string) error {
	logger := m.log.With(
		zap.String("day", day),
	)

	var cnt int64
	var anchorIncome int64
	var agencyIncome int64

	l, err := m.dm.Lock(ctx, "STR:LOCK:QUALITY:SALARY:ISSUE")
	if err != nil {
		logger.Error("发放优质主播薪资", zap.Error(err))

		return err
	}
	defer l.MustUnlock()

	cur, err := m.db.Collection(AnchorQualitySalaryCollectionName()).Find(
		ctx,
		bson.M{
			"day":    day,
			"status": AnchorQualitySalaryStatusPending,
		},
	)

	if err != nil {
		logger.Error("发放优质主播薪资", zap.Error(err))

		return err
	}

	am := make(map[string]AgencyStatistic)

	for cur.Next(ctx) {
		var s AnchorQualitySalary
		err = cur.Decode(&s)

		if err != nil {
			logger.Error("发放优质主播薪资", zap.Error(err))

			return err
		}

		if s.Status != AnchorQualitySalaryStatusPending {
			continue
		}

		err = m.PayAnchorQualitySalary(ctx, s.Id.Hex(), s.UT)

		if err != nil {
			logger.Error("发放优质主播薪资", zap.Error(err), zap.String("content", s.Id.Hex()))

			return err
		}

		cnt += 1
		anchorIncome += s.AnchorIncome
		agencyIncome += s.AgencyIncome

		if s.AnchorIncome > 0 {
			if s.Payee != AnchorQualityPayeeAgency {
				// 异步发送系统通知
				if err = m.sendAnchorQualitySalaryNotice(ctx, s.UserId, s.AnchorIncome); err != nil {
					logger.Error("发放优质主播薪资", zap.Error(err), zap.String("content", s.Id.Hex()), zap.String("from", "Anchor Notice"))
				}
			} else {
				if s.AgencyUserId != "" {
					v, ok := am[s.AgencyUserId]

					if !ok {
						v = AgencyStatistic{}
					}

					v.PayeeAnchorCount++
					v.PayeeAgencyIncome += s.AnchorIncome

					am[s.AgencyUserId] = v
				}
			}
		}

		if s.AgencyUserId != "" && s.AgencyIncome > 0 {
			v, ok := am[s.AgencyUserId]

			if !ok {
				v = AgencyStatistic{}
			}

			v.AnchorCount++
			v.TotalAgencyIncome += s.AgencyIncome

			am[s.AgencyUserId] = v
		}
	}

	// 发送汇总数据的公会通知
	for agencyUserId, v := range am {
		if v.TotalAgencyIncome > 0 {
			if err := m.sendAgencyQualitySalaryNotice(ctx, agencyUserId, v.TotalAgencyIncome); err != nil {
				logger.Error(
					"发放优质主播公会薪资通知",
					zap.Error(err),
					zap.String("userId", agencyUserId),
					zap.Any("request", v),
				)
			}
		}

		if v.PayeeAgencyIncome > 0 {
			if err := m.sendAgencyQualitySalaryBehalfNotice(ctx, agencyUserId, v.PayeeAgencyIncome); err != nil {
				logger.Error(
					"发放优质主播公会代收薪资通知",
					zap.Error(err),
					zap.String("userId", agencyUserId),
					zap.Any("request", v),
				)
			}
		}
	}

	// 更新发放状态
	m.db.Collection(AnchorQualitySalaryGroupCollectionName()).UpdateOne(
		ctx,
		bson.M{
			"day":    day,
			"status": AnchorQualitySalaryStatusDoing,
		},
		bson.M{
			"$set": bson.M{
				"status": AnchorQualitySalaryStatusDone,
			},
		},
	)

	logger.Info(
		"发放优质主播薪资",
		zap.Int64("count", cnt),
		zap.Int64("spend", anchorIncome),
		zap.Int64("score", agencyIncome),
	)

	return nil
}

// 发放优质主播薪资，优质主播薪资数据不更新到主播和公会ls天数据中
func (m *Manager) PayAnchorQualitySalary(
	ctx context.Context,
	salaryId string,
	ut string,
) error {
	id, err := primitive.ObjectIDFromHex(salaryId)

	if err != nil {
		return err
	}

	logger := m.log.With(
		zap.String("salaryId", salaryId),
		zap.String("ut", ut),
	)

	collection := m.db.Collection(AnchorQualitySalaryCollectionName())

	var s AnchorQualitySalary

	err = m.db.TryTxn(ctx, func(ctx context.Context) error {
		res, err := collection.UpdateOne(
			ctx,
			bson.M{
				"_id":    id,
				"status": AnchorQualitySalaryStatusPending,
				"ut":     ut,
			},
			bson.M{
				"$set": bson.M{
					"status": AnchorQualitySalaryStatusDone,
					"ut":     primitive.NewObjectID().Hex(),
				},
			},
		)

		if err != nil {
			logger.Error("发放优质主播薪资更新数据失败", zap.Error(err))

			return err
		}

		if res.MatchedCount == 0 {
			logger.Error("发放优质主播薪资无匹配数据")

			return errors.New("PaySalary no salary matched")
		}

		if res.ModifiedCount == 0 {
			logger.Error("发放优质主播薪资无更新数据")

			return errors.New("PaySalary no salary updated")
		}

		// 查找薪资信息
		if err := collection.FindOne(ctx, bson.M{"_id": id}).Decode(&s); err != nil {
			logger.Error("发放优质主播薪资查询数据失败", zap.Error(err))

			return err
		}

		return nil
	})

	if err != nil {
		logger.Error("发放优质主播薪资失败", zap.Error(err))

		return err
	}

	appId := "QualitySalary"
	anchorPrefix := "Anchor"           // 主播薪资
	agencyPrefix := "Agency"           // 公会长薪资
	agencyPayeePrefix := "AgencyPayee" // 公会长代收主播薪资

	// 各个收款tradeNo
	anchorTradeNo := fmt.Sprintf("%s_%s", anchorPrefix, s.Id.Hex())
	agencyTradeNo := fmt.Sprintf("%s_%s", agencyPrefix, s.Id.Hex())
	agencyPayeeTradeNo := fmt.Sprintf("%s_%s", agencyPayeePrefix, s.Id.Hex())

	// 主播薪资
	if s.AnchorIncome > 0 {
		if s.Payee == AnchorQualityPayeeAgency {
			if s.AgencyUserId != "" {
				// 公会长代收主播薪资
				err = m.fom.Income(
					ctx,
					appId,
					agencyPayeeTradeNo,
					s.AgencyUserId,
					fund.JTypeRewards,
					fund.PTypeFruits,
					s.AnchorIncome, // 主播的薪资发给公会长
					fund.WithTrade(agencyPayeeTradeNo),
					fund.WithGroup(agencyPayeePrefix, s.Day),
					fund.WithDetail(agencyDetailRemark),
				)

				if err != nil {
					logger.Error("发放优质主播公会薪资失败", zap.Error(err), zap.String("from", "FOM_AGENCY"))

					if err != order.ErrOrderDuplicated {
						return err
					}
				}
			} else {
				logger.Error(
					"发放优质主播公会薪资失败",
					zap.String("reason", "AgencyUserId is empty"),
				)
			}
		} else {
			// 主播自己收主播薪资
			err = m.fom.Income(
				ctx,
				appId,
				anchorTradeNo,
				s.UserId,
				fund.JTypeRewards,
				fund.PTypeFruits,
				s.AnchorIncome,
				fund.WithTrade(anchorTradeNo),
				fund.WithDetail(anchorDetailRemark),
			)

			if err != nil {
				logger.Error("发放优质主播薪资失败", zap.Error(err), zap.String("from", "FOM_ANCHOR"))

				if err != order.ErrOrderDuplicated {
					return err
				}
			}
		}
	}

	if s.AgencyIncome > 0 && s.AgencyUserId != "" {
		err = m.fom.Income(
			ctx,
			appId,
			agencyTradeNo,
			s.AgencyUserId,
			fund.JTypeRewards,
			fund.PTypeFruits,
			s.AgencyIncome,
			fund.WithTrade(agencyTradeNo),
			fund.WithGroup(agencyPrefix, s.Day),
			fund.WithDetail(agencyDetailRemark),
		)

		if err != nil {
			logger.Error("发放优质主播公会薪资失败", zap.Error(err), zap.String("from", "FOM_AGENCY"))

			if err != order.ErrOrderDuplicated {
				return err
			}
		}
	}

	logger.Info(
		"发放优质主播薪资成功",
		zap.Int64("anchorIncome", s.AnchorIncome),
		zap.Int64("agencyIncome", s.AgencyIncome),
		zap.String("userId", s.UserId),
		zap.String("agencyUserId", s.AgencyUserId),
		zap.String("day", s.Day),
		zap.Int64("duration", s.Duration),
		zap.Int64("luckDiamond", s.LuckDiamond),
		zap.Int64("giftDiamond", s.GiftDiamond),
		zap.String("payee", string(s.Payee)),
	)

	return nil
}

func (m *Manager) sendAnchorQualitySalaryNotice(ctx context.Context, userId string, anchorIncome int64) error {
	if anchorIncome <= 0 {
		return nil
	}

	return m.imm.SendSystemNoticeTextToUser(
		ctx,
		userId,
		fmt.Sprintf(
			anchorNotice,
			anchorIncome,
		),
	)
}

func (m *Manager) sendAgencyQualitySalaryNotice(ctx context.Context, agencyUserId string, income int64) error {
	if income <= 0 {
		return nil
	}

	return m.imm.SendSystemNoticeTextToUser(
		ctx,
		agencyUserId,
		fmt.Sprintf(
			agencyNotice,
			income,
		),
	)
}

func (m *Manager) sendAgencyQualitySalaryBehalfNotice(ctx context.Context, agencyUserId string, income int64) error {
	if income <= 0 {
		return nil
	}

	return m.imm.SendSystemNoticeTextToUser(
		ctx,
		agencyUserId,
		fmt.Sprintf(
			agencyNoticeBehalf,
			income,
		),
	)
}
