package anchor

import (
	"time"

	"gitlab.sskjz.com/go/cc"
	"gitlab.sskjz.com/go/dq"
	"gitlab.sskjz.com/go/ev"
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/agency"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund/order"
	"gitlab.sskjz.com/overseas/live/osl/internal/im"
	"gitlab.sskjz.com/overseas/live/osl/internal/live"
	"gitlab.sskjz.com/overseas/live/osl/internal/live/lsr"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"go.uber.org/zap"
)

type Manager struct {
	db        *db.MongoClient
	rc        *redi.Client
	dm        *redi.Mutex
	lm        *live.Manager
	fm        *fund.Manager
	fom       *order.Manager
	imm       *im.Manager
	am        *agency.Manager
	lsrm      *lsr.Manager
	ev        ev.Bus
	flagCache cc.Cache[string, *AnchorFlags]
	qit       dq.Queue[*QualitySalaryIssueTask]
	log       *zap.Logger
}

func newManager(db *db.MongoClient, rc *redi.Client, dm *redi.Mutex, lm *live.Manager, fm *fund.Manager, fom *order.Manager, imm *im.Manager, am *agency.Manager, lsrm *lsr.Manager, ev ev.Bus, syn cc.Sync, log *zap.Logger) *Manager {
	m := &Manager{
		db:   db,
		rc:   rc,
		dm:   dm,
		lm:   lm,
		fm:   fm,
		fom:  fom,
		imm:  imm,
		am:   am,
		lsrm: lsrm,
		ev:   ev,
		log:  log,
	}

	m.flagCache = cc.New[string, *AnchorFlags](
		1024, cc.LRU,
		cc.LoaderFunc(func(key string) (*AnchorFlags, error) {
			if acc, err := m.getAnchorFlagsModel(key); err != nil {
				return nil, err
			} else {
				return acc, nil
			}
		}),
		cc.Expiration(2*time.Hour),
		cc.ExportStats("anchor.flag"),
		cc.WithSync(syn, "anchor.flag"),
	)

	return m
}
