package anchor

import (
	"context"
	"fmt"

	"go.mongodb.org/mongo-driver/bson"
)

func (m *Manager) QualityAggregate1(
	ctx context.Context,
	settleDay string,
) (*AnchorQualitySalaryAggregate, error) {
	collection := m.db.Collection(AnchorQualitySalaryCollectionName())

	cursor, err := collection.Aggregate(
		ctx,
		[]bson.M{
			{
				"$match": bson.M{
					"day": settleDay,
				},
			},
			{
				"$group": bson.M{
					"_id": nil,
					"duration": bson.M{
						"$sum": "$duration",
					},
					"luckDiamond": bson.M{
						"$sum": "$luckDiamond",
					},
					"giftDiamond": bson.M{
						"$sum": "$giftDiamond",
					},
					"anchorIncome": bson.M{
						"$sum": "$anchorIncome",
					},
					"agencyIncome": bson.M{
						"$sum": "$agencyIncome",
					},
				},
			},
		},
	)

	if err != nil {
		return nil, fmt.Errorf("aggregate anchor quality salary group: %w", err)
	}

	defer cursor.Close(ctx)

	var result AnchorQualitySalaryAggregate

	if cursor.Next(ctx) {
		if err := cursor.Decode(&result); err != nil {
			return nil, fmt.Errorf("decode anchor quality salary group: %w", err)
		}

		return &result, nil
	}

	return nil, fmt.Errorf("no data found for anchor quality salary aggregate on day %s", settleDay)
}

func (m *Manager) QualityAggregate2(
	ctx context.Context,
	settleDay string,
) (*AnchorQualitySalaryAggregate, error) {
	collection := m.db.Collection(AnchorQualitySalaryCollectionName())

	cursor, err := collection.Aggregate(
		ctx,
		[]bson.M{
			{
				"$match": bson.M{
					"day":          settleDay,
					"anchorIncome": bson.M{"$gt": 0},
				},
			},
			{
				"$group": bson.M{
					"_id":   "$grade",
					"count": bson.M{"$sum": 1},
				},
			},
			{
				"$project": bson.M{
					"_id":   0,
					"key":   "$_id",
					"value": "$count",
				},
			},
			{
				"$group": bson.M{
					"_id": nil,
					"result": bson.M{
						"$push": bson.M{
							"k": "$key",
							"v": "$value",
						},
					},
				},
			},
			{
				"$project": bson.M{
					"_id":              0,
					"anchorGradeCount": bson.M{"$arrayToObject": "$result"},
				},
			},
		},
	)

	if err != nil {
		return nil, fmt.Errorf("aggregate anchor quality salary group: %w", err)
	}

	defer cursor.Close(ctx)

	result := AnchorQualitySalaryAggregate{
		AnchorGradeCount: make(map[string]int, 0),
	}

	if cursor.Next(ctx) {
		if err := cursor.Decode(&result); err != nil {
			return nil, fmt.Errorf("decode anchor quality salary group: %w", err)
		}

		return &result, nil
	}

	return &result, nil
}
