package fctask

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/jinzhu/now"
	"go.uber.org/zap"
)

const (
	keyTaskStep = "FC:TASK:STEP:%s:%d:%s:%s" // date, Type, anchorId, userId
	keyTaskDone = "FC:TASK:DONE:%s:%d:%s:%s" // date, Type, anchorId, userId
	keyIntimacy = "FC:INTIMACY:%s:%d:%s:%s"  // date, Type, anchorId, userId
)

func (s *Manager) getTaskStep(ctx context.Context, at time.Time, tt Type, anchorId, userId string) (int, error) {
	return s.rc.Get(ctx, fmt.Sprintf(keyTaskStep, dateOf(at, s.tz(userId)), tt, anchorId, userId)).Int()
}

func (s *Manager) incTaskStep(ctx context.Context, at time.Time, tt Type, anchorId, userId string, value int) (int, error) {
	key := fmt.Sprintf(keyTaskStep, dateOf(at, s.tz(userId)), tt, anchorId, userId)
	txp := s.rc.Pipeline()
	ret := txp.IncrBy(ctx, key, int64(value))
	txp.Expire(ctx, key, dayTTL(at, s.tz(userId)))
	if _, err := txp.Exec(ctx); err != nil {
		return 0, err
	}
	return int(ret.Val()), nil
}

func (s *Manager) getIntimacy(ctx context.Context, at time.Time, tt Type, anchorId, userId string) (int, error) {
	return s.rc.Get(ctx, fmt.Sprintf(keyIntimacy, dateOf(at, s.tz(userId)), tt, anchorId, userId)).Int()
}

func (s *Manager) addIntimacy(ctx context.Context, at time.Time, tt Type, roomId, anchorId, userId string, value int) (int, error) {
	key := fmt.Sprintf(keyIntimacy, dateOf(at, s.tz(userId)), tt, anchorId, userId)
	txp := s.rc.Pipeline()
	ret := txp.IncrBy(ctx, key, int64(value))
	txp.Expire(ctx, key, dayTTL(at, s.tz(userId)))
	if _, err := txp.Exec(ctx); err != nil {
		return 0, err
	}
	if _, err := s.fc.AddExpInRoom(ctx, roomId, anchorId, userId, value); err != nil {
		s.log.Warn("add exp in room failed", zap.Error(err))
	}
	s.onIntimacy(ctx, at, anchorId, userId, value)
	return int(ret.Val()), nil
}

func (s *Manager) chkIsDone(ctx context.Context, at time.Time, tt Type, anchorId, userId string) (bool, error) {
	if s.isDone(tt, anchorId, userId) {
		return true, nil
	}
	key := fmt.Sprintf(keyTaskDone, dateOf(at, s.tz(userId)), tt, anchorId, userId)
	v, err := s.rc.Get(ctx, key).Int()
	if err != nil && !errors.Is(err, redis.Nil) {
		return false, err
	}
	done := v > 0
	if done {
		s.setDone(at, tt, anchorId, userId)
	}
	return done, nil
}

func (s *Manager) markAsDone(ctx context.Context, at time.Time, tt Type, roomId, anchorId, userId string, intimacy int) error {
	key := fmt.Sprintf(keyTaskDone, dateOf(at, s.tz(userId)), tt, anchorId, userId)
	txp := s.rc.Pipeline()
	ret := txp.IncrBy(ctx, key, 1)
	txp.Expire(ctx, key, dayTTL(at, s.tz(userId)))
	if _, err := txp.Exec(ctx); err != nil {
		return err
	}
	if ret.Val() == 1 {
		if intimacy > 0 {
			_, _ = s.addIntimacy(ctx, at, tt, roomId, anchorId, userId, intimacy)
		}
		s.onTaskDone(ctx, at, tt, anchorId)
	}
	s.setDone(at, tt, anchorId, userId)
	return nil
}

func dateOf(t time.Time, z *time.Location) string {
	return t.In(z).Format("20060102")
}

func dayTTL(t time.Time, z *time.Location) time.Duration {
	t = t.In(z)
	return now.New(t).EndOfDay().Sub(t).Truncate(time.Second) + time.Second
}
