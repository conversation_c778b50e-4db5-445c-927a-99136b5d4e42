package sticker

import (
	"gitlab.sskjz.com/overseas/live/osl/internal/gift"
	"gitlab.sskjz.com/overseas/live/osl/internal/live"
	"gitlab.sskjz.com/overseas/live/osl/internal/room/interact"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
)

func Invoke(r *api.Router, gm *gift.Manager, lm *live.Manager, im *interact.Manager) {
	apis := &API{
		gm: gm,
		lm: lm,
		im: im,
	}

	ar := r.WithAuth()
	{
		g := ar.Group("/gift/sticker")
		g.POST("/send", api.Generic(apis.Send))
	}
}
