package sticker

import (
	"context"
	"time"

	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/mixer"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/internal/gift"
	"gitlab.sskjz.com/overseas/live/osl/internal/live"
	"gitlab.sskjz.com/overseas/live/osl/internal/room/interact"
	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ds"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/rlog"
)

type API struct {
	gm *gift.Manager
	im *interact.Manager
	lm *live.Manager
}

type SendRequest struct {
	RoomId string `json:"roomId" binding:"required"`
	GiftId int    `json:"giftId" binding:"required,gt=0"`
}

// @Tags 互动贴纸礼物
// @Summary 送礼物
// @Description 送互动贴纸礼物
// @Produce json
// @Security HeaderAuth
// @Param param body SendRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.GiftSendResponse}
// @Router /api/v1/gift/sticker/send [post]
func (g *API) Send(ctx *api.Context, req SendRequest) (*types.GiftSendResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	ri, err := g.lm.Room2(req.RoomId)
	if err != nil {
		return nil, err
	}

	if ri.IsLuckyRoom() {
		return nil, biz.NewError(biz.ErrInvalidParam, "invalid params")
	}

	sr, err := g.im.SendGift(context.TODO(), rlog.RequestId(ctx), req.RoomId, uac.UserId, ds.NewUUID(), req.GiftId, 1, nil, time.Now())
	if err != nil {
		return nil, err
	}

	var resp types.GiftSendResponse

	if sr.Wallet != nil {
		resp.Wallet = &types.UserWallet{
			Diamond: sr.Wallet.BVal(fund.PTypeDiamond).IntPart(),
		}
	}

	if sr.Level != nil {
		resp.LevelInfo = mixer.LevelInfo(sr.Level)
	}

	return &resp, nil
}
