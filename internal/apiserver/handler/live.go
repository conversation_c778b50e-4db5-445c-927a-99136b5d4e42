package handler

import (
	"context"
	"crypto/md5"
	"errors"
	"fmt"
	"net/http"
	"slices"
	"strconv"
	"strings"
	"time"
	"unicode/utf8"

	"gitlab.sskjz.com/overseas/live/osl/internal/agency"
	"gitlab.sskjz.com/overseas/live/osl/internal/agency/rqa"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/interaction/sredpacket"
	"gitlab.sskjz.com/overseas/live/osl/internal/profitsharing"

	"github.com/samber/lo"
	"gitlab.sskjz.com/overseas/live/osl/internal/interaction/rocket"

	"github.com/gin-gonic/gin"
	"github.com/jinzhu/now"
	"gitlab.sskjz.com/go/i3n"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/activity/widget"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/mixer"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/upload"
	"gitlab.sskjz.com/overseas/live/osl/internal/face"
	"gitlab.sskjz.com/overseas/live/osl/internal/fclub"
	"gitlab.sskjz.com/overseas/live/osl/internal/follow"
	"gitlab.sskjz.com/overseas/live/osl/internal/link"
	"gitlab.sskjz.com/overseas/live/osl/internal/live"
	"gitlab.sskjz.com/overseas/live/osl/internal/live/ls"
	"gitlab.sskjz.com/overseas/live/osl/internal/live/rtc"
	"gitlab.sskjz.com/overseas/live/osl/internal/live/warning"
	"gitlab.sskjz.com/overseas/live/osl/internal/manage/logging"
	"gitlab.sskjz.com/overseas/live/osl/internal/manage/patrol"
	"gitlab.sskjz.com/overseas/live/osl/internal/moment"
	pp "gitlab.sskjz.com/overseas/live/osl/internal/patrol"
	"gitlab.sskjz.com/overseas/live/osl/internal/pk"
	"gitlab.sskjz.com/overseas/live/osl/internal/review"
	"gitlab.sskjz.com/overseas/live/osl/internal/room/interact"
	"gitlab.sskjz.com/overseas/live/osl/internal/room/online"
	"gitlab.sskjz.com/overseas/live/osl/internal/room/rsd"
	"gitlab.sskjz.com/overseas/live/osl/internal/salary"
	"gitlab.sskjz.com/overseas/live/osl/internal/task"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/avatar"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/privacy"
	"gitlab.sskjz.com/overseas/live/osl/pkg/app"
	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ds"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/cache"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/rlog"
	"gitlab.sskjz.com/overseas/live/osl/pkg/i18n"
	"gitlab.sskjz.com/overseas/live/osl/pkg/protocol"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"go.uber.org/zap"
)

func InvokeLiveHandler(
	r *api.Router, hc *cache.Handler,
	lm *live.Manager,
	ug user.Getter, as *avatar.Show, fg follow.Getter, fc fclub.Getter,
	om *online.Manager,
	im *interact.Manager,
	pkm *pk.Manager,
	sm *rsd.Stats,
	lsm *ls.Manager,
	rtcm *rtc.Manager,
	pm privacy.Getter,
	us *upload.Scenes,
	fm *face.Manager,
	tm *task.Manager,
	dm *redi.Mutex,
	slm *salary.Manager,
	reviewm *review.Manager,
	wm *warning.Manager,
	ptm *patrol.Manager,
	lk *link.Manager,
	psm *profitsharing.Manager,
	rm *rocket.Manager,
	ppm *pp.Manager,
	lkm *link.Manager,
	rh *sredpacket.API,
	rw *widget.Manager,
	am *agency.Manager,
	vnd log.Vendor,
) *Live {
	l := NewLive(lm, ug, as, fg, fc, om, im, pkm, sm, lsm, rtcm, pm, us, fm, tm, dm, slm, reviewm, wm, lk, psm, rm, ppm, lkm, rh, rw, am, vnd.Scope("api.live"))

	ar := r.WithAuth()
	{
		// 观众列表
		ar.GET("/live/audience", api.Generic(l.Audience))
		// 点赞
		ar.POST("/live/like", rlog.Opt(rlog.WithRatio(0)), api.Generic(l.Like))
		// 直播前人脸检测
		ar.POST("/live/face/check", api.Generic(l.FaceCheck))
		// 开始直播
		ar.POST("/live/start", api.Generic(l.Start))
		// 结束直播
		ar.POST("/live/stop", api.Generic(l.Stop))
		// 直播心跳
		ar.POST("/live/heartbeat", api.Generic(l.Heartbeat))
		// 直播暂离
		ar.POST("/live/pause", api.Generic(l.Pause))
		// 直播暂离恢复
		ar.POST("/live/resume", api.Generic(l.Resume))
		// 直播场次数据
		ar.GET("/live/record", api.Generic(l.Record))
		// 直播动态
		ar.GET("/live/moment", api.Generic(l.Moment))
		// 设置开播提醒
		ar.POST("/live/notice/set", api.Generic(l.SetNotice))
		// 预约下一场直播
		ar.POST("/live/appointment/set", api.Generic(l.SetAppointment))
		// 设置直播间简介
		ar.POST("/live/introduction/set", api.Generic(l.SetIntroduction))
		// 获取直播间简介
		ar.GET("/live/introduction", api.Generic(l.GetIntroduction))
		// 用户主页直播页签
		ar.GET("/live/profile", api.Generic(l.Profile))
		// 送礼统计
		ar.GET("/live/gift/stat", api.Generic(l.GiftStat))
		// 封禁/解封用户音视频流
		ar.POST("/live/rtc/stream/operate", api.Generic(l.OperateStream))
		// 直播警告-继续直播
		ar.POST("/live/warning/confirm", api.Generic(l.WarningConfirm))
		// 直播警告-操作选项
		ar.GET("/live/warning/options", api.Generic(l.WarningOptions))

		ar.POST("/live/mute/sync", api.Generic(l.SyncMute))
		// 直播警告-巡管接口
		pr := ar.With(ptm.NeedPatroller, logging.Recorder)
		{
			// 巡管操作日志
			initLiveWarningLog(wm)
			// 直播警告-发出警告
			pr.POST("/live/warning/warn", api.Generic(l.WarningWarn))
			// 直播警告-中断直播
			pr.POST("/live/warning/ban", api.Generic(l.WarningBan))
			// 直播警告-本场直播不分发
			pr.POST("/live/warning/hide", api.Generic(l.WarningHide))
		}
	}

	tr := r.TryAuth()
	{
		// 直播间数据
		tr.GET("/live/room", api.Generic(l.Room))
		// 直播间数据-在room结构基础上扩展
		tr.GET("/live/room/extended", api.Generic(l.RoomExtended))
		// 直播间数据-我相关信息
		tr.GET("/live/room/me", api.Generic(l.RoomMe))
		// 特殊能力直播间
		tr.GET("/live/special/room", api.Generic(l.SpecialRoom))
		// 获取rtc token
		tr.GET("/live/rtc/token", api.Generic(l.RtcToken))
		// 直播间混合状态数据
		tr.GET("/live/room/status", hc.Middleware(cache.WithExpire(time.Millisecond*500)), api.Generic(l.RoomStatus))
		// 直播间活动
		tr.GET("/live/room/activity", api.Generic(l.RoomActivity))

		tr.GET("/live/poll", rlog.Opt(rlog.WithRatio(0)), api.Generic(l.Poll))
		// 直播间状态检查
		tr.POST("/live/online/check", api.Generic(l.OnlineCheck))
	}

	// RTC回调
	cr := r.With(rtcm.Middleware())
	{
		cr.POST("/live/rtc/callback", api.Custom(l.RtcCallback))
	}

	{
		us.Add("live_cover", us.Classic("live_cover", moment.PictureStyle))
	}

	return l
}

type Live struct {
	lm      *live.Manager
	ug      user.Getter
	as      *avatar.Show
	fg      follow.Getter
	fc      fclub.Getter
	om      *online.Manager
	im      *interact.Manager
	sm      *rsd.Stats
	lsm     *ls.Manager
	rtcm    *rtc.Manager
	pkm     *pk.Manager
	pm      privacy.Getter
	us      *upload.Scenes
	fm      *face.Manager
	tm      *task.Manager
	dm      *redi.Mutex
	slm     *salary.Manager
	reviewm *review.Manager
	wm      *warning.Manager
	lk      *link.Manager
	psm     *profitsharing.Manager
	rm      *rocket.Manager
	ppm     *pp.Manager
	lkm     *link.Manager
	rh      *sredpacket.API
	rw      *widget.Manager
	am      *agency.Manager
	logger  *zap.Logger
}

func NewLive(
	lm *live.Manager,
	ug user.Getter,
	as *avatar.Show,
	fg follow.Getter,
	fc fclub.Getter,
	om *online.Manager,
	im *interact.Manager,
	pkm *pk.Manager,
	sm *rsd.Stats,
	lsm *ls.Manager,
	rtcm *rtc.Manager,
	pm privacy.Getter,
	us *upload.Scenes,
	fm *face.Manager,
	tm *task.Manager,
	dm *redi.Mutex,
	slm *salary.Manager,
	reviewm *review.Manager,
	wm *warning.Manager,
	lk *link.Manager,
	psm *profitsharing.Manager,
	rm *rocket.Manager,
	ppm *pp.Manager,
	lkm *link.Manager,
	rh *sredpacket.API,
	rw *widget.Manager,
	am *agency.Manager,
	logger *zap.Logger,
) *Live {
	return &Live{
		lm:      lm,
		om:      om,
		im:      im,
		pkm:     pkm,
		ug:      ug,
		as:      as,
		fg:      fg,
		fc:      fc,
		sm:      sm,
		lsm:     lsm,
		rtcm:    rtcm,
		pm:      pm,
		us:      us,
		fm:      fm,
		tm:      tm,
		dm:      dm,
		slm:     slm,
		reviewm: reviewm,
		wm:      wm,
		lk:      lk,
		psm:     psm,
		rm:      rm,
		ppm:     ppm,
		lkm:     lkm,
		rh:      rh,
		rw:      rw,
		am:      am,
		logger:  logger,
	}
}

// @Tags 直播
// @Summary ✅直播间数据
// @Description 根据直播间ID或用户ID获取直播间数据。14001：直播间不存在, 19010:嘉宾未连线
// @Produce json
// @Param param query types.LiveRoomRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.LiveRoomResponse}
// @Router /api/v1/live/room [get]
func (l *Live) Room(ctx *api.Context, req types.LiveRoomRequest) (*types.LiveRoomResponse, error) {
	userId := ""

	uac, err := ctx.User()
	if err == nil {
		userId = uac.UserId
	}

	var room *live.Room

	if req.Id != "" {
		room, err = l.lm.Room(req.Id)
		if err != nil {
			return nil, err
		}
	} else if req.UserId != "" {
		if req.UserId == userId {
			// 主播查看自己的直播间，没有则初始化
			room, err = l.lm.RoomByUserId2(ctx, req.UserId)
		} else {
			// 观众查看主播的直播间，只读
			room, err = l.lm.RoomByUserIdReadOnly2(ctx, req.UserId)
		}

		if err != nil {
			return nil, err
		}
	} else if req.LinkUserId != "" {
		info, err := l.lkm.UserLinkInfo2(ctx, req.LinkUserId)
		if err != nil {
			return nil, err
		}

		if info == nil {
			return nil, biz.NewError(biz.ErrLinkNotLinked, "user not linked")
		} else {
			room, err = l.lm.SessionRoom(info.SessionId)
			if err != nil {
				return nil, err
			}
		}
	} else {
		return nil, biz.NewError(biz.ErrInvalidParam, "invalid params")
	}

	tr, err := mixer.RoomV2(ctx, userId, l.ug, l.fg, l.sm, room, l.lm)

	if err != nil {
		return nil, err
	}

	// 开播时主播读取直播间配置
	// 封面图没有上传过，则使用最新的头像地址
	// req.Conf == 1 &&
	if userId == tr.User.UserId && !strings.Contains(tr.Cover, "live_cover") {
		tr.Cover = l.as.LargeOf(uac.Avatar)
	}

	var fansclub *types.RoomFans
	if userId != "" && userId != tr.User.UserId {
		if lv, err := l.fc.Info(ctx, tr.User.UserId, userId); err == nil && lv.Valid() {
			fansclub = &types.RoomFans{
				Level:    lv.Level,
				Inactive: !lv.Active,
			}
		}
	}

	return &types.LiveRoomResponse{
		Room:     *tr,
		Fansclub: fansclub,
	}, nil
}

// @Tags 直播
// @Summary 直播间数据-在room结构基础上扩展
// @Description 直播间数据-在room结构基础上扩展。14001：直播间不存在, 19010:嘉宾未连线
// @Produce json
// @Param param query types.LiveRoomRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.LiveRoomExtendedResponse}
// @Router /api/v1/live/room/extended [get]
func (l *Live) RoomExtended(ctx *api.Context, req types.LiveRoomRequest) (*types.LiveRoomExtendedResponse, error) {
	userId := ""

	uac, err := ctx.User()
	if err == nil {
		userId = uac.UserId
	}

	var room *live.Room

	if req.Id != "" {
		room, err = l.lm.Room(req.Id)
		if err != nil {
			return nil, err
		}
	} else if req.UserId != "" {
		if req.UserId == userId {
			// 主播查看自己的直播间，没有则初始化
			room, err = l.lm.RoomByUserId2(ctx, req.UserId)
		} else {
			// 观众查看主播的直播间，只读
			room, err = l.lm.RoomByUserIdReadOnly2(ctx, req.UserId)
		}

		if err != nil {
			return nil, err
		}
	} else if req.LinkUserId != "" {
		info, err := l.lkm.UserLinkInfo2(ctx, req.LinkUserId)
		if err != nil {
			return nil, err
		}

		if info == nil {
			return nil, biz.NewError(biz.ErrLinkNotLinked, "user not linked")
		} else {
			room, err = l.lm.SessionRoom(info.SessionId)
			if err != nil {
				return nil, err
			}
		}
	} else {
		return nil, biz.NewError(biz.ErrInvalidParam, "invalid params")
	}

	tr, err := mixer.RoomV2(ctx, userId, l.ug, l.fg, l.sm, room, l.lm)

	if err != nil {
		return nil, err
	}

	// 开播时主播读取直播间配置
	// 封面图没有上传过，则使用最新的头像地址
	// req.Conf == 1 &&
	if userId == tr.User.UserId && !strings.Contains(tr.Cover, "live_cover") {
		tr.Cover = l.as.LargeOf(uac.Avatar)
	}

	return &types.LiveRoomExtendedResponse{
		Room: *tr,
		Me:   lo.Must(mixer.RoomMe(ctx, tr.User.UserId, userId, l.fc)),
	}, nil
}

// @Tags 直播
// @Summary 直播间数据-与我相关的信息
// @Description 直播间数据-与我相关的信息
// @Produce json
// @Param param query types.LiveRoomMeRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.LiveRoomMeResponse}
// @Router /api/v1/live/room/me [get]
func (l *Live) RoomMe(ctx *api.Context, req types.LiveRoomMeRequest) (*types.LiveRoomMeResponse, error) {
	userId := ""

	uac, err := ctx.User()
	if err == nil {
		userId = uac.UserId
	}

	var room *live.Room

	if req.Id != "" {
		room, err = l.lm.Room(req.Id)
		if err != nil {
			return nil, err
		}
	} else if req.UserId != "" {
		if req.UserId == userId {
			// 主播查看自己的直播间
			room, err = l.lm.RoomByUserId2(ctx, req.UserId)
		} else {
			// 观众查看主播的直播间，只读
			room, err = l.lm.RoomByUserIdReadOnly2(ctx, req.UserId)
		}

		if err != nil {
			return nil, err
		}
	} else {
		return nil, biz.NewError(biz.ErrInvalidParam, "invalid params")
	}

	return &types.LiveRoomMeResponse{Me: lo.Must(mixer.RoomMe(ctx, room.UserId, userId, l.fc))}, nil
}

// @Tags 直播
// @Summary ✅特殊能力直播间数据
// @Description 特殊能力直播间数据。14001：直播间不存在
// @Produce json
// @Param param query types.LiveSpecialRoomRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.LiveRoomResponse}
// @Router /api/v1/live/special/room [get]
func (l *Live) SpecialRoom(ctx *api.Context, req types.LiveSpecialRoomRequest) (*types.LiveRoomResponse, error) {
	scene := req.Scene

	userId := ""

	uac, err := ctx.User()
	if err == nil {
		userId = uac.UserId
	}

	var flag live.RoomFlag

	switch scene {
	case "lucky":
		flag = live.RoomFlagLucky
	case "game":
		flag = live.RoomFlagGame
	default:
		return nil, biz.NewError(biz.ErrInvalidParam, "invalid scene")
	}

	res, err := l.lm.GetRoomListByFlag(ctx, flag, 1, 1)

	if err != nil {
		return nil, err
	}

	if len(res) == 0 {
		l.logger.Error("特殊能力直播间不存在")

		return nil, biz.NewError(biz.ErrRoomNotExists, "room not exists")
	}

	tr, err := mixer.Room(ctx, userId, l.ug, l.fg, l.sm, &res[0])

	if err != nil {
		return nil, err
	}

	return &types.LiveRoomResponse{
		Room: *tr,
	}, nil
}

type LiveRoomActivityRequest struct {
	RoomId string `form:"roomId" binding:"required"`
}

type LiveRoomActivityResponse struct {
	Activities []string       `json:"activities"`
	WidgetRT   *widget.Widget `json:"widget1"` // 右上角挂件（没有时为null）
}

// @Tags 直播
// @Summary ✅直播间活动
// @Description 直播间活动, 返回当前直播间正在进行的活动: lucky_gift_prize 幸运礼物体验房宝箱活动
// @Produce json
// @Security HeaderAuth
// @Param param query LiveRoomActivityRequest true "请求参数"
// @Success 200 {object} codec.Response{data=LiveRoomActivityResponse}
// @Router /api/v1/live/room/activity [get]
func (l *Live) RoomActivity(ctx *api.Context, req LiveRoomActivityRequest) (*LiveRoomActivityResponse, error) {
	ri, err := l.lm.Room2(req.RoomId)
	if err != nil {
		return nil, err
	}

	var resp LiveRoomActivityResponse

	if ri.IsLuckyRoom() {
		resp.Activities = append(resp.Activities, "lucky_gift_prize")
	}

	resp.WidgetRT = widget.LiveRT(ctx, ri)

	return &resp, nil
}

// @Tags 直播
// @Summary ✅获取rtc token
// @Description 获取rtc token，游客使用请求头里的客户端标识
// @Produce json
// @Security HeaderAuth
// @Param X-App-DeviceId header string false "设备ID"
// @Param param query types.LiveRtcTokenRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.LiveRtcTokenResponse}
// @Router /api/v1/live/rtc/token [get]
func (l *Live) RtcToken(ctx *api.Context, req types.LiveRtcTokenRequest) (*types.LiveRtcTokenResponse, error) {
	did := app.DeviceId(ctx)

	if did == "" {
		return nil, biz.NewError(biz.ErrInvalidParam, "missing deviceId")
	}

	didStr := fmt.Sprintf("%x", md5.Sum([]byte(did)))

	uac, err := ctx.User()
	if err != nil {
		uac = &user.Account{UserId: fmt.Sprintf("anonymous_%s", didStr)}
	}
	userId := uac.UserId

	roomId := req.RoomId
	selfUserId := req.SelfUserId

	var token string
	var isOwner bool

	if roomId == "" {
		roomId = "*"
	}

	if req.Self == 1 {
		room, err := l.lm.RoomByUserId(ctx, userId)

		if err != nil {
			return nil, err
		}

		isOwner = true
		roomId = room.Id.Hex()
	} else {
		if roomId != "*" {
			room, err := l.lm.Room2(roomId)

			if err != nil {
				return nil, err
			}

			isOwner = room.UserId == userId
		}
	}

	var tokenUserId string

	if selfUserId == 1 {
		// 主播自己userId
		tokenUserId = userId
	} else {
		// 观众场景，使用userId::didStr
		tokenUserId = rtc.AudienceUid(userId, did)
	}

	token, expireTime, err := l.rtcm.Token(tokenUserId, roomId, isOwner)

	if err != nil {
		return nil, err
	}

	return &types.LiveRtcTokenResponse{
		UserId:     tokenUserId,
		Token:      token,
		ExpireTime: expireTime.Unix(),
	}, nil
}

// @Tags 直播
// @Summary 观众列表
// @Description 观众列表
// @Produce json
// @Security HeaderAuth
// @Param param query types.LiveAudienceRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.LiveAudienceResponse}
// @Router /api/v1/live/audience [get]
func (l *Live) Audience(ctx *api.Context, req types.LiveAudienceRequest) (*types.LiveAudienceResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	r, err := l.lm.Room2(req.Id)
	if err != nil {
		l.logger.Error("audience: get room failed", zap.Error(err), zap.String("roomId", req.Id))
		return nil, errors.New("get audience failed")
	}

	const (
		maxAudience = 200
	)

	users, err := l.om.RoomTopUser(ctx, req.Id, maxAudience)
	if err != nil {
		l.logger.Error("audience: get online audience failed", zap.Error(err), zap.String("roomId", req.Id))
		return nil, errors.New("get audience failed")
	}

	var ranked *online.RoomUserRank
	if index := slices.IndexFunc(users, func(u *online.RoomUserRank) bool {
		return u.UserId == uac.UserId
	}); index != -1 {
		ranked = users[index]
	} else {
		ranked, err = l.om.RoomUserRank(ctx, req.Id, uac.UserId)
		if err != nil {
			return nil, err
		}
	}

	var (
		audiences    = make([]types.Audience, 0, len(users))
		distance     = int64(0)
		showContrib  = ranked.Score > 0
		distanceFunc = func(l, r int64) int64 {
			if l != r {
				return l - r
			}
			return 1
		}
	)

	// 火箭头像框
	rocketUser, _ := l.rm.GetRoomAvatarBorderUserList(ctx, r.SessionId.Hex())

	for i, u := range users {
		roomUser, err := mixer.RoomUser(ctx, r.UserId, u.UserId)
		if err != nil {
			return nil, err
		}

		acc, err := l.ug.Account(ctx, u.UserId)
		if err != nil {
			l.logger.Error("audience: get user failed", zap.Error(err), zap.String("userId", u.UserId))
			continue
		}

		aud := types.Audience{
			User:     *mixer.User(ctx, acc),
			RoomUser: *roomUser,
			Rank:     u.Rank,
			Score:    u.Score,
		}

		if lo.Contains(rocketUser, acc.UserId) {
			aud.User.AvatarBorder = rocket.AvatarBorder
		}

		if u.Score == 0 {
			aud.RankDesc = "-"
		} else {
			aud.RankDesc = strconv.Itoa(int(u.Rank))
		}

		if ((showContrib && ds.Abs(u.Rank-ranked.Rank) <= 5 || u.UserId == uac.UserId) && u.Score > 0) && !r.IsGameRoom() || r.IsGameRoom() && u.UserId == uac.UserId {
			aud.ScoreDesc = i18n.NumberString(ctx, float64(u.Score))
		} else {
			aud.ScoreDesc = ""
		}

		audiences = append(audiences, aud)

		if u.UserId == uac.UserId && i > 0 && showContrib {
			distance = distanceFunc(users[i-1].Score, u.Score)
		}
	}

	if distance == 0 && ranked.Rank > 1 && showContrib {
		upper, err := l.om.RoomRankUser(ctx, req.Id, ranked.Rank-1)
		if err != nil {
			return nil, err
		}

		if upper != nil {
			distance = distanceFunc(upper.Score, ranked.Score)
		}
	}

	roomUser, err := mixer.RoomUser(ctx, r.UserId, uac.UserId)
	if err != nil {
		return nil, err
	}

	mime := &types.AudienceRank{
		Audience: &types.Audience{
			User:     *mixer.User(ctx, uac),
			RoomUser: *roomUser,
			Score:    ranked.Score, // 榜单分
			Rank:     ranked.Rank,
		},
	}
	if lo.Contains(rocketUser, uac.UserId) {
		mime.User.AvatarBorder = rocket.AvatarBorder
	}

	if showContrib {
		mime.ScoreDesc = i18n.NumberString(ctx, float64(ranked.Score))
		mime.RankDesc = strconv.Itoa(int(ranked.Rank))
		if ranked.Rank > 1 && !r.IsGameRoom() {
			mime.Mark = i3n.T(ctx, "Distance to the previous %d", distance)
		}
	} else {
		mime.ScoreDesc = ""
		mime.RankDesc = "-"
	}

	return &types.LiveAudienceResponse{List: audiences, Mine: mime}, nil
}

// @Tags 直播
// @Summary 点赞
// @Description 点赞
// @Produce json
// @Security HeaderAuth
// @Param param body types.LiveLikeRequest true "请求参数"
// @Success 200 {object} codec.Response
// @Router /api/v1/live/like [post]
func (l *Live) Like(c *api.Context, req types.LiveLikeRequest) (*api.EmptyResp, error) {
	uac, err := c.User()
	if err != nil {
		return nil, err
	}

	if err := l.im.Like(context.TODO(), req.RoomId, uac.UserId, int64(req.Count)); err != nil {
		l.logger.Error("like: like failed", zap.Error(err), zap.String("roomId", req.RoomId))
		return nil, err
	}

	return nil, nil
}

// @Tags 直播
// @Summary ✅直播前人脸检测
// @Description 直播前人脸检测，每次开播必调。错误码14002：直播间未认证人脸。错误码12018：认证人脸后获得的ticket已失效（重新认证获得新ticket）
// @Produce json
// @Security HeaderAuth
// @Param param body types.LiveFaceCheckRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.LiveFaceCheckResponse}
// @Router /api/v1/live/face/check [post]
func (h *Live) FaceCheck(ctx *api.Context, req types.LiveFaceCheckRequest) (*types.LiveFaceCheckResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	userId := uac.UserId
	ticket := req.Ticket

	// 获取直播间信息
	room, err := h.lm.RoomByUserId(ctx, userId)

	if err != nil {
		return nil, err
	}

	if !room.FaceChecked() {
		if ticket == "" {
			return nil, biz.NewError(biz.ErrRoomFaceUnchecked, "need face check")
		}

		if err := h.fm.VerifyTicket(ctx, userId, ticket); err != nil {
			return nil, err
		}

		if err := h.lm.SetFace(ctx, room.Id.Hex()); err != nil {
			return nil, err
		}
	}

	return &types.LiveFaceCheckResponse{}, nil
}

// @Tags 直播
// @Summary ✅开始直播
// @Description 开始直播。错误码14002：直播间未认证人脸。错误码12018：认证人脸后获得的ticket已失效（重新认证获得新ticket）。错误码14008：直播违规封禁。错误码17011：强制退会流程中
// @Produce json
// @Security HeaderAuth
// @Param param body types.LiveStartRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.LiveStartResponse,error=types.LiveStartErrorResponse}
// @Router /api/v1/live/start [post]
func (h *Live) Start(ctx *api.Context, req types.LiveStartRequest) (*types.LiveStartResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	userId := uac.UserId
	title := req.Title
	cover := req.Cover
	ext := req.Ext

	logger := h.logger.With(
		zap.String("userId", userId),
		zap.String("title", title),
		zap.String("cover", cover),
		zap.Any("ext", ext),
	)

	startDeviceId, ok := ext[live.RoomExtDeviceId]

	if !ok {
		return nil, biz.NewError(biz.ErrInvalidParam, "ext deviceId not found")
	}

	if !strings.HasPrefix(cover, "http") {
		return nil, biz.NewError(biz.ErrInvalidParam, "cover must start with http")
	}

	l, err := h.dm.Lock(ctx, fmt.Sprintf("STR:MUTEX:LIVE:START:%s", userId))

	if err != nil {
		return nil, biz.NewError(biz.ErrBusiness, "Please try again later")
	}
	defer l.MustUnlock()

	// 获取直播间信息
	room, err := h.lm.RoomByUserId(ctx, userId)

	if err != nil {
		return nil, err
	}

	// 直播间是否已认证人脸
	if !room.FaceChecked() {
		return nil, biz.NewError(biz.ErrRoomFaceUnchecked, "need face check")
	}

	// 直播间是否被封禁
	if room.IsBanned() {
		popupTitle := live.LiveStartBanPopupTitle(ctx)

		return nil, api.NewDataErr(biz.NewError(biz.ErrRoomBanned, popupTitle), &types.LiveStartErrorResponse{
			Title:   popupTitle,
			Content: live.LiveStartBanPopupContent(ctx, room.Ban.EndTime),
		})
	}

	var result *live.StartResult

	if room.IsLiving() {
		// 判断和开播时的deviceId是否一致
		livingDeviceId, ok := room.Ext[live.RoomExtDeviceId]

		if !ok {
			return nil, biz.NewError(biz.ErrRoomDeviceIdUnmatch, "deviceId unmatch")
		}

		if startDeviceId != livingDeviceId {
			return nil, biz.NewError(biz.ErrRoomDeviceIdUnmatch, "deviceId unmatch")
		}

		result = &live.StartResult{
			RoomId:     room.Id.Hex(),
			SessionId:  room.SessionId.Hex(),
			PushStream: room.PushStream,
			Ext:        room.Ext,
		}
	} else {
		// !判断是否要自动改时区：强制转为巴西时区
		nt := time.Now()
		cc := ctz.Parse(room.CTZ)
		// 主播当前直播间时区时间
		currentTzTime := now.New(cc.In(nt)).Format("2006-01-02 15:04:05")
		// 期望的巴西时区时间
		needTzTime := now.New(nt.In(ctz.Brazil)).Format("2006-01-02 15:04:05")

		logger = logger.With(
			zap.String("roomId", room.Id.Hex()),
			zap.String("ctz", room.CTZ),
			zap.String("request", currentTzTime),
			zap.String("response", needTzTime),
		)

		// 时间不一致则修改
		if currentTzTime != needTzTime {
			today := now.New(nt.In(ctz.Brazil))
			// 是否有薪资记录：不能已有大等于今日数据的薪资记录
			exist, err := h.slm.IsExistDayData(ctx, userId, today)

			var changed bool

			if err == nil && !exist {
				// 是否有天数据：不能已有大等于今日数据的天数据
				exist, err := h.lsm.IsExistDayData(ctx, userId, today)

				if err == nil && !exist {
					err = h.lm.ResetCtz(ctx, userId)

					if err != nil {
						logger.Error("修改直播间时区为巴西", zap.Error(err))
					} else {
						logger.Info("修改直播间时区为巴西")
						changed = true
					}
				}
			}

			if !changed {
				logger.Info("直播间时区不一致，但已有数据，不修改")
			}
		}

		if err := rqa.ForceAgencyQuit(ctx, h.am, userId, req.RevokeQuitAgency.RevokeQuitAgency); err != nil {
			return nil, err
		}

		// 直播标题敏感词检查
		sws := h.reviewm.SensitiveWords(title)
		if len(sws) > 0 {
			logger.Info("直播标题敏感词检查", zap.Strings("words", sws))

			return nil, biz.NewError(biz.ErrInvalidParam, "Title contains inappropriate word. Please modify your title")
		}

		// 修改直播封面图，内容安全检查
		if room.Cover != cover {
			if rr, err := h.reviewm.ReviewImage(ctx, cover); err != nil {
				logger.Error("直播封面图内容安全检查", zap.Error(err))

				return nil, biz.NewError(biz.ErrBusiness, "Please try again later")
			} else if rr.RiskLevel == review.RiskLevelHigh {
				logger.Info("直播封面图内容安全检查", zap.Any("review", rr))

				return nil, biz.NewError(biz.ErrInvalidParam, "Inappropriate live cover. Modify your live cover")
			}
		}

		result, err = h.lm.Start(
			ctx,
			userId,
			&live.Room{
				UserId: userId,
				Title:  title,
				Cover:  cover,
				Ext:    ext,
			},
		)

		if err != nil {
			return nil, err
		}
	}

	token, expireTime, err := h.rtcm.Token(userId, result.RoomId, true)

	if err != nil {
		return nil, err
	}

	return &types.LiveStartResponse{
		RoomId:     result.RoomId,
		SessionId:  result.SessionId,
		PushStream: result.PushStream,
		Ext:        result.Ext,
		StartTime:  result.StartTime.Unix(),
		Token:      token,
		ExpireTime: expireTime.Unix(),
	}, nil
}

// @Tags 直播
// @Summary ✅结束直播
// @Description 结束直播
// @Produce json
// @Security HeaderAuth
// @Param param body types.LiveStopRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.LiveStopResponse}
// @Router /api/v1/live/stop [post]
func (h *Live) Stop(ctx *api.Context, req types.LiveStopRequest) (*types.LiveStopResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	userId := uac.UserId

	err = h.lm.Stop(
		ctx,
		userId,
		req.Reason,
	)

	if err != nil {
		h.logger.Error("stop live failed", zap.Error(err), zap.String("userId", userId))
		return nil, err
	}

	return &types.LiveStopResponse{}, nil
}

// @Tags 直播
// @Summary ✅直播心跳
// @Description 直播心跳，每x秒请求
// @Produce json
// @Security HeaderAuth
// @Param param body types.LiveHeartbeatRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.LiveHeartbeatResponse}
// @Router /api/v1/live/heartbeat [post]
func (h *Live) Heartbeat(ctx *api.Context, req types.LiveHeartbeatRequest) (*types.LiveHeartbeatResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	anchorId := uac.UserId

	if err := h.lm.Heartbeat(ctx, anchorId, req.Stream); err != nil {
		return nil, err
	}

	// 获取直播警告
	var warning *types.LiveWarning

	w, err := h.wm.Get(ctx, anchorId)

	if err == nil && w.Id != "" {
		warning = &types.LiveWarning{
			Id:          w.Id,
			Title:       w.Title,
			Content:     w.Content,
			LiveEndTime: w.LiveEndTime,
		}
	}

	return &types.LiveHeartbeatResponse{
		Warning: warning,
	}, nil
}

// @Tags 直播
// @Summary ✅直播暂离
// @Description 直播暂离
// @Produce json
// @Security HeaderAuth
// @Success 200 {object} codec.Response{data=api.EmptyResp}
// @Router /api/v1/live/pause [post]
func (h *Live) Pause(ctx *api.Context, _ api.EmptyReq) (*api.EmptyResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	if err := h.lm.Pause(ctx, uac.UserId, live.PauseSceneManual); err != nil {
		return nil, err
	}

	return &api.EmptyResp{}, nil
}

// @Tags 直播
// @Summary ✅直播暂离恢复
// @Description 直播暂离恢复
// @Produce json
// @Security HeaderAuth
// @Success 200 {object} codec.Response{data=api.EmptyResp}
// @Router /api/v1/live/resume [post]
func (h *Live) Resume(ctx *api.Context, _ api.EmptyReq) (*api.EmptyResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	if err := h.lm.Resume(ctx, uac.UserId, live.PauseSceneManual); err != nil {
		return nil, err
	}

	return &api.EmptyResp{}, nil
}

// @Tags 直播
// @Summary ✅直播结束数据
// @Description 直播结束页直播场次数据
// @Produce json
// @Security HeaderAuth
// @Param param query types.LiveRecordRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.LiveRecordResponse}
// @Router /api/v1/live/record [get]
func (l *Live) Record(ctx *api.Context, req types.LiveRecordRequest) (*types.LiveRecordResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	session, err := l.lm.Session(req.SessionId)

	if err != nil {
		return nil, err
	}

	if session.UserId != uac.UserId {
		return nil, biz.NewError(biz.ErrInvalidParam, "invalid session")
	}

	if !session.IsEnd() {
		return nil, biz.NewError(biz.ErrRoomLiving, "session not end")
	}

	var termination *types.LiveTermination

	if session.Termination.Content != "" {
		termination = &types.LiveTermination{
			Content: session.Termination.Content,
		}
	}

	return &types.LiveRecordResponse{
		RoomId:        session.RoomId,
		SessionId:     session.Id.Hex(),
		StartTime:     session.StartTime.Unix(),
		EndTime:       session.EndTime.Unix(),
		Duration:      session.Duration,
		PauseDuration: session.PauseDuration,
		LuckDiamond:   session.LuckDiamond,
		GiftDiamond:   session.GiftDiamond,
		NewFanCount:   session.NewFanCount,
		AudCount:      session.AudCount,
		GiftUserCount: session.GiftUserCount,
		LikeCount:     session.LikeCount,
		Termination:   termination,
	}, nil
}

// @Tags 直播
// @Summary ✅直播动态
// @Description 直播动态，历史直播回顾
// @Produce json
// @Security HeaderAuth
// @Param param query types.LiveMomentRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.LiveMomentResponse}
// @Router /api/v1/live/moment [get]
func (l *Live) Moment(ctx *api.Context, req types.LiveMomentRequest) (*types.LiveMomentResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	userId := uac.UserId
	anchorId := req.UserId

	setting, err := l.pm.Take(ctx, anchorId)

	if err != nil {
		return nil, err
	}

	list := make([]types.LiveMomentSession, 0)

	if !setting.HideLiveSession {
		sessions, err := l.lm.LiveSessionsIn1Year(ctx, anchorId)

		if err != nil {
			return nil, err
		}

		for _, s := range sessions {
			list = append(list, types.LiveMomentSession{
				StartTime: s.StartTime.Unix(),
				EndTime:   s.EndTime.Unix(),
				Title:     s.Title,
			})
		}
	}

	notice, _ := l.lm.HasNotice(ctx, userId, anchorId)

	return &types.LiveMomentResponse{
		Notice:   notice,
		Hidden:   setting.HideLiveSession,
		Sessions: list,
	}, nil
}

// @Tags 直播
// @Summary ✅设置开播提醒
// @Description 设置开播提醒
// @Produce json
// @Security HeaderAuth
// @Param param body types.LiveNoticeSetRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.LiveNoticeSetResponse}
// @Router /api/v1/live/notice/set [post]
func (h *Live) SetNotice(ctx *api.Context, req types.LiveNoticeSetRequest) (*types.LiveNoticeSetResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	_, err = h.lm.RoomByUserId2(ctx, req.UserId)

	if err != nil {
		return nil, err
	}

	err = h.lm.SetNotice(ctx, uac.UserId, req.UserId, req.Notice)

	if err != nil {
		return nil, err
	}

	return &types.LiveNoticeSetResponse{}, nil
}

// @Tags 直播
// @Summary ✅预约下一场直播
// @Description 预约下一场直播
// @Produce json
// @Security HeaderAuth
// @Param param body types.LiveAppointmentSetRequest true "请求参数"
// @Success 200 {object} codec.Response{data=api.EmptyResp}
// @Router /api/v1/live/appointment/set [post]
func (h *Live) SetAppointment(ctx *api.Context, req types.LiveAppointmentSetRequest) (*api.EmptyResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	_, err = h.lm.RoomByUserId2(ctx, req.UserId)

	if err != nil {
		return nil, err
	}

	err = h.lm.MakeAppointment(ctx, uac.UserId, req.UserId)

	if err != nil {
		return nil, err
	}

	return &api.EmptyResp{}, nil
}

type SetIntroductionRequest struct {
	Enable bool   `json:"enable"`
	Text   string `json:"text"`
}

// @Tags 直播
// @Summary 设置直播间介绍
// @Description 设置直播间介绍
// @Produce json
// @Security HeaderAuth
// @Param param body SetIntroductionRequest true "请求参数"
// @Success 200 {object} codec.Response{data=api.EmptyResp}
// @Router /api/v1/live/introduction/set [post]
func (h *Live) SetIntroduction(ctx *api.Context, req SetIntroductionRequest) (*api.EmptyResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	if utf8.RuneCountInString(req.Text) > live.IntroductionMaxLength {
		return nil, biz.NewError(biz.ErrInvalidParam, "introduction too long")
	}

	if err := h.lm.SetIntroduction(ctx, uac.UserId, req.Enable, req.Text); err != nil {
		return nil, err
	}

	return &api.EmptyResp{}, nil
}

type GetIntroductionResponse struct {
	Enable bool   `json:"enable"`
	Text   string `json:"text"`
}

// @Tags 直播
// @Summary 获取直播间介绍
// @Description 获取直播间介绍
// @Produce json
// @Security HeaderAuth
// @Param param body api.EmptyReq true "请求参数"
// @Success 200 {object} codec.Response{data=GetIntroductionResponse}
// @Router /api/v1/live/introduction [get]
func (h *Live) GetIntroduction(ctx *api.Context, _ api.EmptyReq) (*GetIntroductionResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	ri, err := h.lm.RoomByUserId2(ctx, uac.UserId)
	if err != nil {
		return nil, err
	}

	return &GetIntroductionResponse{Enable: ri.IntroductionEnable, Text: ri.Introduction}, nil
}

// @Tags 直播
// @Summary ✅用户主页直播页签
// @Description 用户主页直播页签
// @Produce json
// @Security HeaderAuth
// @Param param query types.LiveProfileRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.LiveProfileResponse}
// @Router /api/v1/live/profile [get]
func (h *Live) Profile(ctx *api.Context, req types.LiveProfileRequest) (*types.LiveProfileResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	anchorId := req.UserId

	self := uac.UserId == anchorId

	var sessionCount int64
	var income int64

	// 直播间信息
	room, err := h.lm.RoomByUserId2(ctx, anchorId)

	if err != nil {
		return nil, err
	}

	nt := now.New(ctz.Parse(room.CTZ).In(time.Now()))

	startDay := nt.AddDate(0, 0, -6)
	endDay := nt.EndOfDay()

	sessionCount, err = h.lm.SessionCount(ctx, anchorId, startDay, endDay)

	if err != nil {
		return nil, err
	}

	if self {
		income, err = h.lsm.GetIncome(ctx, anchorId, startDay, endDay, nt)

		if err != nil {
			return nil, err
		}
	}

	return &types.LiveProfileResponse{
		Self:         self,
		SessionCount: sessionCount,
		Income:       income,
	}, nil
}

// @Tags 直播
// @Summary ✅送礼统计
// @Description 送礼统计
// @Produce json
// @Security HeaderAuth
// @Param param query types.LiveGiftStatRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.LiveGiftStatResponse}
// @Router /api/v1/live/gift/stat [get]
func (h *Live) GiftStat(ctx *api.Context, req types.LiveGiftStatRequest) (*types.LiveGiftStatResponse, error) {
	room, err := h.lm.Room2(req.RoomId)

	if err != nil {
		return nil, err
	}

	var giftDiamond int64
	var luckDiamond int64

	// 非游戏直播间显示礼物统计
	if !room.IsGameRoom() {
		lsd, err := h.lsm.GetLiveSummaryDay(ctx, room.UserId, ctz.Parse(room.CTZ).In(time.Now()))

		if err == nil {
			giftDiamond = lsd.GiftDiamond
			luckDiamond = lsd.LuckDiamond
		}
	}

	var task *task.AnchorTask

	if req.WithTask == 1 {
		task, err = h.tm.GetLuckDiamondTask(ctx, luckDiamond)

		if err != nil {
			return nil, err
		}
	}

	return &types.LiveGiftStatResponse{
		LuckDiamondTask: task,
		GiftDiamond:     giftDiamond,
		LuckDiamond:     luckDiamond,
		Total:           luckDiamond,
	}, nil
}

// @Tags 直播
// @Summary ✅封禁/解封用户音视频流
// @Description 封禁/解封用户音视频流
// @Produce json
// @Security HeaderAuth
// @Param param body types.LiveRtcStreamOperateRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.LiveRtcStreamOperateResponse}
// @Router /api/v1/live/rtc/stream/operate [post]
func (h *Live) OperateStream(ctx *api.Context, req types.LiveRtcStreamOperateRequest) (*types.LiveRtcStreamOperateResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	operatorUserId := uac.UserId
	userId := req.UserId
	roomId := req.RoomId
	operate := req.Operate

	room, err := h.lm.Room2(roomId)

	if err != nil {
		return nil, err
	}

	if room.UserId != operatorUserId {
		return nil, biz.NewError(biz.ErrBusiness, "permission denied")
	}

	switch operate {
	case "ban":
		if operatorUserId == userId {
			return nil, biz.NewError(biz.ErrInvalidParam, "cannot ban self")
		}

		err = h.rtcm.BanUserStream(ctx, operatorUserId, userId, roomId, true, false)
	case "unban":
		err = h.rtcm.UnbanUserStream(ctx, operatorUserId, userId, roomId, true, false)
	default:
		return nil, biz.NewError(biz.ErrInvalidParam, "invalid operate")
	}

	if err != nil {
		h.logger.Error(
			"operate stream failed",
			zap.Error(err),
			zap.String("operate", operate),
			zap.String("operatorUserId", operatorUserId),
			zap.String("userId", userId),
			zap.String("roomId", roomId),
		)
		return nil, err
	}

	return &types.LiveRtcStreamOperateResponse{}, nil
}

// @Tags 直播
// @Summary 直播间状态数据（包含暂离、PK状态）
// @Description 直播间状态数据（包含暂离、PK状态）,该接口缓存500ms
// @Produce json
// @Param param query types.LiveRoomStatusRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.LiveRoomStatusResponse}
// @Router /api/v1/live/room/status [get]
func (l *Live) RoomStatus(ctx *api.Context, req types.LiveRoomStatusRequest) (*types.LiveRoomStatusResponse, error) {
	if req.Id == "" && req.UserId == "" {
		return nil, biz.NewError(biz.ErrInvalidParam, "invalid params")
	}

	var (
		room *live.Room
		err  error
	)

	if req.Id != "" {
		room, err = l.lm.Room2(req.Id)
	} else {
		room, err = l.lm.RoomByUserIdReadOnly2(ctx, req.UserId)
	}

	if err != nil {
		return nil, err
	}

	out := &types.LiveRoomStatusResponse{}
	if room.IsLiving() {
		out.Status |= protocol.LStatusLive
		out.Paused = room.Pause.Paused()
	} else {
		return out, nil
	}

	sess, err := l.pkm.UserCurSession(ctx, room.UserId)
	if err != nil {
		return nil, err
	}

	if sess != nil {
		out.Status = pk.MakeStatus(sess.Status)

		out.PKStatus, err = l.pkm.BuildPKS(ctx, sess, time.Now())
		if err != nil {
			return nil, err
		}
	} else {
		out.LinkStatus, err = l.lk.LinkStatus(ctx, room.Id.Hex())
		if err != nil {
			return nil, err
		}

		if out.LinkStatus != nil && len(out.LinkStatus.Linked) > 0 {
			out.Status |= protocol.LStatusLink
		}
	}

	return out, nil
}

// @Tags 直播
// @Summary 直播警告-操作选项（巡管账号）
// @Description 直播警告-操作选项
// @Produce json
// @Success 200 {object} codec.Response{data=types.LiveWarningOptionsResponse}
// @Router /api/v1/live/warning/options [get]
func (l *Live) WarningOptions(ctx *api.Context, req api.EmptyReq) (*types.LiveWarningOptionsResponse, error) {
	wol := make([]types.LiveWarnOption, 0)
	bol := make([]types.LiveBanOption, 0)

	ws := l.wm.WarnOptions(ctx)

	for _, w := range ws {
		wol = append(wol, types.LiveWarnOption{
			Id:     w.Id,
			Reason: w.Reason,
		})
	}

	bs := l.wm.BanOptions(ctx)

	for _, b := range bs {
		bol = append(bol, types.LiveBanOption{
			Name:    b.Name,
			Seconds: b.Seconds,
		})
	}

	return &types.LiveWarningOptionsResponse{
		WarnOptions: wol,
		BanOptions:  bol,
	}, nil
}

// @Tags 直播
// @Summary 直播警告-发出警告（巡管账号）
// @Description 直播警告-发出警告
// @Produce json
// @Security HeaderAuth
// @Param param body types.LiveWarningWarnRequest true "请求参数"
// @Success 200 {object} codec.Response
// @Router /api/v1/live/warning/warn [post]
func (h *Live) WarningWarn(ctx *api.Context, req types.LiveWarningWarnRequest) (*api.EmptyResp, error) {
	anchorId := req.UserId
	id := req.Id

	if err := h.ppm.WarningWarn(ctx, anchorId, id); err != nil {
		return nil, err
	}

	return nil, nil
}

// @Tags 直播
// @Summary 直播警告-中断直播（巡管账号）
// @Description 直播警告-中断直播
// @Produce json
// @Security HeaderAuth
// @Param param body types.LiveWarningBanRequest true "请求参数"
// @Success 200 {object} codec.Response
// @Router /api/v1/live/warning/ban [post]
func (h *Live) WarningBan(ctx *api.Context, req types.LiveWarningBanRequest) (*api.EmptyResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	anchorId := req.UserId
	seconds := req.Seconds

	if err := h.ppm.WarningBan(ctx, anchorId, seconds, uac.UserId); err != nil {
		return nil, err
	}

	return nil, nil
}

// @Tags 直播
// @Summary 主播确认直播警告
// @Description 主播确认直播警告
// @Produce json
// @Security HeaderAuth
// @Param param body types.LiveWarningConfirmRequest true "请求参数"
// @Success 200 {object} codec.Response
// @Router /api/v1/live/warning/confirm [post]
func (h *Live) WarningConfirm(ctx *api.Context, req types.LiveWarningConfirmRequest) (*api.EmptyResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	anchorId := uac.UserId

	if err := h.ppm.WarningConfirm(ctx, anchorId); err != nil {
		return nil, err
	}

	return nil, nil
}

// @Tags 直播
// @Summary 直播警告-本场直播不分发（巡管账号）
// @Description 直播警告-本场直播不分发
// @Produce json
// @Security HeaderAuth
// @Param param body types.LiveWarningHideRequest true "请求参数"
// @Success 200 {object} codec.Response
// @Router /api/v1/live/warning/hide [post]
func (h *Live) WarningHide(ctx *api.Context, req types.LiveWarningHideRequest) (*api.EmptyResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	anchorId := req.UserId

	if err := h.ppm.WarningHide(ctx, anchorId, uac.UserId); err != nil {
		return nil, err
	}

	return &api.EmptyResp{}, nil
}

func (h *Live) RtcCallback(ctx *gin.Context) error {
	var event rtc.Event

	logger := h.logger

	if err := ctx.BindJSON(&event); err != nil {
		logger.Error("RTC回调通知", zap.Error(err))

		ctx.JSON(http.StatusBadRequest, gin.H{})
		ctx.Abort()

		return nil
	}

	logger = logger.With(
		zap.Any("request", event),
	)

	err := h.rtcm.Callback(ctx, &event)

	if err != nil {
		logger.Error("RTC回调通知", zap.Error(err))
		ctx.JSON(http.StatusInternalServerError, gin.H{})
		ctx.Abort()

		return nil
	}

	logger.Info("RTC回调通知")

	return nil
}

type SyncMuteRequest struct {
	Mute bool `json:"mute"` // 是否静音
}

// @Tags 直播
// @Summary 同步静音状态
// @Description 同步静音状态
// @Produce json
// @Security HeaderAuth
// @Param param body SyncMuteRequest true "请求参数"
// @Success 200 {object} api.EmptyResp
// @Router /api/v1/live/mute/sync [post]
func (h *Live) SyncMute(ctx *api.Context, req SyncMuteRequest) (*api.EmptyResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	ri, err := h.lm.RoomByUserId2(ctx, uac.UserId)
	if err != nil {
		return nil, err
	}

	if !ri.IsLiving() {
		return nil, biz.NewError(biz.ErrRoomNotLiving, "room not living")
	}

	if err := h.sm.UpdateSessionMute(ctx, ri.SessionId.Hex(), req.Mute); err != nil {
		return nil, err
	}

	return &api.EmptyResp{}, nil
}

// @Tags 直播
// @Summary 直播间状态检查
// @Description 直播间状态检查
// @Produce json
// @Security HeaderAuth
// @Param param body types.LiveOnlineCheckRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.LiveOnlineCheckResponse}
// @Router /api/v1/live/online/check [post]
func (h *Live) OnlineCheck(ctx *api.Context, req types.LiveOnlineCheckRequest) (*types.LiveOnlineCheckResponse, error) {
	roomIds := req.RoomIds

	liveRoomIds := make([]string, 0)

	for _, roomId := range roomIds {
		room, err := h.lm.Room2(roomId)

		if err != nil {
			continue
		}

		if room.IsLiving() {
			liveRoomIds = append(liveRoomIds, roomId)
		}

		if len(liveRoomIds) >= 50 {
			break
		}
	}

	return &types.LiveOnlineCheckResponse{RoomIds: liveRoomIds}, nil
}
