package handler

import (
	"context"
	"fmt"
	"time"

	"github.com/samber/lo"
	"gitlab.sskjz.com/go/i3n"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/mixer"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/internal/gift"
	"gitlab.sskjz.com/overseas/live/osl/internal/live"
	"gitlab.sskjz.com/overseas/live/osl/internal/room/interact"
	"gitlab.sskjz.com/overseas/live/osl/pkg/app"
	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ctz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/mux"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/rlog"
	"go.uber.org/zap"
)

func InvokeGiftHandler(
	r *api.Router, hm *mux.Locker,
	gm *gift.Manager,
	im *interact.Manager,
	lm *live.Manager,
	vnd log.Vendor,
) *Gift {
	g := &Gift{
		gm:     gm,
		im:     im,
		lm:     lm,
		logger: vnd.Scope("api.gift"),
	}

	ar := r.WithAuth()
	{
		// 送礼
		ar.POST("/gift/send", api.Generic(g.Send))
	}

	tr := r.TryAuth()
	{
		// 礼物列表
		tr.GET("/gift/list", api.Generic(g.List))
	}

	return g
}

type Gift struct {
	gm     *gift.Manager
	im     *interact.Manager
	lm     *live.Manager
	logger *zap.Logger
}

// @Tags 礼物
// @Summary 列表
// @Description 列表
// @Produce json
// @Param param query types.GiftListRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.GiftListResponse}
// @Router /api/v1/gift/list [get]
func (g *Gift) List(c *api.Context, req types.GiftListRequest) (*types.GiftListResponse, error) {
	res, err := g.gm.GiftPageList(c)

	if err != nil {
		return nil, err
	}

	var (
		pages []types.GiftPage
	)

	var isLuckyRoom bool

	anchorId := req.UserId

	if anchorId != "" {
		room, err := g.lm.RoomByUserId2(c, anchorId)

		if err != nil {
			return nil, err
		}

		isLuckyRoom = room.IsLuckyRoom()
	}

	act519StartTime := time.Date(2025, 5, 19, 0, 0, 0, 0, ctz.Brazil)

	frontGiftIds := []uint{10080, 10081, 10082, 10083, 10084}

	hideGiftIds := []uint{}

	if time.Now().After(act519StartTime) {
		hideGiftIds = append(hideGiftIds, []uint{10063}...)
	} else {
		hideGiftIds = append(hideGiftIds, []uint{10085, 10086}...) // 新等级粉丝团礼物
		hideGiftIds = append(hideGiftIds, frontGiftIds...)         // 新特效礼物
		frontGiftIds = []uint{10070, 10071, 10072, 10073, 10074, 10075}
	}

	for _, p := range res {
		// 幸运礼物体验房只显示幸运礼物
		if isLuckyRoom && p.Page.Key != gift.PageKeyLucky {
			continue
		}

		if p.Page.Key == gift.PageKeyFansclub && !app.CVGTE(c, app.V1100, 55) {
			p.Page.Display = false
		}

		gifts := make([]types.Gift, 0, len(p.Gifts))

		frontGifts := make([]types.Gift, 0)

		for _, pg := range p.Gifts {
			if lo.Contains(hideGiftIds, pg.ID) {
				pg.Display = false
			}

			if app.CVGTE(c, app.V180, 47) {
				pg.Describe = fmt.Sprintf("%s %s", i3n.T(c, "Send"), pg.Describe)
			}

			mg := *mixer.Gift(&pg)

			// 特殊处理，新上6个礼物排在前面
			if lo.Contains(frontGiftIds, pg.ID) {
				frontGifts = append(frontGifts, mg)
			} else {
				gifts = append(gifts, mg)
			}
		}

		if len(frontGifts) > 0 {
			gifts = append(frontGifts, gifts...)
		}

		pages = append(pages, types.GiftPage{
			PageId:   p.Page.ID,
			PageName: p.Page.Name,
			Key:      p.Page.Key,
			Display:  p.Page.Display,
			Gifts:    gifts,
		})
	}

	return &types.GiftListResponse{
		Pages: pages,
	}, nil
}

// @Tags 礼物
// @Summary 送礼
// @Description 送礼
// @Produce json
// @Security HeaderAuth
// @Param param body types.GiftSendRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.GiftSendResponse}
// @Router /api/v1/gift/send [post]
func (g *Gift) Send(ctx *api.Context, req types.GiftSendRequest) (*types.GiftSendResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	gft, err := g.gm.GiftById(req.GiftId)
	if err != nil {
		return nil, err
	}

	if len(req.ComboId) > 512 {
		return nil, biz.NewError(biz.ErrInvalidParam, "invalid params")
	}

	ri, err := g.lm.Room2(req.RoomId)
	if err != nil {
		return nil, err
	}

	if ri.IsLuckyRoom() {
		return nil, biz.NewError(biz.ErrInvalidParam, "invalid params")
	}

	sr, err := g.im.SendGift(context.TODO(), rlog.RequestId(ctx), req.RoomId, uac.UserId, req.ComboId, req.GiftId, req.GiftCount, req.Receivers, time.Now())
	if err != nil {
		return nil, err
	}

	var resp types.GiftSendResponse

	if sr.Wallet != nil {
		resp.Wallet = &types.UserWallet{
			Diamond: sr.Wallet.BVal(fund.PTypeDiamond).IntPart(),
		}
	}

	if sr.Level != nil {
		resp.LevelInfo = mixer.LevelInfo(sr.Level)
	}

	if !sr.Pows.Empty() {
		resp.LuckDraw = &types.LuckDraw{
			GiftId:    req.GiftId,
			GiftCount: req.GiftCount,
			Prizes:    sr.Pows.Protocol(gft.Diamond),
		}
	}

	return &resp, nil
}
