package handler

import (
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/mux"
	"strings"
	"time"

	"gitlab.sskjz.com/overseas/live/osl/pkg/app"

	"github.com/samber/lo"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/mixer"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/upload"
	"gitlab.sskjz.com/overseas/live/osl/internal/follow"
	"gitlab.sskjz.com/overseas/live/osl/internal/live"
	"gitlab.sskjz.com/overseas/live/osl/internal/moment"
	"gitlab.sskjz.com/overseas/live/osl/internal/review"
	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/sto"
	"gitlab.sskjz.com/overseas/live/osl/pkg/sts"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
)

// InvokeMomentHandler 动态接口
func InvokeMomentHandler(
	r *api.Router,
	us *upload.Scenes,
	ug user.Getter,
	mm *moment.Manager,
	fg follow.Getter,
	sto sto.Instance,
	sts *sts.Mgr,
	lg *live.Manager,
	reviewm *review.Manager,
	ml *mux.Locker,
) {
	sc := sto.Conf("moment")
	m := NewMoment(ug, mm, fg, sc, sts, lg, reviewm)

	// public接口
	{
		r.GET("/public/moment/get", api.Generic(m.PublicGet))
	}

	ta := r.TryAuth(ml.Middleware(mux.WithPOST))
	{
		// 评论列表
		ta.GET("/moment/comment/list", api.Generic(m.CommentList))
		// 回复列表
		ta.GET("/moment/comment/reply", api.Generic(m.CommentReply))
		// feed
		ta.GET("/moment/feed", api.Generic(m.Feed))
		// read
		ta.POST("/moment/read", api.Generic(m.Read))
		// 动态点赞列表
		ta.GET("/moment/like/list", api.Generic(m.LikeList))
	}

	ar := r.WithAuth(ml.Middleware(mux.WithPOST))
	{
		// 获取指定动态
		ar.GET("/moment/get", api.Generic(m.Get))
		// 动态发布
		ar.POST("/moment/publish", api.Generic(m.Publish))
		// 动态修改
		ar.POST("/moment/modify", api.Generic(m.Modify))
		// "我的"动态列表
		ar.GET("/moment/user/own", api.Generic(m.UserOwn))
		// "我的"点赞列表
		ar.GET("/moment/user/like", api.Generic(m.UserLike))
		// 动态删除
		ar.POST("/moment/delete", api.Generic(m.Delete))
		// 点赞
		ar.POST("/moment/like", api.Generic(m.Like))
		// 取消点赞
		ar.POST("/moment/unlike", api.Generic(m.Unlike))
		// 动态分享成功（增加分享次数）
		ar.POST("/moment/share/success", api.Generic(m.ShareSuccess))
		// 动态举报
		ar.POST("/moment/report", api.Generic(m.Report))
		// 动态可见状态更改
		ar.POST("/moment/visible", api.Generic(m.Visible))
		// 动态置顶
		ar.POST("/moment/top", api.Generic(m.Top))
		// 动态取消置顶
		ar.POST("/moment/untop", api.Generic(m.Untop))
		// 发布评论
		ar.POST("/moment/comment/publish", api.Generic(m.CommentPublish))
		// 删除评论
		ar.POST("/moment/comment/delete", api.Generic(m.CommentDelete))
		// 举报评论
		//ar.POST("/moment/comment/report", api.Generic())
		// 评论、回复点赞
		ar.POST("/moment/comment/like", api.Generic(m.CommentLike))
		// 评论、回复取消点赞
		ar.POST("/moment/comment/unlike", api.Generic(m.CommentUnlike))
		// 评论、回复点赞
		ar.POST("/moment/comment/fold", api.Generic(m.CommentFold))
		// 评论、回复取消点赞
		ar.POST("/moment/comment/unfold", api.Generic(m.CommentUnFold))
	}

	{
		us.Add("moment", us.Classic("moment", moment.PictureStyle))
	}
}

type Moment struct {
	ug      user.Getter
	mm      *moment.Manager
	fg      follow.Getter
	sto     sto.Conf
	sts     *sts.Mgr
	lg      *live.Manager
	reviewm *review.Manager
}

func NewMoment(
	ug user.Getter,
	mm *moment.Manager,
	fg follow.Getter,
	sto sto.Conf,
	sts *sts.Mgr,
	lg *live.Manager,
	reviewm *review.Manager,
) *Moment {
	return &Moment{
		ug:      ug,
		mm:      mm,
		fg:      fg,
		sto:     sto,
		sts:     sts,
		lg:      lg,
		reviewm: reviewm,
	}
}

// @Tags 动态
// @Summary 获取动态信息
// @Description 获取动态信息
// @Produce json
// @Security HeaderAuth
// @Param param query types.MomentGetRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.MomentGetResponse}
// @Router /api/v1/moment/get [get]
func (m *Moment) Get(ctx *api.Context, req types.MomentGetRequest) (*types.MomentGetResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	v, err := m.mm.MomentGet(ctx, req.MId, uac.UserId)
	if err != nil {
		return nil, err
	}

	var followed bool
	if v.Author != nil {
		followed, err = m.fg.Following(ctx, uac.UserId, v.Author.UserId)
		if err != nil {
			return nil, err
		}
	}

	return &types.MomentGetResponse{
		MomentFeedItem: types.MomentFeedItem{
			MId:           v.MId,
			MomentType:    v.MomentType,
			Author:        mixer.User(ctx, v.Author),
			Followed:      followed,
			Title:         v.Title,
			Desc:          v.Desc,
			DescExtra:     v.DescExtra,
			CharacterInfo: v.CharacterInfo,
			PictureInfo:   v.PictureInfo,
			VideoInfo:     v.VideoInfo,
			OptionAllow: &moment.MomentOption{
				CanComment:     true,
				CanShowComment: true,
				CanShare:       true,
			},
			IsTop:      v.IsTop,
			Statistics: v.Statistics,
			Status:     v.Status,
			CreateTime: v.CreateTime,
		},
	}, nil
}

// @Tags 动态
// @Summary 获取动态信息（public无需登录）
// @Description 获取动态信息（public无需登录）
// @Produce json
// @Param param query types.MomentGetRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.MomentGetResponse}
// @Router /api/v1/public/moment/get [get]
func (m *Moment) PublicGet(ctx *api.Context, req types.MomentGetRequest) (*types.MomentGetResponse, error) {
	v, err := m.mm.MomentGet(ctx, req.MId, "")
	if err != nil {
		return nil, err
	}

	return &types.MomentGetResponse{
		MomentFeedItem: types.MomentFeedItem{
			MId:           v.MId,
			MomentType:    v.MomentType,
			Author:        mixer.User(ctx, v.Author),
			Followed:      false,
			Title:         v.Title,
			Desc:          v.Desc,
			DescExtra:     v.DescExtra,
			CharacterInfo: v.CharacterInfo,
			PictureInfo:   v.PictureInfo,
			VideoInfo:     v.VideoInfo,
			OptionAllow: &moment.MomentOption{
				CanComment:     true,
				CanShowComment: true,
				CanShare:       true,
			},
			IsTop:      v.IsTop,
			Statistics: v.Statistics,
			Status:     v.Status,
			CreateTime: v.CreateTime,
		},
	}, nil
}

// @Tags 动态
// @Summary 动态feed
// @Description 动态feed
// @Produce json
// @Security HeaderAuth
// @Param param query types.MomentFeedRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.MomentFeedResponse}
// @Router /api/v1/moment/feed [get]
func (m *Moment) Feed(ctx *api.Context, req types.MomentFeedRequest) (*types.MomentFeedResponse, error) {
	var (
		userId string
		err    error
	)
	uac, err := ctx.User()
	if err == nil {
		userId = uac.UserId
	}
	
	var (
		// ios审核代码
		isIOSVerify = app.DeviceType(ctx) == app.IOS && app.InReview(app.Version(ctx))
		feed        []moment.Moment
		cursor      int
	)
	feed, cursor, err = m.mm.MomentFeedByTime(ctx, userId, req.Cursor, req.Count, isIOSVerify)
	if err != nil {
		return nil, err
	}

	try := 1
	for req.Cursor == 0 && len(feed) == 0 && try <= 3 {
		feed, cursor, err = m.mm.MomentFeedByTime(ctx, userId, req.Cursor+req.Count*try, req.Count, isIOSVerify)
		if err != nil {
			return nil, err
		}
		try++
	}

	list := make([]types.MomentFeedItem, 0, len(feed))
	for _, v := range feed {
		var followed bool
		if v.Author != nil && userId != "" {
			followed, err = m.fg.Following(ctx, userId, v.Author.UserId)
			if err != nil {
				return nil, err
			}
		}

		item := types.MomentFeedItem{
			MId:           v.MId,
			MomentType:    v.MomentType,
			Author:        mixer.User(ctx, v.Author),
			Followed:      followed,
			Title:         v.Title,
			Desc:          v.Desc,
			DescExtra:     v.DescExtra,
			CharacterInfo: v.CharacterInfo,
			PictureInfo:   v.PictureInfo,
			VideoInfo:     v.VideoInfo,
			OptionAllow: &moment.MomentOption{
				CanComment:     true,
				CanShowComment: true,
				CanShare:       true,
			},
			IsTop:      v.IsTop,
			Statistics: v.Statistics,
			Status:     v.Status,
			CreateTime: v.CreateTime,
		}

		list = append(list, item)
	}

	return &types.MomentFeedResponse{
		List:   list,
		Cursor: cursor,
	}, nil
}

// @Tags 动态
// @Summary 动态已读
// @Description 动态已读
// @Produce json
// @Security HeaderAuth
// @Param param body types.MomentReadRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.MomentReadResponse}
// @Router /api/v1/moment/read [post]
func (m *Moment) Read(ctx *api.Context, req types.MomentReadRequest) (*types.MomentReadResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return &types.MomentReadResponse{}, nil
	}

	m.mm.Read(ctx, req.Mid, uac.UserId)

	return &types.MomentReadResponse{}, nil
}

// @Tags 动态
// @Summary 动态发布
// @Description 动态发布
// @Produce json
// @Security HeaderAuth
// @Param param body types.MomentPublishRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.MomentPublishResponse}
// @Router /api/v1/moment/publish [post]
func (m *Moment) Publish(ctx *api.Context, req types.MomentPublishRequest) (*types.MomentPublishResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	userAccount, err := m.ug.Account(ctx, uac.UserId)
	if err != nil {
		return nil, err
	}

	mmReq := moment.MomentPublish{
		MomentType:    req.MomentType,
		Title:         req.Title,
		Desc:          req.Desc,
		DescExtra:     req.DescExtra,
		VisibleStatus: req.VisibleStatus,
	}

	var (
		pictureInfo = new(moment.MomentPicture)
		videoInfo   = new(moment.MomentVideo)
	)
	switch req.MomentType {
	case moment.TypeMomentCharacter:
		if req.CharacterInfo != nil {
			mmReq.CharacterInfo = &moment.MomentCharacter{Data: req.CharacterInfo.Data}
		}
	case moment.TypeMomentPicture:
		if req.PictureInfo != nil {
			mmReq.PictureInfo = &moment.MomentPicture{PictureUrls: req.PictureInfo.PictureUrls}
			urls := make([]string, 0, len(pictureInfo.PictureUrls))
			for _, v := range req.PictureInfo.PictureUrls {
				if !strings.HasPrefix(v, "http") {
					urls = append(urls, m.sto.ExternalURL(v, moment.PictureStyle))
				} else {
					urls = append(urls, v)
				}
			}
			pictureInfo.PictureUrls = urls
		}
	case moment.TypeMomentVideo:
		if req.VideoInfo != nil {
			mmReq.VideoInfo = &moment.MomentVideo{
				Cover:    req.VideoInfo.Cover,
				VideoUrl: req.VideoInfo.VideoUrl,
				Duration: req.VideoInfo.Duration,
			}
			if !strings.HasPrefix(mmReq.VideoInfo.VideoUrl, "http") && len(mmReq.VideoInfo.VideoUrl) > 0 {
				videoInfo.VideoUrl = m.sto.ExternalURL(mmReq.VideoInfo.VideoUrl)
			}
			if !strings.HasPrefix(mmReq.VideoInfo.Cover, "http") && len(mmReq.VideoInfo.Cover) > 0 {
				videoInfo.Cover = m.sto.ExternalURL(mmReq.VideoInfo.Cover, moment.PictureStyle)
			}
			videoInfo.Duration = req.VideoInfo.Duration
		}
	}

	mid, err := m.mm.MomentPublish(ctx, uac.UserId, mmReq)
	if err != nil {
		return nil, err
	}

	return &types.MomentPublishResponse{
		MId:           mid,
		MomentType:    req.MomentType,
		Author:        *mixer.User(ctx, userAccount),
		Title:         req.Title,
		Desc:          req.Desc,
		DescExtra:     req.DescExtra,
		CharacterInfo: mmReq.CharacterInfo,
		PictureInfo:   pictureInfo,
		VideoInfo:     videoInfo,
		OptionAllow: &moment.MomentOption{
			CanComment:     true,
			CanShowComment: true,
			CanShare:       true,
		},
		Statistics:    &moment.MomentStatisticsSimple{},
		Status:        &moment.MomentStatus{},
		IsTop:         false,
		VisibleStatus: moment.AllVisible,
		CreateTime:    time.Now().Unix(),
	}, nil
}

// @Tags 动态
// @Summary 动态修改
// @Description 动态修改，支持标题，描述修改
// @Produce json
// @Security HeaderAuth
// @Param param body types.MomentModifyRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.MomentPublishResponse}
// @Router /api/v1/moment/modify [post]
func (m *Moment) Modify(ctx *api.Context, req types.MomentModifyRequest) (*types.MomentPublishResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	userAccount, err := m.ug.Account(ctx, uac.UserId)
	if err != nil {
		return nil, err
	}

	mmReq := moment.MomentPublish{
		MomentType: req.MomentType,
		Title:      req.Title,
		Desc:       req.Desc,
		DescExtra:  req.DescExtra,
	}

	switch req.MomentType {
	case moment.TypeMomentCharacter:
		if req.CharacterInfo != nil {
			mmReq.CharacterInfo = &moment.MomentCharacter{Data: req.CharacterInfo.Data}
		}
	}

	mo, err := m.mm.MomentModify(ctx, req.MId, uac.UserId, mmReq)
	if err != nil {
		return nil, err
	}

	return &types.MomentPublishResponse{
		MId:           mo.MId,
		MomentType:    mo.MomentType,
		Author:        *mixer.User(ctx, userAccount),
		Title:         mo.Title,
		Desc:          mo.Desc,
		DescExtra:     mo.DescExtra,
		CharacterInfo: mo.CharacterInfo,
		PictureInfo:   mo.PictureInfo,
		VideoInfo:     mo.VideoInfo,
		OptionAllow: &moment.MomentOption{
			CanComment:     true,
			CanShowComment: true,
			CanShare:       true,
		},
		Statistics:    mo.Statistics,
		Status:        mo.Status,
		IsTop:         mo.IsTop,
		VisibleStatus: mo.VisibleStatus,
		CreateTime:    mo.CreateTime,
	}, nil
}

// @Tags 动态
// @Summary 动态删除
// @Description 动态删除
// @Produce json
// @Security HeaderAuth
// @Param param body types.MomentDeleteRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.MomentDeleteResponse}
// @Router /api/v1/moment/delete [post]
func (m *Moment) Delete(ctx *api.Context, req types.MomentDeleteRequest) (*types.MomentDeleteResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	if err := m.mm.MomentDelete(ctx, uac.UserId, req.MId); err != nil {
		return nil, err
	}

	return &types.MomentDeleteResponse{}, nil
}

// @Tags 动态
// @Summary 点赞
// @Description 点赞
// @Produce json
// @Security HeaderAuth
// @Param param body types.MomentLikeRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.MomentLikeResponse}
// @Router /api/v1/moment/like [post]
func (m *Moment) Like(ctx *api.Context, req types.MomentLikeRequest) (*types.MomentLikeResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	err = m.mm.MomentLike(ctx, uac.UserId, req.MId)
	if err != nil {
		return nil, err
	}

	return &types.MomentLikeResponse{}, nil
}

// @Tags 动态
// @Summary 取消点赞
// @Description 取消点赞
// @Produce json
// @Security HeaderAuth
// @Param param body types.MomentLikeRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.MomentLikeResponse}
// @Router /api/v1/moment/unlike [post]
func (m *Moment) Unlike(ctx *api.Context, req types.MomentLikeRequest) (*types.MomentLikeResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	m.mm.MomentUnlike(ctx, uac.UserId, req.MId)

	return &types.MomentLikeResponse{}, nil
}

// @Tags 动态
// @Summary 举报
// @Description 举报
// @Produce json
// @Security HeaderAuth
// @Param param body types.MomentReportRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.MomentReportResponse}
// @Router /api/v1/moment/report [post]
func (m *Moment) Report(ctx *api.Context, req types.MomentReportRequest) (*types.MomentReportResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	m.mm.Report(ctx, uac.UserId, req.MId, req.Type, req.Reason, req.Desc, req.Images)

	return &types.MomentReportResponse{}, nil
}

// @Tags 动态
// @Summary 可见设置
// @Description 可见设置
// @Produce json
// @Security HeaderAuth
// @Param param body types.MomentVisibleRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.MomentVisibleResponse}
// @Router /api/v1/moment/visible [post]
func (m *Moment) Visible(ctx *api.Context, req types.MomentVisibleRequest) (*types.MomentVisibleResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	if err := m.mm.MomentVisible(ctx, req.MId, uac.UserId, req.VisibleStatus); err != nil {
		return nil, err
	}

	return &types.MomentVisibleResponse{}, nil
}

// @Tags 动态
// @Summary 动态置顶
// @Description 动态置顶
// @Produce json
// @Security HeaderAuth
// @Param param body types.MomentTopRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.MomentTopResponse}
// @Router /api/v1/moment/top [post]
func (m *Moment) Top(ctx *api.Context, req types.MomentTopRequest) (*types.MomentTopResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	if err := m.mm.MomentTop(ctx, req.MId, uac.UserId); err != nil {
		return nil, err
	}

	return &types.MomentTopResponse{}, nil
}

// @Tags 动态
// @Summary 取消置顶
// @Description 取消置顶
// @Produce json
// @Security HeaderAuth
// @Param param body types.MomentTopRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.MomentTopResponse}
// @Router /api/v1/moment/untop [post]
func (m *Moment) Untop(ctx *api.Context, req types.MomentTopRequest) (*types.MomentTopResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	if err := m.mm.MomentUntop(ctx, req.MId, uac.UserId); err != nil {
		return nil, err
	}

	return &types.MomentTopResponse{}, nil
}

// @Tags 动态
// @Summary 评论/回复发布
// @Description 评论/回复发布
// @Produce json
// @Security HeaderAuth
// @Param param body types.MomentCommentPublishRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.MomentCommentPublishResponse}
// @Router /api/v1/moment/comment/publish [post]
func (m *Moment) CommentPublish(ctx *api.Context, req types.MomentCommentPublishRequest) (*types.MomentCommentPublishResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	if req.Text == "" {
		return nil, biz.NewError(biz.ErrCommentTextEmpty, "comment is empty")
	}

	// 检查敏感词
	sws := m.reviewm.SensitiveWords(req.Text)

	if len(sws) > 0 {
		return nil, biz.NewError(biz.ErrCommentSensitiveWords, "Contains prohibited words")
	}

	if len(req.TextExtra) > 10 {
		return nil, biz.NewError(biz.ErrInvalidParam, "invalid params")
	}

	res, err := m.mm.CommentPublish(ctx, req.MId, req.ParentId, req.TargetId, uac.UserId, req.Text, req.TextExtra, req.ImageList)
	if err != nil {
		return nil, err
	}

	var toUser *types.User
	if res.ToUser != nil {
		toUser = mixer.User(ctx, res.ToUser)
	}

	return &types.MomentCommentPublishResponse{MomentComment: types.MomentComment{
		MId:             res.MId,
		CId:             res.CId,
		FromUser:        mixer.User(ctx, uac),
		ToUser:          toUser,
		LikeCount:       0,
		ReplyCount:      0,
		LabelList:       res.LabelList,
		ToUserLabelList: res.ToUserLabelList,
		IsLike:          false,
		IpLabel:         "",
		Text:            res.Text,
		TextExtra:       res.TextExtra,
		ImageList:       res.ImageList,
		CreateTime:      res.CreateTime,
	}}, nil
}

// @Tags 动态
// @Summary 评论删除
// @Description 评论删除
// @Produce json
// @Security HeaderAuth
// @Param param body types.MomentCommentDeleteRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.MomentCommentDeleteResponse}
// @Router /api/v1/moment/comment/delete [post]
func (m *Moment) CommentDelete(ctx *api.Context, req types.MomentCommentDeleteRequest) (*types.MomentCommentDeleteResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	if err := m.mm.CommentDelete(ctx, uac.UserId, req.CId); err != nil {
		return nil, err
	}

	return &types.MomentCommentDeleteResponse{}, nil
}

// @Tags 动态
// @Summary 评论列表
// @Description 评论列表
// @Produce json
// @Security HeaderAuth
// @Param param query types.MomentCommentListRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.MomentCommentListResponse}
// @Router /api/v1/moment/comment/list [get]
func (m *Moment) CommentList(ctx *api.Context, req types.MomentCommentListRequest) (*types.MomentCommentListResponse, error) {
	var userId string
	uac, err := ctx.User()
	if err == nil {
		userId = uac.UserId
	}

	res, cursor, err := m.mm.CommentList(ctx, userId, req.MId, req.CId, req.Cursor, req.Count)
	if err != nil {
		return nil, err
	}

	comments := make([]types.MomentComment, 0, len(res))
	for _, v := range res {
		comment := types.MomentComment{
			MId:        v.MId,
			CId:        v.CId,
			FromUser:   mixer.User(ctx, v.FromUser),
			LikeCount:  v.LikeCount,
			ReplyCount: v.ReplyCount,
			LabelList:  v.LabelList,
			IsLike:     v.IsLike,
			IsFold:     v.IsFold,
			IsReport:   v.IsReport,
			IpLabel:    v.IpLabel,
			Text:       v.Text,
			TextExtra:  v.TextExtra,
			ImageList:  v.ImageList,
			CreateTime: v.CreateTime,
		}

		comments = append(comments, comment)
	}

	return &types.MomentCommentListResponse{
		Comments: comments,
		Cursor:   cursor,
	}, nil
}

// @Tags 动态
// @Summary 回复列表
// @Description 回复列表
// @Produce json
// @Security HeaderAuth
// @Param param query types.MomentCommentReplyRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.MomentCommentReplyResponse}
// @Router /api/v1/moment/comment/reply [get]
func (m *Moment) CommentReply(ctx *api.Context, req types.MomentCommentReplyRequest) (*types.MomentCommentReplyResponse, error) {
	var userId string
	uac, err := ctx.User()
	if err == nil {
		userId = uac.UserId
	}

	res, cursor, err := m.mm.CommentReplyList(ctx, userId, req.MId, req.ParentId, req.WithoutCid, req.Cursor, req.Count)
	if err != nil {
		return nil, err
	}

	comments := make([]types.MomentComment, 0, len(res))
	for _, v := range res {
		var toUser *types.User
		if v.ToUser != nil {
			toUser = mixer.User(ctx, v.ToUser)
		}
		comment := types.MomentComment{
			MId:             v.MId,
			CId:             v.CId,
			FromUser:        mixer.User(ctx, v.FromUser),
			ToUser:          toUser,
			LikeCount:       v.LikeCount,
			ReplyCount:      v.ReplyCount,
			LabelList:       v.LabelList,
			ToUserLabelList: v.ToUserLabelList,
			IsLike:          v.IsLike,
			IsFold:          v.IsFold,
			IsReport:        v.IsReport,
			IpLabel:         v.IpLabel,
			Text:            v.Text,
			TextExtra:       v.TextExtra,
			ImageList:       v.ImageList,
			CreateTime:      v.CreateTime,
		}

		comments = append(comments, comment)
	}

	return &types.MomentCommentReplyResponse{
		Comments: comments,
		Cursor:   cursor,
	}, nil
}

// @Tags 动态
// @Summary 评论点赞
// @Description 评论点赞
// @Produce json
// @Security HeaderAuth
// @Param param body types.MomentCommentLikeRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.MomentCommentLikeResponse}
// @Router /api/v1/moment/comment/like [post]
func (m *Moment) CommentLike(ctx *api.Context, req types.MomentCommentLikeRequest) (*types.MomentCommentLikeResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	m.mm.CommentUnfold(ctx, uac.UserId, req.CId)
	m.mm.CommentLike(ctx, uac.UserId, req.CId)

	return &types.MomentCommentLikeResponse{}, nil
}

// @Tags 动态
// @Summary 评论取消点赞
// @Description 评论取消点赞
// @Produce json
// @Security HeaderAuth
// @Param param body types.MomentCommentUnlikeRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.MomentCommentUnlikeResponse}
// @Router /api/v1/moment/comment/unlike [post]
func (m *Moment) CommentUnlike(ctx *api.Context, req types.MomentCommentUnlikeRequest) (*types.MomentCommentUnlikeResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	m.mm.CommentUnlike(ctx, uac.UserId, req.CId)

	return &types.MomentCommentUnlikeResponse{}, nil
}

// @Tags 动态
// @Summary 评论折叠
// @Description 评论折叠
// @Produce json
// @Security HeaderAuth
// @Param param body types.MomentCommentLikeRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.MomentCommentLikeResponse}
// @Router /api/v1/moment/comment/fold [post]
func (m *Moment) CommentFold(ctx *api.Context, req types.MomentCommentLikeRequest) (*types.MomentCommentLikeResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	m.mm.CommentUnlike(ctx, uac.UserId, req.CId)
	m.mm.CommentFold(ctx, uac.UserId, req.CId)

	return &types.MomentCommentLikeResponse{}, nil
}

// @Tags 动态
// @Summary 评论取消折叠
// @Description 评论取消折叠
// @Produce json
// @Security HeaderAuth
// @Param param body types.MomentCommentUnlikeRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.MomentCommentUnlikeResponse}
// @Router /api/v1/moment/comment/unfold [post]
func (m *Moment) CommentUnFold(ctx *api.Context, req types.MomentCommentUnlikeRequest) (*types.MomentCommentUnlikeResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	m.mm.CommentUnfold(ctx, uac.UserId, req.CId)

	return &types.MomentCommentUnlikeResponse{}, nil
}

// @Tags 动态
// @Summary "我的"页面动态列表
// @Description "我的"页面动态列表
// @Produce json
// @Security HeaderAuth
// @Param param query types.MomentUserOwnRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.MomentFeedResponse}
// @Router /api/v1/moment/user/own [get]
func (m *Moment) UserOwn(ctx *api.Context, req types.MomentUserOwnRequest) (*types.MomentFeedResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	if len(req.UserId) == 0 {
		return nil, biz.NewError(biz.ErrInvalidParam, "userId is empty")
	}

	req.Cursor = lo.Ternary(req.Cursor <= 0, 0, req.Cursor)
	req.Count = lo.Ternary(req.Count <= 0, 10, req.Count)

	moments, err := m.mm.MyMoments(ctx, uac.UserId, req.UserId, req.Cursor, req.Count)
	if err != nil {
		return nil, err
	}

	list := make([]types.MomentFeedItem, 0, len(moments))
	for _, v := range moments {
		var followed bool
		if v.Author != nil {
			followed, err = m.fg.Following(ctx, uac.UserId, v.Author.UserId)
			if err != nil {
				return nil, err
			}
		}

		item := types.MomentFeedItem{
			MId:           v.MId,
			MomentType:    v.MomentType,
			Author:        mixer.User(ctx, v.Author),
			Followed:      followed,
			Title:         v.Title,
			Desc:          v.Desc,
			DescExtra:     v.DescExtra,
			CharacterInfo: v.CharacterInfo,
			PictureInfo:   v.PictureInfo,
			VideoInfo:     v.VideoInfo,
			OptionAllow: &moment.MomentOption{
				CanComment:     true,
				CanShowComment: true,
				CanShare:       true,
			},
			Statistics:    v.Statistics,
			Status:        v.Status,
			IsTop:         v.IsTop,
			VisibleStatus: v.VisibleStatus,
			CreateTime:    v.CreateTime,
		}

		list = append(list, item)
	}

	return &types.MomentFeedResponse{
		List:   list,
		Cursor: req.Cursor + req.Count,
	}, nil
}

// @Tags 动态
// @Summary "我的"页面喜欢列表
// @Description "我的"页面喜欢列表
// @Produce json
// @Security HeaderAuth
// @Param param query types.MomentUserOwnRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.MomentFeedResponse}
// @Router /api/v1/moment/user/like [get]
func (m *Moment) UserLike(ctx *api.Context, req types.MomentUserOwnRequest) (*types.MomentFeedResponse, error) {
	if len(req.UserId) == 0 {
		return nil, biz.NewError(biz.ErrInvalidParam, "userId is empty")
	}

	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	req.Cursor = lo.Ternary(req.Cursor <= 0, 0, req.Cursor)
	req.Count = lo.Ternary(req.Count <= 0, 10, req.Count)

	moments, err := m.mm.MyLikeMoments(ctx, uac.UserId, req.UserId, req.Cursor, req.Count)
	if err != nil {
		return nil, err
	}

	list := make([]types.MomentFeedItem, 0, len(moments))
	for _, v := range moments {
		var followed bool
		if v.Author != nil {
			followed, err = m.fg.Following(ctx, uac.UserId, v.Author.UserId)
			if err != nil {
				return nil, err
			}
		}

		item := types.MomentFeedItem{
			MId:           v.MId,
			MomentType:    v.MomentType,
			Author:        mixer.User(ctx, v.Author),
			Followed:      followed,
			Title:         v.Title,
			Desc:          v.Desc,
			DescExtra:     v.DescExtra,
			CharacterInfo: v.CharacterInfo,
			PictureInfo:   v.PictureInfo,
			VideoInfo:     v.VideoInfo,
			OptionAllow: &moment.MomentOption{
				CanComment:     true,
				CanShowComment: true,
				CanShare:       true,
			},
			Statistics:    v.Statistics,
			Status:        v.Status,
			IsTop:         v.IsTop,
			VisibleStatus: v.VisibleStatus,
			CreateTime:    v.CreateTime,
		}

		list = append(list, item)
	}

	return &types.MomentFeedResponse{
		List:   list,
		Cursor: req.Cursor + req.Count,
	}, nil
}

// @Tags 动态
// @Summary 动态分享成功
// @Description 动态分享成功
// @Produce json
// @Security HeaderAuth
// @Param param body types.MomentSuccessRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.MomentSuccessResponse}
// @Router /api/v1/moment/share/success [post]
func (m *Moment) ShareSuccess(ctx *api.Context, req types.MomentSuccessRequest) (*types.MomentSuccessResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	m.mm.MomentShareSuccess(ctx, uac.UserId, req.MId)

	return &types.MomentSuccessResponse{}, nil
}

// @Tags 动态
// @Summary 动态点赞列表
// @Description 动态点赞列表
// @Produce json
// @Security HeaderAuth
// @Param param query types.MomentLikeListRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.MomentLikeListResponse}
// @Router /api/v1/moment/like/list [get]
func (m *Moment) LikeList(ctx *api.Context, req types.MomentLikeListRequest) (*types.MomentLikeListResponse, error) {
	var userId string
	uac, err := ctx.User()
	if err == nil {
		userId = uac.UserId
	}

	req.Cursor = lo.Ternary(req.Cursor <= 0, 0, req.Cursor)
	req.Count = lo.Ternary(req.Count <= 0, 10, req.Count)

	res, cursor, err := m.mm.MomentLikeList(ctx, req.MId, req.Cursor, req.Count)
	if err != nil {
		return nil, err
	}

	users := make([]types.UserWithRelation, 0, len(res))
	for _, v := range res {
		uac, err := m.ug.Account(ctx, v.UserId)
		if err != nil {
			continue
		}

		userWithRelation := types.UserWithRelation{
			User: *mixer.User(ctx, uac),
		}

		if userId != "" {
			userWithRelation.FollowState = *mixer.FollowState(ctx, userId, uac.UserId)
		}

		users = append(users, userWithRelation)
	}

	return &types.MomentLikeListResponse{
		Users:  users,
		Cursor: cursor,
	}, nil

}
