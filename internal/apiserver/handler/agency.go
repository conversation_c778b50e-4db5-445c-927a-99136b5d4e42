package handler

import (
	"encoding/json"
	"github.com/samber/lo"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/mux"
	"strconv"
	"time"

	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/overseas/live/osl/internal/agency"
	"gitlab.sskjz.com/overseas/live/osl/internal/anchor"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/mixer"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/upload"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/avatar"
	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/sto"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"go.uber.org/zap"
)

func InvokeAgencyHandler(
	am *agency.Manager,
	r *api.Router,
	ug user.Getter,
	si sto.Instance,
	as *avatar.Show,
	us *upload.Scenes,
	anm *anchor.Manager,
	vnd log.Vendor,
	ml *mux.Locker,
) *Agency {
	sc := si.Conf("agency")

	h := NewAgency(am, ug, sc, anm, vnd.Scope("api.agency"))

	ar := r.WithAuth(ml.Middleware(mux.WithPOST))
	{
		// 创建公会信息
		ar.POST("/agency/create", api.Generic(h.AgencyCreate))
		// 获取邀请码信息
		ar.GET("/agency/invite/info", api.Generic(h.InviteCodeInfo))
		// 我的公会
		ar.GET("/agency/my", api.Generic(h.MyAgency))
		// 获取公会信息
		ar.GET("/agency/get", api.Generic(h.AgencyGet))
		// 申请入会
		ar.POST("/agency/join/apply", api.Generic(h.AgencyJoinApply))
		// 取消申请入会
		ar.POST("/agency/join/cancel", api.Generic(h.AgencyJoinCancel))
		// 申请退出公会
		ar.POST("/agency/quit/apply", api.Generic(h.AgencyQuitApply))
		// 取消退出公会
		ar.POST("/agency/quit/cancel", api.Generic(h.AgencyQuitCancel))
		// 待审核成员列表
		ar.GET("/agency/verify/list", api.Generic(h.AgencyVerifyList))
		// 入会审核接口
		ar.POST("/agency/verify/join", api.Generic(h.AgencyVerifyJoin))
		// 退出审核接口
		ar.POST("/agency/verify/quit", api.Generic(h.AgencyVerifyQuit))
		// 公会成员列表
		ar.GET("/agency/member/list", api.Generic(h.AgencyMemberList))
		// 踢出公会
		ar.POST("/agency/member/kickout", api.Generic(h.AgencyMemberKickout))
		// 强制退会申请
		ar.POST("/agency/force/quit/apply", api.Generic(h.AgencyForceQuitApply))
		// 强制退会取消
		ar.POST("/agency/force/quit/cancel", api.Generic(h.AgencyForceQuitCancel))
		// 强制退会确认
		ar.POST("/agency/force/quit/confirm", api.Generic(h.AgencyForceQuitConfirm))
	}

	{
		// todo 图片尺寸
		us.Add("agency", us.Classic("agency", as.Style().Thumb))
	}

	return h
}

type Agency struct {
	am     *agency.Manager
	ug     user.Getter
	sc     sto.Conf
	anm    *anchor.Manager
	logger *zap.Logger
}

func NewAgency(
	am *agency.Manager,
	ug user.Getter,
	sc sto.Conf,
	anm *anchor.Manager,
	logger *zap.Logger,
) *Agency {
	return &Agency{
		am:     am,
		ug:     ug,
		sc:     sc,
		anm:    anm,
		logger: logger,
	}
}

// @Tags 公会
// @Summary 创建公会
// @Description 用于创建公会，出参与"已创建公会信息"接口相同，入参不同
// @Produce json
// @Security HeaderAuth
// @Param param body types.AgencyCreateRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.AgencyCreateResponse}
// @Router /api/v1/agency/create [post]
func (h *Agency) AgencyCreate(ctx *api.Context, req types.AgencyCreateRequest) (*types.AgencyCreateResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	// 校验邀请码
	if req.InviteCode != "" {
		numId, _ := strconv.Atoi(req.InviteCode)
		if _, err := h.am.GetAgencyInfoByNumId(ctx, int64(numId)); err != nil {
			return nil, biz.NewError(biz.ErrInviteCode, "The Invitation Code is Invalid. Please Re-enter It.")
		}
	}

	if len(req.Material) > 5 {
		return nil, biz.NewError(biz.ErrInvalidParam, "invalid param")
	}

	err = h.am.AgencyCreate(ctx, uac.UserId, agency.AgencyCreateApply{
		Name:       req.Name,
		ImageUrl:   req.ImageUrl,
		WhatsappId: req.WhatsappId,
		MemberNum:  req.MemberNum,
		Platform:   req.Platform,
		Material:   req.Material,
		Phone:      req.Phone,
		InviteCode: req.InviteCode,
		Code:       req.Code,
	})
	if err != nil {
		return nil, err
	}

	return &types.AgencyCreateResponse{}, nil
}

// @Tags 公会
// @Summary 邀请码信息
// @Description 获取邀请码公会信息
// @Produce json
// @Security HeaderAuth
// @Param param query types.InviteCodeInfoRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.InviteCodeInfoResponse}
// @Router /api/v1/agency/invite/info [get]
func (h *Agency) InviteCodeInfo(ctx *api.Context, req types.InviteCodeInfoRequest) (*types.InviteCodeInfoResponse, error) {
	// 校验邀请码
	numId, _ := strconv.Atoi(req.InviteCode)
	info, err := h.am.GetAgencyInfoByNumId(ctx, int64(numId))
	if err != nil {
		return nil, biz.NewError(biz.ErrInviteCode, "The Invitation Code is Invalid. Please Re-enter It.")
	}

	chiefAcc, err := h.ug.Account(ctx, info.ChiefId)
	if err != nil {
		return nil, err
	}

	return &types.InviteCodeInfoResponse{
		Agency: types.Agency{
			NumId:     info.NumId,
			Name:      info.Name,
			ImageUrl:  h.sc.ExternalURL(info.ImageUrl),
			ShowId:    info.ShowId,
			ChiefId:   info.ChiefId,
			ChiefInfo: mixer.User(ctx, chiefAcc),
			CreatedAt: info.CreatedAt,
			UpdatedAt: info.UpdatedAt,
		},
	}, nil
}

// @Tags 公会
// @Summary 公会信息
// @Description 使用公会id获取公会信息
// @Produce json
// @Security HeaderAuth
// @Param param query types.AgencyGetRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.AgencyGetResponse}
// @Router /api/v1/agency/get [get]
func (h *Agency) AgencyGet(ctx *api.Context, req types.AgencyGetRequest) (*types.AgencyGetResponse, error) {
	info, err := h.am.GetAgencyInfoByShowId(ctx, req.ShowId)
	if err != nil {
		return nil, err
	}

	acc, err := h.ug.Account(ctx, info.ChiefId)
	if err != nil {
		return nil, err
	}

	return &types.AgencyGetResponse{
		Agency: types.Agency{
			NumId:     info.NumId,
			Name:      info.Name,
			ImageUrl:  h.sc.ExternalURL(info.ImageUrl),
			ShowId:    info.ShowId,
			ChiefId:   info.ChiefId,
			ChiefInfo: mixer.User(ctx, acc),
			CreatedAt: info.CreatedAt,
			UpdatedAt: info.UpdatedAt,
		},
	}, nil
}

// @Tags 公会
// @Summary 申请加入公会
// @Description 用于主播申请入会
// @Produce json
// @Security HeaderAuth
// @Param param body types.AgencyJoinApplyRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.AgencyJoinApplyResponse}
// @Router /api/v1/agency/join/apply [post]
func (h *Agency) AgencyJoinApply(ctx *api.Context, req types.AgencyJoinApplyRequest) (*types.AgencyJoinApplyResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	if err := h.am.AgencyJoinApply(ctx, req.NumId, uac.UserId); err != nil {
		return nil, err
	}

	return &types.AgencyJoinApplyResponse{}, nil
}

// @Tags 公会
// @Summary 取消入会申请
// @Description 取消入会申请
// @Produce json
// @Security HeaderAuth
// @Param param body types.AgencyJoinCancelRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.AgencyJoinCancelResponse}
// @Router /api/v1/agency/join/cancel [post]
func (h *Agency) AgencyJoinCancel(ctx *api.Context, req types.AgencyJoinCancelRequest) (*types.AgencyJoinCancelResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	if err := h.am.AgencyJoinCancel(ctx, uac.UserId); err != nil {
		return nil, err
	}

	return &types.AgencyJoinCancelResponse{}, nil
}

// @Tags 公会
// @Summary 申请退出公会
// @Description 用于主播退出入会
// @Produce json
// @Security HeaderAuth
// @Param param body types.AgencyQuitApplyRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.AgencyQuitApplyResponse}
// @Router /api/v1/agency/quit/apply [post]
func (h *Agency) AgencyQuitApply(ctx *api.Context, req types.AgencyQuitApplyRequest) (*types.AgencyQuitApplyResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	if err := h.am.AgencyQuitApply(ctx, req.NumId, uac.UserId); err != nil {
		return nil, err
	}

	return &types.AgencyQuitApplyResponse{}, nil
}

// @Tags 公会
// @Summary 取消退出申请
// @Description 取消退出申请
// @Produce json
// @Security HeaderAuth
// @Param param body types.AgencyQuitCancelRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.AgencyQuitCancelResponse}
// @Router /api/v1/agency/quit/cancel [post]
func (h *Agency) AgencyQuitCancel(ctx *api.Context, req types.AgencyQuitCancelRequest) (*types.AgencyQuitCancelResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	if err := h.am.AgencyQuitCancel(ctx, uac.UserId); err != nil {
		return nil, err
	}

	return &types.AgencyQuitCancelResponse{}, nil
}

// @Tags 公会
// @Summary 我的公会
// @Description 我的公会，进入我的公会页面，请求此接口查看当前用户加入公会状态
// @Produce json
// @Security HeaderAuth
// @Param param query types.AgencyMyRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.AgencyMyResponse}
// @Router /api/v1/agency/my [get]
func (h *Agency) MyAgency(ctx *api.Context, req types.AgencyMyRequest) (*types.AgencyMyResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	var response types.AgencyMyResponse

	// 审核中公会
	aca, err := h.am.GetMyAgencyApply(ctx, uac.UserId)
	if err != nil {
		return nil, err
	}
	if aca.ID > 0 && aca.Status == int(agency.VerifyStatusDefault) {
		chiefAccount, err := h.ug.Account(ctx, aca.ChiefId)
		if err != nil {
			return nil, err
		}

		var (
			material    []string
			materialUrl []string
		)
		json.Unmarshal([]byte(aca.Material), &material)
		for _, v := range material {
			materialUrl = append(materialUrl, h.sc.ExternalURL(v))
		}

		response = types.AgencyMyResponse{
			Agency: &types.Agency{
				Name:       aca.Name,
				ImageUrl:   h.sc.ExternalURL(aca.ImageUrl),
				WhatsappId: aca.WhatsappId,
				MemberNum:  aca.MemberNum,
				Platform:   aca.Platform,
				Material:   materialUrl,
				Phone:      aca.Phone,
				InviteCode: aca.InviteCode,
				ChiefId:    aca.ChiefId,
				ChiefInfo:  mixer.User(ctx, chiefAccount),
				CreatedAt:  aca.CreatedAt,
				UpdatedAt:  aca.UpdatedAt,
			},
			Status: 4, // 以申请创建公会
		}
	} else {
		res, err := h.am.GetMyAgency(ctx, uac.UserId)
		if err != nil {
			return nil, err
		}

		if res.Status > agency.MyAgencyStatusNoJoin && res.Agency != nil {
			chiefAccount, err := h.ug.Account(ctx, res.Agency.ChiefId)
			if err != nil {
				return nil, err
			}

			response = types.AgencyMyResponse{
				Agency: &types.Agency{
					NumId:     res.Agency.NumId,
					Name:      res.Agency.Name,
					MemberNum: res.Agency.MemberNum,
					ImageUrl:  h.sc.ExternalURL(res.Agency.ImageUrl),
					ShowId:    res.Agency.ShowId,
					Ratio:     res.Agency.Ratio,
					ChiefId:   res.Agency.ChiefId,
					ChiefInfo: mixer.User(ctx, chiefAccount),
					UpdatedAt: res.Agency.UpdatedAt,
					CreatedAt: res.Agency.CreatedAt,
				},
			}
		}
		response.Status = res.Status
	}

	// 在公会中，查询是否有强制退会信息
	if lo.Contains([]int{2, 3}, response.Status) {
		fqInfo, err := h.am.ForceQuitInfo(ctx, uac.UserId)
		if err == nil {
			leftTime := int64(fqInfo.LastAlive.AddDate(0, 0, 30).Sub(time.Now()).Seconds())
			response.Status = 5 // 强制退会申请中
			response.LastAliveTime = fqInfo.LastAlive.Unix()
			response.LeftTime = lo.Ternary(leftTime < 0, 0, leftTime)
		}
	}

	return &response, nil
}

// @Tags 公会
// @Summary 公会成员待审核列表
// @Description 公会成员待审核列表，包含入会申请，和退出申请两个列表
// @Produce json
// @Security HeaderAuth
// @Param param query types.AgencyVerifyListRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.AgencyVerifyListResponse}
// @Router /api/v1/agency/verify/list [get]
func (h *Agency) AgencyVerifyList(ctx *api.Context, req types.AgencyVerifyListRequest) (*types.AgencyVerifyListResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	joinList, quitList, err := h.am.AgencyMemberVerifyList(ctx, uac.UserId, req.NumId)
	if err != nil {
		return nil, err
	}

	var (
		joinUserList []*types.UserWithExt
		quitUserList []*types.UserWithExt
	)
	if len(joinList) > 0 {
		for _, v := range joinList {
			acc, err := h.ug.Account(ctx, v.UserId)
			if err != nil {
				return nil, err
			}

			joinUserList = append(joinUserList, mixer.UserWithExt(ctx, acc))
		}
	}

	if len(quitList) > 0 {
		for _, v := range quitList {
			acc, err := h.ug.Account(ctx, v.UserId)
			if err != nil {
				return nil, err
			}

			quitUserList = append(quitUserList, mixer.UserWithExt(ctx, acc))
		}
	}

	return &types.AgencyVerifyListResponse{
		JoinList: joinUserList,
		QuitList: quitUserList,
	}, nil
}

// @Tags 公会
// @Summary 入会审核
// @Description 加入公会审核，由会长操作
// @Produce json
// @Security HeaderAuth
// @Param param query types.AgencyMemberVerifyRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.AgencyMemberVerifyResponse}
// @Router /api/v1/agency/verify/join [post]
func (h *Agency) AgencyVerifyJoin(ctx *api.Context, req types.AgencyMemberVerifyRequest) (*types.AgencyMemberVerifyResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	if err := h.am.MemberJoinVerify(ctx, req.NumId, uac.UserId, req.UserId, req.VerifyResult); err != nil {
		return nil, err
	}

	return &types.AgencyMemberVerifyResponse{}, nil
}

// @Tags 公会
// @Summary 退出审核
// @Description 退出公会审核，有会长操作
// @Produce json
// @Security HeaderAuth
// @Param param query types.AgencyMemberVerifyRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.AgencyMemberVerifyResponse}
// @Router /api/v1/agency/verify/quit [post]
func (h *Agency) AgencyVerifyQuit(ctx *api.Context, req types.AgencyMemberVerifyRequest) (*types.AgencyMemberVerifyResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	if err := h.am.MemberQuitVerify(ctx, req.NumId, uac.UserId, req.UserId, req.VerifyResult); err != nil {
		return nil, err
	}

	return &types.AgencyMemberVerifyResponse{}, nil
}

// @Tags 公会
// @Summary 公会成员列表
// @Description 公会成员列表
// @Produce json
// @Security HeaderAuth
// @Param param query types.AgencyMemberListRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.AgencyMemberListResponse}
// @Router /api/v1/agency/member/list [get]
func (h *Agency) AgencyMemberList(ctx *api.Context, req types.AgencyMemberListRequest) (*types.AgencyMemberListResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	// 时间排序
	list, total, err := h.am.AgencyMemberListByTime(ctx, uac.UserId, req.NumId, req.Cursor, req.Count)
	if err != nil {
		return nil, err
	}

	// todo 收入排序

	var res []*types.AgencyMember = make([]*types.AgencyMember, 0)
	for _, v := range list {
		acc, err := h.ug.Account(ctx, v.UserId)
		if err != nil {
			return nil, err
		}

		anchorFlags, _ := h.anm.GetAnchorFlags(acc.UserId)

		res = append(res, &types.AgencyMember{
			UserWithExt: mixer.UserWithExt(ctx, acc),
			IsNewAnchor: anchorFlags.IsNewAnchorFlag(),
			JoinTime:    v.UpdatedAt.Unix(),
		})
	}

	return &types.AgencyMemberListResponse{
		List:   res,
		Cursor: req.Cursor + req.Count,
		Total:  int(total),
	}, nil
}

// @Tags 公会
// @Summary 踢出公会
// @Description 踢出公会，由会长操作
// @Produce json
// @Security HeaderAuth
// @Param param query types.AgencyMemberKickoutRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.AgencyMemberKickoutResponse}
// @Router /api/v1/agency/member/kickout [post]
func (h *Agency) AgencyMemberKickout(ctx *api.Context, req types.AgencyMemberKickoutRequest) (*types.AgencyMemberKickoutResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	if err := h.am.MemberKickout(ctx, req.NumId, uac.UserId, req.UserId); err != nil {
		return nil, err
	}

	return &types.AgencyMemberKickoutResponse{}, nil
}

// @Tags 公会
// @Summary 申请退出公会
// @Description 用于主播退出入会
// @Produce json
// @Security HeaderAuth
// @Param param body types.AgencyForceQuitApplyRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.AgencyForceQuitApplyResponse}
// @Router /api/v1/agency/force/quit/apply [post]
func (h *Agency) AgencyForceQuitApply(ctx *api.Context, req types.AgencyForceQuitApplyRequest) (*types.AgencyForceQuitApplyResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	if err := h.am.AgencyForceQuitApply(ctx, uac.UserId, true); err != nil {
		return nil, err
	}

	return &types.AgencyForceQuitApplyResponse{}, nil
}

// @Tags 公会
// @Summary 取消退出申请
// @Description 取消退出申请
// @Produce json
// @Security HeaderAuth
// @Param param body types.AgencyForceCancelRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.AgencyForceCancelResponse}
// @Router /api/v1/agency/force/quit/cancel [post]
func (h *Agency) AgencyForceQuitCancel(ctx *api.Context, req types.AgencyForceCancelRequest) (*types.AgencyForceCancelResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	if err := h.am.AgencyForceQuitCancel(ctx, uac.UserId); err != nil {
		return nil, err
	}

	return &types.AgencyForceCancelResponse{}, nil
}

// @Tags 公会
// @Summary 强制退会确认
// @Description 强制退会确认，停播30天后点击确认按钮直接退会
// @Produce json
// @Security HeaderAuth
// @Param param body types.AgencyForceConfirmRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.AgencyForceConfirmResponse}
// @Router /api/v1/agency/force/quit/confirm [post]
func (h *Agency) AgencyForceQuitConfirm(ctx *api.Context, req types.AgencyForceConfirmRequest) (*types.AgencyForceConfirmResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	if err := h.am.AgencyForceQuitConfirm(ctx, uac.UserId); err != nil {
		return nil, err
	}

	return &types.AgencyForceConfirmResponse{}, nil
}
