package handler

import (
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/bytedance/sonic"
	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/mixer"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"gitlab.sskjz.com/overseas/live/osl/internal/follow"
	"gitlab.sskjz.com/overseas/live/osl/internal/im"
	"gitlab.sskjz.com/overseas/live/osl/internal/im/ban"
	"gitlab.sskjz.com/overseas/live/osl/internal/im/limiter"
	"gitlab.sskjz.com/overseas/live/osl/internal/manage/patrol"
	"gitlab.sskjz.com/overseas/live/osl/internal/push/pre"
	"gitlab.sskjz.com/overseas/live/osl/internal/review"
	"gitlab.sskjz.com/overseas/live/osl/internal/ufind"
	"gitlab.sskjz.com/overseas/live/osl/pkg/dbg"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"go.uber.org/zap"
)

// 消息
func InvokeImHandler(
	r *api.Router,
	imm *im.Manager,
	ibm *ban.Manager,
	ilm *limiter.Manager,
	fg follow.Getter,
	ug user.Getter,
	us *ufind.Indexer,
	reviewm *review.Manager,
	dm *redi.Mutex,
	rc *redi.Client,
	pg patrol.Getter,
	pd *pre.Dispatcher,
	vnd log.Vendor,
) *Im {
	logger := vnd.Scope("api.im")
	h := NewIm(imm, ibm, ilm, fg, ug, us, reviewm, dm, rc, pg, pd, logger)

	ar := r.WithAuth()
	{
		ar.GET("/im/contact/list", api.Generic(h.ContactList))
		ar.GET("/im/contact/search", api.Generic(h.ContactSearch))
		ar.GET("/im/token", api.Generic(h.Token))
		ar.GET("/im/message/send", api.Generic(h.SendMessage))
		ar.GET("/im/callback/result", api.Generic(h.CallbackResult))
		ar.GET("/im/user/info", api.Generic(h.UserInfo))
		ar.POST("/im/cs/refresh", api.Generic(h.RefreshCs))
	}

	cr := r.With(im.CallbackSignatureVerify(imm.GetCallbackSecretKey(), logger))
	if dbg.Ing() {
		if envIsTest() {
			initSSEServer(r)
			cr = cr.With(forwardImCallback)
		}
		if envIsLocal() {
			initSSEClient()
		}
	}
	{
		// IM回调通知
		cr.POST("/im/callback", api.Custom(h.Callback))
	}

	return h
}

type Im struct {
	imm     *im.Manager
	ibm     *ban.Manager
	ilm     *limiter.Manager
	fg      follow.Getter
	ug      user.Getter
	us      *ufind.Indexer
	reviewm *review.Manager
	dm      *redi.Mutex
	rc      *redi.Client
	pg      patrol.Getter
	pd      *pre.Dispatcher
	logger  *zap.Logger
}

func NewIm(imm *im.Manager, ibm *ban.Manager, ilm *limiter.Manager, fg follow.Getter, ug user.Getter, us *ufind.Indexer, reviewm *review.Manager, dm *redi.Mutex, rc *redi.Client, pg patrol.Getter, pd *pre.Dispatcher, logger *zap.Logger) *Im {
	return &Im{
		imm:     imm,
		ibm:     ibm,
		ilm:     ilm,
		fg:      fg,
		ug:      ug,
		us:      us,
		reviewm: reviewm,
		dm:      dm,
		rc:      rc,
		pg:      pg,
		pd:      pd,
		logger:  logger,
	}
}

// @Tags 消息
// @Summary ✅根据numId查询IM用户信息
// @Description 根据numId查询IM用户信息
// @Produce json
// @Security HeaderAuth
// @Param param query types.ImUserInfoRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.ImUserInfoResponse}
// @Router /api/v1/im/user/info [get]
func (h *Im) UserInfo(ctx *api.Context, req types.ImUserInfoRequest) (*types.ImUserInfoResponse, error) {
	acc, err := h.imm.Account(ctx, req.NumId)

	if err != nil {
		return nil, err
	}

	ext := make(map[string]string)

	if lo.Contains(h.imm.GetSystemNoticeNumIds(), acc.NumId) {
		ext = h.imm.GetSystemNoticeAccountExt()
	}

	return &types.ImUserInfoResponse{
		User: *mixer.UserWithExt(ctx, acc),
		Ext:  ext,
	}, nil
}

// @Tags 消息
// @Summary ✅联系人列表
// @Description 联系人列表，互相关注的用户列表
// @Produce json
// @Security HeaderAuth
// @Success 200 {object} codec.Response{data=types.ImContactListResponse}
// @Router /api/v1/im/contact/list [get]
func (h *Im) ContactList(ctx *api.Context, _ api.EmptyReq) (*types.ImContactListResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	list := make([]types.ImContact, 0)

	friendIds, err := h.fg.FriendIds(ctx, uac.UserId)

	if err != nil {
		return nil, err
	}

	for _, userId := range friendIds {
		u, err := h.ug.Account(ctx, userId)

		if err != nil {
			continue
		}

		list = append(list, types.ImContact{
			UserWithExt: *mixer.UserWithExt(ctx, u),
		})
	}

	return &types.ImContactListResponse{
		List: list,
	}, nil
}

// @Tags 消息
// @Summary ✅搜索联系人
// @Description 搜索联系人
// @Produce json
// @Security HeaderAuth
// @Param param query types.ImContactSearchRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.ImContactSearchResponse}
// @Router /api/v1/im/contact/search [get]
func (h *Im) ContactSearch(ctx *api.Context, req types.ImContactSearchRequest) (*types.ImContactSearchResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	userIds, err := h.us.Search(ctx, uac.UserId, req.Keyword, 10)
	if err != nil {
		return nil, err
	}

	list := make([]types.ImContact, 0)
	for _, userId := range userIds {
		if acc, err := h.ug.Account(ctx, userId); err == nil {
			list = append(list, types.ImContact{
				UserWithExt: *mixer.UserWithExt(ctx, acc),
			})
		}
	}

	return &types.ImContactSearchResponse{
		List: list,
	}, nil
}

// @Tags 消息
// @Summary ✅获取火山引擎IM token
// @Description 获取火山引擎IM token
// @Produce json
// @Security HeaderAuth
// @Success 200 {object} codec.Response{data=types.ImTokenResponse}
// @Router /api/v1/im/token [get]
func (h *Im) Token(ctx *api.Context, _ api.EmptyReq) (*types.ImTokenResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	token, err := h.imm.Token(uac.NumId)

	if err != nil {
		return nil, err
	}

	return &types.ImTokenResponse{
		Token:        token,
		NoticeNumIds: h.imm.GetSystemNoticeNumIds(),
	}, nil
}

// @Tags 消息
// @Summary 消息回调结果以及拒绝原因
// @Description 消息回调结果以及拒绝原因
// @Produce json
// @Security HeaderAuth
// @Param param query types.ImCallbackResultRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.ImCallbackResultResponse}
// @Router /api/v1/im/callback/result [get]
func (h *Im) CallbackResult(ctx *api.Context, req types.ImCallbackResultRequest) (*types.ImCallbackResultResponse, error) {
	shortConversationId := req.ShortConversationId
	clientMessageId := req.ClientMessageId

	result, err := h.ilm.Result(ctx, shortConversationId, clientMessageId)

	if err != nil {
		return nil, err
	}

	return &types.ImCallbackResultResponse{
		Deny:   result.Deny,
		Reason: result.Reason,
	}, nil
}

// @Tags 消息
// @Summary 测试发送消息
// @Description 测试发送消息
// @Produce json
// @Security HeaderAuth
// @Param param query types.ImMessageSendRequest true "请求参数"
// @Success 200 {object} codec.Response{data=types.ImMessageSendResponse}
// @Router /api/v1/im/message/send [get]
func (h *Im) SendMessage(ctx *api.Context, req types.ImMessageSendRequest) (*types.ImMessageSendResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	var msg im.MessageContent

	if req.Type == int(im.MsgTypeCustom) {
		msg = &im.MessageCustom{
			Content: req.Content,
		}
	} else {
		msg = &im.MessageText{
			Text: req.Content,
		}
	}

	var sender im.Sender

	if req.Notice == 1 {
		sender = &im.SenderSystemNotice{}
	} else if req.Notice == 2 {
		sender = &im.SenderSystemOfficial{}
	} else {
		sender = &im.SenderUserId{
			UserId: uac.UserId,
		}
	}

	var receiver im.Receiver

	if req.ReceiverUserId != "" {
		receiver = &im.ReceiverUserId{
			UserId: req.ReceiverUserId,
		}
	} else {
		receiver = &im.ReceiverTag{}
	}

	err = h.imm.SendMessage(
		ctx,
		sender,
		receiver,
		msg,
	)

	if err != nil {
		return nil, err
	}

	return &types.ImMessageSendResponse{}, nil
}

// @Tags 消息
// @Summary 刷新会话设置
// @Description 刷新会话设置
// @Produce json
// @Security HeaderAuth
// @Param param body types.ImCsRefreshRequest true "请求参数"
// @Success 200 {object} codec.Response
// @Router /api/v1/im/cs/refresh [post]
func (h *Im) RefreshCs(ctx *api.Context, req types.ImCsRefreshRequest) (*api.EmptyResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	userId := uac.UserId
	shortConversationId := req.ShortConversationId

	h.imm.ClearConversationSettingCache(ctx, userId, shortConversationId)

	return &api.EmptyResp{}, nil
}

func (h *Im) Callback(ctx *gin.Context) error {
	st := time.Now()

	var event im.Event

	logger := h.logger

	err := ctx.ShouldBindJSON(&event)
	if err != nil {
		logger.Error("IM回调通知", zap.Error(err))

		ctx.JSON(http.StatusBadRequest, gin.H{})
		ctx.Abort()

		return err
	}

	logger = logger.With(
		zap.Any("request", event),
	)

	var beforeSendMessageData *im.EventDataBeforeSendMessage
	var afterSendMessageData *im.EventDataAfterSendMessage
	// 客户端消息ID才能真正保证幂等
	var clientMessageId string

	// 解析数据放前面
	switch event.EventType {
	case im.EventTypeBeforeSendMessage:
		err := json.Unmarshal([]byte(event.EventData), &beforeSendMessageData)

		if err != nil {
			logger.Error("IM回调通知", zap.Error(err))
		} else {
			clientMessageId = beforeSendMessageData.ClientMessageId
		}
	case im.EventTypeAfterSendMessage:
		err := json.Unmarshal([]byte(event.EventData), &afterSendMessageData)

		if err != nil {
			logger.Error("IM回调通知", zap.Error(err))
		} else {
			clientMessageId = afterSendMessageData.MessageBody.Ext["s:client_message_id"]
		}
	}

	if clientMessageId == "" {
		logger.Error("IM回调通知", zap.String("from", "empty clientMessageId"))

		ctx.JSON(http.StatusBadRequest, gin.H{"error": "clientMessageId is empty"})
		ctx.Abort()

		return nil
	}

	l, err := h.dm.Lock(ctx, fmt.Sprintf("STR:MUTEX:IM:CALLBACK:%s:%s", event.EventType, clientMessageId))
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "callback is processing"})
		ctx.Abort()

		return nil
	}
	defer l.MustUnlock()

	// 获取缓存结果
	cacheKey := fmt.Sprintf("STR:IM:CALLBACK:%s:%s", event.EventType, clientMessageId)

	if str, err := h.rc.Get(ctx, cacheKey).Result(); err == nil {
		var res im.CallbackResponse

		err := json.Unmarshal([]byte(str), &res)

		if err == nil {
			logger.Info(
				"IM回调通知",
				zap.String("from", "cache"),
				zap.Any("response", res),
				zap.Int64("duration", time.Since(st).Milliseconds()),
			)

			ctx.JSON(http.StatusOK, res)

			return nil
		}
	}

	// 定义返回异常结构
	sws := make([]string, 0)
	var (
		riskLevel string
		sendDeny  bool
	)

	switch event.EventType {
	case im.EventTypeBeforeSendMessage:
		if beforeSendMessageData != nil {
			systemSenders := h.imm.GetSystemNoticeNumIds()
			sender := beforeSendMessageData.MessageBody.Sender

			// 系统消息不处理
			if !lo.Contains(systemSenders, sender) {
				if uac, err := h.ug.GetByNumId(ctx, sender); err == nil {
					if h.ibm.Blocking(ctx, uac.UserId) {
						sendDeny = true
						goto makeResp
					}
					if patrol.Has(uac.Roles) && h.pg.Take(ctx, uac.UserId).Limited() {
						sendDeny = true
						goto makeResp
					}
					toAcc, err := h.ug.GetByNumId(ctx, beforeSendMessageData.ToId)

					if err == nil {
						result := h.ilm.IsDeny(ctx, beforeSendMessageData.MessageBody.ConversationShortId, clientMessageId, uac.UserId, toAcc.UserId)
						if result.Deny {
							sendDeny = true
							goto makeResp
						}
					}
				}
				switch beforeSendMessageData.MessageBody.MsgType {
				case int(im.MsgTypeText):
					var mt im.MessageText

					err := json.Unmarshal([]byte(beforeSendMessageData.MessageBody.Content), &mt)

					if err != nil {
						logger.Error("IM回调通知", zap.Error(err))
					} else {
						// 敏感词检测
						sws = h.reviewm.SensitiveWords(mt.Text)

						logger = logger.With(
							zap.Strings("sensitive_words", sws),
						)
					}
				case int(im.MsgTypeImage):
					var mi im.MessageImage

					err := json.Unmarshal([]byte(beforeSendMessageData.MessageBody.Content), &mi)

					if err != nil {
						logger.Error("IM回调通知", zap.Error(err))
					} else {
						// 图片审核
						result, err := h.reviewm.ReviewImage(ctx, mi.Files.Media.RemoteURL)

						if err != nil {
							logger.Error("IM回调通知", zap.Error(err))
						} else {
							riskLevel = result.RiskLevel
						}
					}
				}
			}
		}
	case im.EventTypeAfterSendMessage:
		if afterSendMessageData != nil {
			systemSenders := h.imm.GetSystemNoticeNumIds()
			fromId := afterSendMessageData.FromId
			toId := afterSendMessageData.ToId

			// 系统消息不处理
			if !lo.Contains(systemSenders, fromId) {
				fromAcc, err1 := h.ug.GetByNumId(ctx, fromId)
				toAcc, err2 := h.ug.GetByNumId(ctx, toId)

				if err1 == nil && err2 == nil {
					// 统计用户间的往来消息
					h.ilm.AfterSendMessage(ctx, fromAcc.UserId, toAcc.UserId)
				}
			}

			if afterSendMessageData.MessageBody.MsgType == int(im.MsgTypeText) {
				h.pd.IMAfterSend(ctx,
					afterSendMessageData.MessageBody.ConversationShortId, fromId, toId,
					afterSendMessageData.MessageBody.Content,
					afterSendMessageData.MessageBody.Ext,
				)
			}
		}
	}

makeResp:
	res := im.CallbackResponse{}

	if len(sws) > 0 || riskLevel == review.RiskLevelHigh {
		res.CheckCode = 1

		logger = logger.With(
			zap.Int("status", 1),
		)
	}

	if sendDeny {
		res.CheckCode = 1
	}

	logger.Info(
		"IM回调通知",
		zap.Int64("duration", time.Since(st).Milliseconds()),
		zap.Any("response", res),
	)

	b, _ := sonic.Marshal(res)

	h.rc.SetEX(ctx, cacheKey, string(b), time.Second*20)

	ctx.JSON(http.StatusOK, res)

	return nil
}
