package mixer

import (
	"gitlab.sskjz.com/overseas/live/osl/internal/fclub"
	"gitlab.sskjz.com/overseas/live/osl/internal/follow"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/internal/link"
	"gitlab.sskjz.com/overseas/live/osl/internal/live"
	"gitlab.sskjz.com/overseas/live/osl/internal/manage/patrol"
	"gitlab.sskjz.com/overseas/live/osl/internal/pk"
	"gitlab.sskjz.com/overseas/live/osl/internal/seller"
	"gitlab.sskjz.com/overseas/live/osl/internal/urm"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/dress"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/privacy"
	"gitlab.sskjz.com/overseas/live/osl/pkg/region"
)

func Provide(
	fg fund.Getter,
	fi follow.Getter,
	rg region.Getter,
	lm *live.Manager,
	pg patrol.Getter,
	sg seller.Getter,
	urm *urm.Manager,
	dsm *dress.Manager,
	fc fclub.Getter,
	pm *pk.Manager,
	lkm *link.Manager,
	pvm *privacy.Manager,
) *Mixer {
	return &Mixer{
		fg:  fg,
		fi:  fi,
		rg:  rg,
		lm:  lm,
		pg:  pg,
		sg:  sg,
		urm: urm,
		dsm: dsm,
		fc:  fc,
		pkm: pm,
		lkm: lkm,
		pvm: pvm,
	}
}
