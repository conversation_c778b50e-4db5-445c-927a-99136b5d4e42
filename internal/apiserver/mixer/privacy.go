package mixer

import (
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/privacy"
)

func PublicPrivacy(setting *privacy.Setting) *types.PublicPrivacy {
	return &types.PublicPrivacy{
		HideMomentLike:  setting.HideMomentLike,
		HideLiveSession: setting.HideLiveSession,
		HideLinkStatus:  setting.HideLinkStatus,
	}
}

func PrivatePrivacy(setting *privacy.Setting) *types.UserPrivacy {
	return &types.UserPrivacy{
		PublicPrivacy: *PublicPrivacy(setting),
		HideGender:    setting.HideGender,
		HideBirthday:  setting.HideBirthday,
	}
}
