package mixer

import (
	"context"

	"github.com/gin-gonic/gin"
	"gitlab.sskjz.com/overseas/live/osl/internal/fclub"
	"gitlab.sskjz.com/overseas/live/osl/internal/follow"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/internal/link"
	"gitlab.sskjz.com/overseas/live/osl/internal/live"
	"gitlab.sskjz.com/overseas/live/osl/internal/manage/patrol"
	"gitlab.sskjz.com/overseas/live/osl/internal/pk"
	"gitlab.sskjz.com/overseas/live/osl/internal/seller"
	"gitlab.sskjz.com/overseas/live/osl/internal/urm"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/dress"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/privacy"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/region"
)

const ctxIns = "mixer"

type Mixer struct {
	fg  fund.Getter
	fi  follow.Getter
	rg  region.Getter
	lm  *live.Manager
	pg  patrol.Getter
	sg  seller.Getter
	urm *urm.Manager
	dsm *dress.Manager
	fc  fclub.Getter
	pkm *pk.Manager
	lkm *link.Manager
	pvm *privacy.Manager
}

func (s *Mixer) Middleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Set(ctxIns, s)
		c.Next()
	}
}

func hasMixer(ctx context.Context) *Mixer {
	if g := api.Unwrap(ctx); g != nil {
		if v, has := g.Get(ctxIns); has {
			return v.(*Mixer)
		}
	}
	return nil
}
