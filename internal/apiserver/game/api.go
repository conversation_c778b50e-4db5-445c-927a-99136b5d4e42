package game

import (
	"encoding/json"
	"slices"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
	"gitlab.sskjz.com/go/i3n"
	"gitlab.sskjz.com/overseas/live/osl/internal/game"
	"gitlab.sskjz.com/overseas/live/osl/internal/game/bs"
	"gitlab.sskjz.com/overseas/live/osl/internal/game/bs2"
	"gitlab.sskjz.com/overseas/live/osl/internal/game/jing"
	"gitlab.sskjz.com/overseas/live/osl/internal/payc"
	"gitlab.sskjz.com/overseas/live/osl/pkg/app"
	"gitlab.sskjz.com/overseas/live/osl/pkg/biz"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/auth"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/cache"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/codec"
)

func Invoke(r *api.Router, mgr *game.Manager, gus *game.Store, hc *cache.Handler, pp payc.Getter) {
	a := &Api{mgr: mgr, gus: gus, pp: pp}
	ar := r.WithAuth()
	{
		interrupter := func(ctx *gin.Context) {
			var (
				notRelease = func() {
					ctx.AbortWithStatusJSON(200, codec.Response{Code: biz.ErrBusiness, Msg: i3n.T(ctx, "Not Open Yet")})
				}
			)

			uac, err := auth.User(ctx)
			if err != nil {
				notRelease()
				return
			}

			if openGame := app.IsAppStore(ctx) || gus.OpenGame(ctx, uac); !openGame {
				ctx.AbortWithStatusJSON(200, codec.Response{Code: biz.ErrBusiness, Msg: i3n.T(ctx, "Not supported in your region")})
				return
			}

			if game.Open(ctx) {
				ctx.Next()
				return
			}

			notRelease()
		}

		ar.GET("/mgame/list", interrupter, api.Generic(a.List))
		ar.GET("/game/hub", interrupter, api.Generic(a.HubList))
	}

	ar.POST("/game/session/code", api.Generic(a.GetCode))
}

type Api struct {
	mgr *game.Manager
	gus *game.Store
	pp  payc.Getter
}

func (a *Api) gameList(ctx *api.Context) ([]*game.ListItem, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	show := lo.ToPtr(true)
	if gu, _ := a.gus.User(ctx, uac.UserId); gu != nil && (gu.Role == game.RoleDeveloper) {
		show = nil
	}

	lst, err := a.mgr.ListShowItems(ctx, show)
	if err != nil {
		return nil, err
	}

	var (
		devType          = app.DeviceType(ctx)
		cv               = app.Version(ctx)
		hideBlackJack    bool
		hideBSFullscreen bool = true
	)

	lst = lo.Filter(lst, func(item *game.ListItem, _ int) bool {
		if !app.CVGTE(ctx, app.V1110, 62) && item.FV {
			return false
		}
		return len(item.Data) > 0 && item.Platform != bs.Platform
	})

	switch devType {
	case "android":
		hideBlackJack = cv.LessThan(app.V140)
		hideBSFullscreen = cv.LessThan(app.V160)
	case "ios":
		hideBlackJack = !app.CVGTE(ctx, app.V140, 31)

		hideBSFullscreen = !app.CVGTE(ctx, app.V160, 37)
	default:
		hideBlackJack = false
	}

	if hideBlackJack {
		lst = lo.Filter(lst, func(item *game.ListItem, _ int) bool {
			return item.Platform != bs2.Platform || item.GameID != "1047"
		})
	}

	if hideBSFullscreen {
		lst = lo.Filter(lst, func(item *game.ListItem, _ int) bool {
			return item.Platform != bs2.Platform || (slices.Contains(item.GameMode, game.ModeLive) && item.Sorts[game.CatalogFullScreen] <= 0)
		})
	}

	return lst, nil
}

type ListItem struct {
	Id         string          `json:"id"`
	Name       string          `json:"name"`
	Desc       string          `json:"desc"`
	Tags       []string        `json:"tags"`
	Icon       string          `json:"icon"`
	Cover      string          `json:"cover"` // 大图展示
	Fullscreen bool            `json:"fullscreen"`
	Landscape  bool            `json:"landscape"`
	Sort       int             `json:"sort"`
	Config     any             `json:"config"`
	Entry      json.RawMessage `json:"entry"`
}

type ListResponse = map[string][]ListItem

type fakeListItem struct {
	Id         string   `json:"id"`
	Name       string   `json:"name"`       // 游戏名称
	Desc       string   `json:"desc"`       // 游戏描述
	Tags       []string `json:"tags"`       // 游戏标签
	Icon       string   `json:"icon"`       // 游戏图标
	Fullscreen bool     `json:"fullscreen"` // 是否全屏
	Landscape  bool     `json:"landscape"`  // 是否横屏
	Sort       int      `json:"sort"`       // 排序
	Config     any      `json:"config"`     // 游戏配置,百顺的配置格式{"app_id":123,"gsp":304,"app_channel":"kako"}
	Entry      any      `json:"entry"`      // 游戏入口
}
type fakeListResponse = map[string][]fakeListItem

// @Tags MiniGame
// @Summary Mini游戏列表
// @Description Mini游戏列表, 服务端缓存15秒
// @Produce json
// @Security HeaderAuth
// @Success 200 {object} codec.Response{data=fakeListResponse}
// @Router /api/v1/mgame/list [get]
func (a *Api) List(ctx *api.Context, _ api.EmptyReq) (ListResponse, error) {
	lst, err := a.gameList(ctx)
	if err != nil {
		return nil, err
	}

	out := make([]*game.ListItem, 0, len(lst))
	for _, v := range lst {
		if v.Platform == jing.Platform {
			continue
		}

		if v.Platform != bs.Platform { // 这个平台不动，等后期删除这个平台
			if len(v.Sorts) > 0 && v.Sorts[game.CatalogLive] <= 0 {
				continue
			}
			v.Sort = v.Sorts[game.CatalogLive]
		}

		out = append(out, v)
	}
	return resortGameList(ctx, a.mgr, out, SceneLive), nil
}

func resortGameList(ctx *api.Context, mgr *game.Manager, lst []*game.ListItem, s Scene) map[string][]ListItem {
	fvSupported := app.CVGTE(ctx, app.V1110, 62)

	gMap := make(map[string][]*game.ListItem)
	for _, v := range lst {
		gMap[v.Platform] = append(gMap[v.Platform], v)
	}

	out := make(map[string][]ListItem)
	for platform, items := range gMap {
		var b []ListItem
		for _, itm := range items {
			item := ListItem{
				Id:         itm.ID.Hex(),
				Name:       itm.Name,
				Desc:       itm.LocalDesc(ctx),
				Icon:       itm.Icon,
				Fullscreen: false,
				Landscape:  false,
				Cover:      itm.Cover,
				Tags:       itm.LocalTags(ctx),
				Sort:       itm.Sort,
				Config:     mgr.Config(itm.Platform),
				Entry:      itm.Data,
			}

			if len(itm.GameMode) == 1 {
				item.Fullscreen = itm.GameMode[0] == game.ModeFullscreen
			} else {
				if s == SceneHub {
					item.Fullscreen = slices.Contains(itm.GameMode, game.ModeFullscreen) && itm.Sorts[game.CatalogFullScreen] > 0
				} else {
					item.Fullscreen = false
				}
			}

			if item.Fullscreen {
				if itm.FV {
					item.Landscape = false
				} else {
					item.Landscape = true
				}
			}

			// 兼容老版本
			if !fvSupported {
				item.Landscape = item.Fullscreen
			}

			b = append(b, item)
		}
		if len(b) > 0 {
			out[platform] = b
		}
	}

	return out
}

type Catalog struct {
	Name    string   `json:"name"`
	GameIds []string `json:"gameIds"`
}

type HubListResponse struct {
	Recent   []string              `json:"recent"` // gameids
	Banner   []string              `json:"banner"` // gameids
	Games    map[string][]ListItem `json:"games"`
	Catalogs []Catalog             `json:"catalogs"`
}

type fakeHubListResponse struct {
	Recent   []string                  `json:"recent"` // gameids
	Banner   []string                  `json:"banner"` // gameids
	Games    map[string][]fakeListItem `json:"games"`
	Catalogs []Catalog                 `json:"catalogs"`
}

type Scene int

const (
	SceneHub  Scene = iota // 游戏中心
	SceneLive              // 直播间
)

type HubListRequest struct {
	Scene Scene `form:"scene"`
}

// @Tags 游戏中心
// @Summary 游戏中心列表
// @Description 游戏中心列表
// @Produce json
// @Security HeaderAuth
// @Param scene query Scene false "场景: 0-游戏中心, 1-直播间"
// @Success 200 {object} codec.Response{data=fakeHubListResponse}
// @Router /api/v1/game/hub [get]
func (a *Api) HubList(ctx *api.Context, req HubListRequest) (*HubListResponse, error) {
	lst, err := a.gameList(ctx)
	if err != nil {
		return nil, err
	}

	if req.Scene == SceneLive {
		lst = lo.Filter(lst, func(v *game.ListItem, _ int) bool {
			return v.Sorts[game.CatalogLive] > 0
		})
	}

	var (
		catalogs = make(map[string][]string)
		gameMap  = make(map[string]*game.ListItem)
	)
	for _, v := range lst {
		gameMap[v.ID.Hex()] = v

		for c, s := range v.Sorts {
			if s > 0 {
				catalogs[c] = append(catalogs[c], v.ID.Hex())
			}
		}
	}

	var resp = &HubListResponse{
		Banner: catalogs[game.CatalogBanner],
		Games:  resortGameList(ctx, a.mgr, lst, req.Scene),
		Catalogs: []Catalog{
			{Name: game.CatalogHot, GameIds: []string{}},
			{Name: game.CatalogLive, GameIds: []string{}},
			{Name: game.CatalogFullScreen, GameIds: []string{}},
		},
	}

	for i, c := range resp.Catalogs {
		if v, ok := catalogs[c.Name]; ok {
			resp.Catalogs[i].GameIds = v
		}
	}

	for _, v := range resp.Catalogs {
		slices.SortFunc(v.GameIds, func(a, b string) int {
			gameA := gameMap[a]
			gameB := gameMap[b]
			return gameA.Sorts[v.Name] - gameB.Sorts[v.Name]
		})
	}

	slices.SortFunc(resp.Banner, func(a, b string) int {
		gameA := gameMap[a]
		gameB := gameMap[b]
		return gameA.Sorts[game.CatalogBanner] - gameB.Sorts[game.CatalogBanner]
	})

	if uac, err := ctx.User(); err == nil && uac != nil {
		played, _ := a.gus.Played(ctx, uac.UserId)
		resp.Recent = lo.Filter(played, func(id string, _ int) bool {
			return gameMap[id] != nil
		})

		if len(resp.Recent) > 5 {
			resp.Recent = resp.Recent[:5]
		}
	}

	if req.Scene == SceneLive {
		resp.Catalogs = []Catalog{
			{Name: game.CatalogHot, GameIds: []string{}},
			{Name: game.CatalogLive, GameIds: catalogs[game.CatalogLive]},
			{Name: game.CatalogFullScreen, GameIds: []string{}},
		}
	}

	return resp, nil
}
