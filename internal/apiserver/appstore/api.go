package appstore

import (
	"cmp"
	"errors"
	"slices"
	"strconv"

	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/go/redi"
	"gitlab.sskjz.com/overseas/live/osl/internal/pay"
	"gitlab.sskjz.com/overseas/live/osl/internal/pay/sb"
	"gitlab.sskjz.com/overseas/live/osl/internal/payc"
	"gitlab.sskjz.com/overseas/live/osl/pkg/app"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/rlimit"
)

const gwId = "appstore"

func Invoke(r *api.Router, rl *rlimit.Handler, dm *redi.Mutex, ps *pay.Service, pp payc.Getter, gws *pay.Gateways, sc *sb.Checker, vnd log.Vendor) {
	iap := newIns(ps, sc, vnd.Scope("appstore"))
	gws.Register(gwId, iap)
	s := &apis{dm: dm, ps: ps, pp: pp, iap: iap}
	r.GET("/appstore/config", api.Generic(s.config))
	r.POST("/appstore/notify", api.Request(s.notify))
	r.WithAuth().POST("/appstore/order", api.Generic(s.createOrder))
	r.With(rl.Middleware()).POST("/appstore/verify", api.Generic(s.verifyOrder))
}

type apis struct {
	dm  *redi.Mutex
	ps  *pay.Service
	pp  payc.Getter
	iap *IAP
}

type productItem struct {
	Id  string `json:"id"`  // 产品ID
	SKU string `json:"sku"` // 对应充值SKU
}

type configResp struct {
	Products []productItem `json:"products"` // 内购产品列表
}

// @Tags 苹果内购
// @Summary 获取配置
// @Description 获取配置
// @Produce json
// @Security HeaderAuth
// @Param param body api.EmptyReq true "请求参数"
// @Success 200 {object} codec.Response{data=configResp}
// @Router /api/v1/appstore/config [get]
func (s *apis) config(ctx *api.Context, _ api.EmptyReq) (*configResp, error) {
	skus := products[app.BundleId(ctx)]
	items := make([]productItem, 0, len(skus))
	for id, sku := range skus {
		items = append(items, productItem{Id: id, SKU: sku})
	}
	slices.SortFunc(items, func(a, b productItem) int {
		aa, _ := strconv.Atoi(a.SKU)
		bb, _ := strconv.Atoi(b.SKU)
		return cmp.Compare(aa, bb)
	})
	return &configResp{Products: items}, nil
}

type createOrderReq struct {
	Scene     string `json:"scene" binding:"required"`
	ProductId string `json:"productId" binding:"required"`
}

type createOrderResp struct {
	TradeNo string          `json:"tradeNo"` // 内部交易号
	Status  pay.OrderStatus `json:"status"`  // 订单状态
}

// @Tags 苹果内购
// @Summary 创建订单
// @Description 创建订单
// @Produce json
// @Security HeaderAuth
// @Param param body createOrderReq true "请求参数"
// @Success 200 {object} codec.Response{data=createOrderResp}
// @Router /api/v1/appstore/order [post]
func (s *apis) createOrder(ctx *api.Context, req createOrderReq) (*createOrderResp, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	sku, has := products[app.BundleId(ctx)][req.ProductId]
	if !has {
		return nil, errors.New("invalid product id")
	}

	if app.BundleId(ctx) == "live.kako.global" {
		// testflight users
		return nil, sb.ErrNotSandboxUser
	}

	if policy, err := s.pp.Policy(ctx, uac.UserId); err != nil {
		return nil, err
	} else if policy.IAP == payc.IAPForbid {
		return nil, sb.ErrNotSandboxUser
	}

	order, _, err := s.ps.Create(ctx, gwId, uac.UserId, req.Scene, sku, pay.USD)
	if err != nil {
		return nil, err
	}

	return &createOrderResp{
		TradeNo: order.TradeNo,
		Status:  order.Status,
	}, nil
}

type verifyOrderReq struct {
	TradeNo string `json:"tradeNo" binding:"required"` // 内部交易号
	Receipt string `json:"receipt" binding:"required"` // 支付凭证
}

type verifyOrderResp struct {
	Status pay.OrderStatus `json:"status"` // 订单状态
}

// @Tags 苹果内购
// @Summary 验证支付凭证
// @Description 验证支付凭证
// @Produce json
// @Param param body verifyOrderReq true "请求参数"
// @Success 200 {object} codec.Response{data=verifyOrderResp}
// @Router /api/v1/appstore/verify [post]
func (s *apis) verifyOrder(ctx *api.Context, req verifyOrderReq) (*verifyOrderResp, error) {
	if l, err := s.dm.Lock(ctx, "APPSTORE:VERIFY:"+req.TradeNo); err != nil {
		return nil, err
	} else {
		defer l.MustUnlock()
	}

	order, err := s.iap.Verify(ctx, req.TradeNo, req.Receipt)
	if err != nil {
		return nil, err
	}

	return &verifyOrderResp{Status: order.Status}, nil
}
