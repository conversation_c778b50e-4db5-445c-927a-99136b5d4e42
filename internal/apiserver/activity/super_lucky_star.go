package activity

import (
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/mixer"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http/api"
)

// @Tags 活动
// @Summary 超级幸运星
// @Description 超级幸运星活动信息
// @Produce json
// @Security HeaderAuth
// @Param param query SuperLuckyStarInfoRequest true "请求参数"
// @Success 200 {object} codec.Response{data=SuperLuckyStarInfoResponse}
// @Router /api/v1/activity/superluckystar/info [get]
func (s *apis) SuperLuckyStarInfo(ctx *api.Context, req SuperLuckyStarInfoRequest) (*SuperLuckyStarInfoResponse, error) {
	uac, err := ctx.User()
	if err != nil {
		return nil, err
	}

	var (
		todayRankInfo     SuperLuckyStarRankInfo
		yesterdayRankInfo SuperLuckyStarRankInfo
	)

	info, err := s.slsm.GetRank(ctx, "today", uac.UserId)
	if err != nil {
		return nil, err
	}
	for _, v := range info.RankInfo {
		r := SuperLuckyStarUserRank{
			Rank:  v.Rank,
			User:  mixer.User(ctx, v.User),
			Value: v.Value,
		}
		todayRankInfo.RankList = append(todayRankInfo.RankList, r)
	}

	todayRankInfo.MyRank = SuperLuckyStarUserRank{
		User: mixer.User(ctx, uac),
	}
	if info.MyRank != nil {
		todayRankInfo.MyRank.Rank = info.MyRank.Rank
		todayRankInfo.MyRank.Value = info.MyRank.Value
	}

	yesterdayInfo, err := s.slsm.GetRank(ctx, "yesterday", uac.UserId)
	if err != nil {
		return nil, err
	}
	for _, v := range yesterdayInfo.RankInfo {
		r := SuperLuckyStarUserRank{
			Rank:  v.Rank,
			User:  mixer.User(ctx, v.User),
			Value: v.Value,
		}
		yesterdayRankInfo.RankList = append(yesterdayRankInfo.RankList, r)
	}
	yesterdayRankInfo.MyRank = SuperLuckyStarUserRank{
		User: mixer.User(ctx, uac),
	}
	if yesterdayInfo.MyRank != nil {
		yesterdayRankInfo.MyRank.Rank = yesterdayInfo.MyRank.Rank
		yesterdayRankInfo.MyRank.Value = yesterdayInfo.MyRank.Value
	}

	return &SuperLuckyStarInfoResponse{
		StartAt:           info.StartAt,
		EndAt:             info.EndAt,
		TodayRankInfo:     todayRankInfo,
		YesterdayRankInfo: yesterdayRankInfo,
	}, nil
}
