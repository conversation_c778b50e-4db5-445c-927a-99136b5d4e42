package activity

import (
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/types"
)

type AgencyInviteInfoRequest struct{}

type AgencyInviteInfoResponse struct {
	Status     int               `json:"status"`               // 用户的公会状态 0：未创建公会 1：已成功创建公会
	InviteData *AgencyInviteData `json:"inviteData,omitempty"` // 公会邀请信息，在status为1时才有
}

type AgencyInviteData struct {
	InviteCode   string `json:"inviteCode"`   // 公会邀请码
	InviteApply  int    `json:"inviteApply"`  // 申请中
	InviteCreate int    `json:"inviteCreate"` // 已入驻
}

type LuckyInfoRequest struct {
}

type LuckyInfoResponse struct {
	StartAt           int64           `json:"startAt"`           // 开始时间，秒级时间戳
	EndAt             int64           `json:"endAt"`             // 结束时间，秒级时间戳
	TodayRankInfo     []GiftLuckyInfo `json:"todayRankInfo"`     // 幸运之星信息
	YesterdayRankInfo []GiftLuckyInfo `json:"yesterdayRankInfo"` // 昨日幸运之星信息
}

type GiftLuckyInfo struct {
	GiftId       int      `json:"giftId"`       // 礼物id
	GiftImageUrl string   `json:"giftImageUrl"` // 礼物图片地址
	Award        int      `json:"award"`        // 奖励金额
	LuckyStar    UserRank `json:"luckyStar"`    // 当前幸运之星
	MyRanking    UserRank `json:"myRanking"`    // 我的信息
}

type UserRank struct {
	User       *types.User `json:"user"`       // 用户信息
	LuckyTimes int         `json:"luckyTimes"` // 中奖次数
}

type RebateAwardReceiveRecord struct {
}

type CoinGrabInfoRequest struct {
}

type CoinGrabInfoResponse struct {
	StartAt           int64            `json:"startAt"`           // 开始时间，秒级时间戳
	EndAt             int64            `json:"endAt"`             // 结束时间，秒级时间戳
	CoinPool          int64            `json:"coinPool"`          // 当前总奖池
	TodayRankInfo     CoinGrabRankInfo `json:"todayRankInfo"`     // 今日排名
	YesterdayRankInfo CoinGrabRankInfo `json:"yesterdayRankInfo"` // 昨日排名
}

type CoinGrabRankInfo struct {
	RankList  []CoinGrabUserRank `json:"rankList"`
	MyRank    CoinGrabUserRank   `json:"myRank"`    // 我的信息
	IsReceive bool               `json:"isReceive"` // 是否已领取
}

type CoinGrabUserRank struct {
	Rank   int         `json:"rank"`
	User   *types.User `json:"user"`   // 用户信息
	Value  int64       `json:"value"`  // 积分
	Reward int64       `json:"reward"` // 奖励
}

type CoinGrabWidgetRequest struct {
}

type CoinGrabWidgetResponse struct {
	CoinPool int64 `json:"coinPool"` // 当前总奖池
	MyRank   int   `json:"myRank"`   // 我的排名
}

type CoinGrabRewardReceiveRequest struct {
}

type CoinGrabRewardReceiveResponse struct {
	User   *types.User `json:"user"`   // 用户信息
	Rank   int         `json:"rank"`   // 我的排名
	Reward int64       `json:"reward"` // 我的奖励数量
}

type SuperLuckyStarInfoRequest struct {
}

type SuperLuckyStarInfoResponse struct {
	StartAt           int64                  `json:"startAt"`           // 开始时间，秒级时间戳
	EndAt             int64                  `json:"endAt"`             // 结束时间，秒级时间戳
	TodayRankInfo     SuperLuckyStarRankInfo `json:"todayRankInfo"`     // 今日排名
	YesterdayRankInfo SuperLuckyStarRankInfo `json:"yesterdayRankInfo"` // 昨日排名
}

type SuperLuckyStarRankInfo struct {
	RankList []SuperLuckyStarUserRank `json:"rankList"`
	MyRank   SuperLuckyStarUserRank   `json:"myRank"` // 我的信息
}

type SuperLuckyStarUserRank struct {
	Rank  int         `json:"rank"`
	User  *types.User `json:"user"`  // 用户信息
	Value int64       `json:"value"` // 积分
}
