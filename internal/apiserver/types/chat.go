package types

import "gitlab.sskjz.com/overseas/live/osl/pkg/protocol"

type SendChatMessageRequest struct {
	RoomId  string                `json:"roomId"`
	Content string                `json:"content"`
	Quotes  []string              `json:"quotes"`  // @desc 引用的消息id
	Sticker string                `json:"sticker"` // @desc 贴纸id
	Level   protocol.PayChatLevel `json:"level"`   // @desc 付费聊天等级, 默认0, 1: 基础弹幕, 2: 高级弹幕, 3: 至尊弹幕
}

type SendChatMessageResponse struct {
}

type ChatHistoryRequest struct {
	RoomId string `json:"roomId" form:"roomId"`
}

type HistoryChat struct {
	Id      string      `json:"id"`
	User    UserWithExt `json:"user"`
	Content string      `json:"content"`
	Sticker string      `json:"sticker"`
	Quotes  []User      `json:"quotes"` // @desc 引用的消息id
	At      int64       `json:"at"`     // @desc 发送时间 ms
}

type ChatHistoryResponse struct {
	List []HistoryChat `json:"list"`
}
