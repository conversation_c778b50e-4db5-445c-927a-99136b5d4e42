package types

import (
	"gitlab.sskjz.com/overseas/live/osl/internal/agency/rqa"
	"gitlab.sskjz.com/overseas/live/osl/internal/task"
	"gitlab.sskjz.com/overseas/live/osl/pkg/protocol"
)

// 直播信息
type LiveRoomRequest struct {
	Id         string `form:"id"`         // 房间ID，房间ID和用户ID,嘉宾Id三选一
	UserId     string `form:"userId"`     // 用户ID，房间ID和用户ID,嘉宾Id三选一
	LinkUserId string `form:"linkUserId"` // 嘉宾Id,房间ID和用户ID,嘉宾Id三选一
	Conf       int    `form:"conf"`       // 1:读取开播配置时请求 0:默认
}

type LiveRoomResponse struct {
	Room
	Fansclub *RoomFans `json:"fansclub,omitempty"` // 粉丝团信息（没有时为null）
}

// 直播间数据-在room结构基础上扩展
type LiveRoomExtendedResponse struct {
	Room Room    `json:"room"`         // 房间信息
	Me   *RoomMe `json:"me,omitempty"` // 直播间关于观众的信息（没有时为null）
}

// 直播间数据-与我相关的信息
type LiveRoomMeRequest struct {
	Id     string `form:"id"`     // 房间ID，房间ID和用户ID二选一
	UserId string `form:"userId"` // 用户ID，房间ID和用户ID二选一
}

type LiveRoomMeResponse struct {
	Me *RoomMe `json:"me"` // 直播间关于观众的信息
}

type LiveSpecialRoomRequest struct {
	Scene string `form:"scene" binding:"required"` // 场景标识 lucky:幸运礼物体验房 game:游戏房
}

type LiveRoomStatusRequest struct {
	Id     string `form:"id"`     // 房间ID
	UserId string `form:"userId"` // 用户ID
}

type LiveRoomStatusResponse struct {
	Paused     bool                         `json:"paused"`     // 查询的房间是否进入暂离状态
	Status     protocol.LStatus             `json:"status"`     // 直播状态,和websocket中的状态一致
	PKStatus   *protocol.RoomPKStatusNotify `json:"pkStatus"`   // PK状态
	LinkStatus *protocol.RoomLinkStatus     `json:"linkStatus"` // 连麦状态
}

type LiveRtcTokenRequest struct {
	RoomId     string `form:"roomId"`     // 房间ID 传*表示获取通配符token
	Self       int    `form:"self"`       // 取自己直播间ID
	SelfUserId int    `form:"selfUserId"` // 1：用userId生成token 0：用userId@deviceId生成token
}

type LiveRtcTokenResponse struct {
	UserId     string `json:"userId"`     // 生成RTC token的用户ID
	Token      string `json:"token"`      // RTC token
	ExpireTime int64  `json:"expireTime"` // 过期时间，秒时间戳
}

type LiveLikeRequest struct {
	RoomId string `json:"roomId" binding:"required"` // 房间ID
	Count  int    `json:"count" binding:"gt=0"`      // 点赞数
}

type LiveFaceCheckRequest struct {
	Ticket string `json:"ticket"` // 人脸检测凭证
}

type LiveFaceCheckResponse struct{}

type LiveStartRequest struct {
	rqa.RevokeQuitAgency
	Title string            `json:"title" binding:"required"` // 直播标题
	Cover string            `json:"cover" binding:"required"` // 直播封面
	Ext   map[string]string `json:"ext"`                      // 当前将开播场次扩展字段，room对象会透传回客户端
}

type LiveStartResponse struct {
	RoomId     string            `json:"roomId"`     // 直播间ID
	SessionId  string            `json:"sessionId"`  // 场次ID
	PushStream string            `json:"pushStream"` // 直播推流地址，rtc为使用火山引擎rtc sdk推流
	Ext        map[string]string `json:"ext"`        // 当前将开播场次扩展字段，room对象会透传回客户端
	StartTime  int64             `json:"startTime"`  // 开始时间，秒时间戳
	Token      string            `json:"token"`      // RTC token
	ExpireTime int64             `json:"expireTime"` // RTC token过期时间，秒时间戳
}

type LiveStartErrorResponse struct {
	Title   string `json:"title"`   // 弹窗标题
	Content string `json:"content"` // 弹窗内容
}

type LiveStopRequest struct {
	CheckFace bool `json:"checkFace"` // 是否检测人脸
	Reason    int  `json:"reason"`    // 结束原因 0:主播自己结束 1:另一端重新开播 2+预留
}

type LiveStopResponse struct{}

type LiveHeartbeatRequest struct {
	Stream int `json:"stream" binding:"required,gte=1,lte=2"` // 1:流正常 2:流异常或其他
}

type LiveHeartbeatResponse struct {
	Warning *LiveWarning `json:"warning"` // 直播警告
}

type LiveRecordRequest struct {
	SessionId string `form:"sessionId" binding:"required"` // 场次ID
}

type LiveRecordResponse struct {
	RoomId        string           `json:"roomId"`        // 房间ID
	SessionId     string           `json:"sessionId"`     // 场次ID
	StartTime     int64            `json:"startTime"`     // 开始时间，秒时间戳
	EndTime       int64            `json:"endTime"`       // 结束时间，秒时间戳
	Duration      int64            `json:"duration"`      // 直播时长，单位秒
	PauseDuration int64            `json:"pauseDuration"` // 暂离时长，单位秒
	LuckDiamond   int64            `json:"luckDiamond"`   // 幸运礼物收入（音符）
	GiftDiamond   int64            `json:"giftDiamond"`   // 特效礼物收入（音波）
	NewFanCount   int64            `json:"newFanCount"`   // 新增粉丝
	AudCount      int64            `json:"audCount"`      // 观看人数
	GiftUserCount int64            `json:"giftUserCount"` // 送礼人数
	LikeCount     int64            `json:"likeCount"`     // 点赞次数
	Termination   *LiveTermination `json:"termination"`   // 中断信息
}

type LiveTermination struct {
	Content string `json:"content"` // 中断原因
}

// 直播动态
type LiveMomentRequest struct {
	UserId string `form:"userId"` // 主播ID
}

type LiveMomentResponse struct {
	Notice   bool                `json:"notice"`   // 是否设置了开播提醒
	Hidden   bool                `json:"hidden"`   // 主播是否隐藏开播记录
	Sessions []LiveMomentSession `json:"sessions"` // 直播动态历史直播回顾
}

// 直播动态历史直播回顾
type LiveMomentSession struct {
	StartTime int64  `json:"startTime"` // 开始时间，秒时间戳
	EndTime   int64  `json:"endTime"`   // 结束时间，秒时间戳
	Title     string `json:"title"`     // 标题
}

type LiveNoticeSetRequest struct {
	UserId string `json:"userId"` // 主播ID
	Notice bool   `json:"notice"` // 设置开播提醒 true提醒 false取消提醒
}

type LiveNoticeSetResponse struct{}

type LiveAppointmentSetRequest struct {
	UserId string `json:"userId"` // 主播ID
}

type LiveProfileRequest struct {
	UserId string `form:"userId"` // 主播ID
}

type LiveProfileResponse struct {
	Self             bool  `json:"self"`             // 是否是自己
	SessionCount     int64 `json:"sessionCount"`     // 近7日直播场次数
	Income           int64 `json:"income"`           // 近7日直播收益（元宝），只有self=true时有效
	FclubMemberCount int64 `json:"fclubMemberCount"` // !V2版本实现 粉丝灯牌数量，只有self=true时有效
	GuardCount       int64 `json:"guardCount"`       // !V2版本实现 我的守护数量，只有self=true时有效
}

type LiveGiftStatRequest struct {
	RoomId   string `form:"roomId"`   // 直播间ID
	WithTask int    `form:"withTask"` // 是否返回幸运礼物音符任务数据 1:返回 0:不返回
}

type LiveGiftStatResponse struct {
	LuckDiamondTask *task.AnchorTask `json:"luckDiamondTask"` // 幸运礼物任务
	GiftDiamond     int64            `json:"giftDiamond"`     // 特效礼物，金币
	LuckDiamond     int64            `json:"luckDiamond"`     // 幸运礼物，金币
	Total           int64            `json:"total"`           // 总收礼（20240814改为幸运礼物值WX）
}

type LiveRtcStreamOperateRequest struct {
	Operate string `json:"operate"` // 操作类型 ban封禁 unban解封
	RoomId  string `json:"roomId"`  // 房间ID
	UserId  string `json:"userId"`  // 被封禁/解封的用户ID
}

type LiveRtcStreamOperateResponse struct{}

type LiveWarningOptionsResponse struct {
	WarnOptions []LiveWarnOption `json:"warnOptions"` // 警告选项
	BanOptions  []LiveBanOption  `json:"banOptions"`  // 中断选项
}

type LiveWarnOption struct {
	Id     string `json:"id"`     // 警告原因ID
	Reason string `json:"reason"` // 警告原因
}

type LiveBanOption struct {
	Name    string `json:"name"`    // 中断选项名称
	Seconds int    `json:"seconds"` // 中断时长，单位秒
}

type LiveWarning struct {
	Id          string `json:"id"`          // 警告ID
	Title       string `json:"title"`       // 警告标题
	Content     string `json:"content"`     // 警告内容
	LiveEndTime int64  `json:"liveEndTime"` // 自动关闭直播时间，秒时间戳
}

type LiveWarningWarnRequest struct {
	UserId string `json:"userId"` // 主播ID
	Id     string `json:"id"`     // 警告原因ID
}

type LiveWarningBanRequest struct {
	UserId  string `json:"userId"`                            // 主播ID
	Seconds int    `json:"seconds" binding:"min=1,max=86400"` // 中断时长，单位秒
}

type LiveWarningConfirmRequest struct {
	WarningId string `json:"warningId"` // 警告ID
}

type LiveOnlineCheckRequest struct {
	RoomIds []string `json:"roomIds" form:"roomIds"` // 直播间ID，最多50个
}

type LiveOnlineCheckResponse struct {
	RoomIds []string `json:"roomIds"` // 直播间ID
}

type LiveWarningHideRequest struct {
	UserId string `json:"userId"` // 主播ID
}
