package types

type ViewProfileRequest struct {
	UserId string `form:"userId"`
}

type PublicProfile struct {
	User
	UserExt
	UserSocial
	FollowState
	UserMoment
	UserGrant
	Privacy *PublicPrivacy `json:"privacy"` // 隐私设置
}

// 用户私人信息
type UserPrivate struct {
	Verified bool   `json:"verified"` // 实名认证
	Friends  int    `json:"friends"`  // 朋友数
	Birthday string `json:"birthday"` // 生日：格式 YYYYMMDD
	Region   string `json:"region"`   // 区域代码
}

// 用户隐私设置可公开部分
type PublicPrivacy struct {
	HideMomentLike  bool `json:"hideMomentLike"`  // 隐藏点赞内容
	HideLiveSession bool `json:"hideLiveSession"` // 隐藏直播动态
	HideLinkStatus  bool `json:"hideLinkStatus"`  // 隐藏连线状态
}

// 用户隐私设置
type UserPrivacy struct {
	PublicPrivacy
	HideGender   bool `json:"hideGender"`   // 隐藏性别
	HideBirthday bool `json:"hideBirthday"` // 隐藏年龄
}

type PrivateProfile struct {
	PublicProfile
	UserPrivate
	LevelInfo *LevelInfo   `json:"levelInfo"` // 等级信息
	Wallet    *UserWallet  `json:"wallet"`    // 钱包信息
	Privacy   *UserPrivacy `json:"privacy"`   // 隐私设置
}
