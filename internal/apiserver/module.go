package apiserver

import (
	swagUI "gitlab.sskjz.com/overseas/live/osl/api/swagger/ui"
	"gitlab.sskjz.com/overseas/live/osl/internal/activity/activity_20240819"
	"gitlab.sskjz.com/overseas/live/osl/internal/activity/agency_invite"
	"gitlab.sskjz.com/overseas/live/osl/internal/activity/agency_newbie"
	blindBoxActivity "gitlab.sskjz.com/overseas/live/osl/internal/activity/blindbox"
	"gitlab.sskjz.com/overseas/live/osl/internal/activity/blindbox_collect"
	"gitlab.sskjz.com/overseas/live/osl/internal/activity/christmas"
	"gitlab.sskjz.com/overseas/live/osl/internal/activity/coin_grab"
	"gitlab.sskjz.com/overseas/live/osl/internal/activity/easter"
	"gitlab.sskjz.com/overseas/live/osl/internal/activity/foolsday"
	"gitlab.sskjz.com/overseas/live/osl/internal/activity/halloween"
	"gitlab.sskjz.com/overseas/live/osl/internal/activity/hourlyrank"
	"gitlab.sskjz.com/overseas/live/osl/internal/activity/laborious"
	"gitlab.sskjz.com/overseas/live/osl/internal/activity/launch"
	"gitlab.sskjz.com/overseas/live/osl/internal/activity/lucky_star"
	"gitlab.sskjz.com/overseas/live/osl/internal/activity/payment_vehicle"
	"gitlab.sskjz.com/overseas/live/osl/internal/activity/rebate"
	"gitlab.sskjz.com/overseas/live/osl/internal/activity/rlaa"
	"gitlab.sskjz.com/overseas/live/osl/internal/activity/super_lucky_star"
	"gitlab.sskjz.com/overseas/live/osl/internal/activity/valentines_day"
	"gitlab.sskjz.com/overseas/live/osl/internal/activity/widget"
	"gitlab.sskjz.com/overseas/live/osl/internal/adjust"
	"gitlab.sskjz.com/overseas/live/osl/internal/adm"
	"gitlab.sskjz.com/overseas/live/osl/internal/agency"
	"gitlab.sskjz.com/overseas/live/osl/internal/anchor"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/activity"
	admin2 "gitlab.sskjz.com/overseas/live/osl/internal/apiserver/admin"
	appstoreAPI "gitlab.sskjz.com/overseas/live/osl/internal/apiserver/appstore"
	certAPI "gitlab.sskjz.com/overseas/live/osl/internal/apiserver/cert"
	clientAPI "gitlab.sskjz.com/overseas/live/osl/internal/apiserver/client"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/connect"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/connect/refer"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/fansclub"
	sflytext "gitlab.sskjz.com/overseas/live/osl/internal/apiserver/flytext"
	followAPI "gitlab.sskjz.com/overseas/live/osl/internal/apiserver/follow"
	gameAPI "gitlab.sskjz.com/overseas/live/osl/internal/apiserver/game"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/handler"
	interactionAPI "gitlab.sskjz.com/overseas/live/osl/internal/apiserver/interaction"
	interactionBlindBoxAPI "gitlab.sskjz.com/overseas/live/osl/internal/apiserver/interaction/blindbox"
	interactionRocketAPI "gitlab.sskjz.com/overseas/live/osl/internal/apiserver/interaction/rocket"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/interaction/sredpacket"
	giftStickerAPI "gitlab.sskjz.com/overseas/live/osl/internal/apiserver/interaction/sticker"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/ipk"
	linkAPI "gitlab.sskjz.com/overseas/live/osl/internal/apiserver/link"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/location"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/login"
	appleLogin "gitlab.sskjz.com/overseas/live/osl/internal/apiserver/login/appleid"
	grantLogin "gitlab.sskjz.com/overseas/live/osl/internal/apiserver/login/grant"
	phoneLogin "gitlab.sskjz.com/overseas/live/osl/internal/apiserver/login/phone"
	luckyroomAPI "gitlab.sskjz.com/overseas/live/osl/internal/apiserver/luckyroom"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/mixer"
	mockAPI "gitlab.sskjz.com/overseas/live/osl/internal/apiserver/mock"
	payermaxAPI "gitlab.sskjz.com/overseas/live/osl/internal/apiserver/payermax"
	playstoreAPI "gitlab.sskjz.com/overseas/live/osl/internal/apiserver/playstore"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/profile"
	userCard "gitlab.sskjz.com/overseas/live/osl/internal/apiserver/profile/card"
	propsAPI "gitlab.sskjz.com/overseas/live/osl/internal/apiserver/props"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/public"
	rechargeAPI "gitlab.sskjz.com/overseas/live/osl/internal/apiserver/recharge"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/security"
	sellerAPI "gitlab.sskjz.com/overseas/live/osl/internal/apiserver/seller"
	stickerAPI "gitlab.sskjz.com/overseas/live/osl/internal/apiserver/sticker"
	translateAPI "gitlab.sskjz.com/overseas/live/osl/internal/apiserver/translate"
	"gitlab.sskjz.com/overseas/live/osl/internal/apiserver/upload"
	walletAPI "gitlab.sskjz.com/overseas/live/osl/internal/apiserver/wallet"
	exchangeAPI "gitlab.sskjz.com/overseas/live/osl/internal/apiserver/wallet/exchange"
	journalAPI "gitlab.sskjz.com/overseas/live/osl/internal/apiserver/wallet/jstream"
	withdrawAPI "gitlab.sskjz.com/overseas/live/osl/internal/apiserver/wallet/withdraw"
	"gitlab.sskjz.com/overseas/live/osl/internal/asset"
	"gitlab.sskjz.com/overseas/live/osl/internal/banner"
	"gitlab.sskjz.com/overseas/live/osl/internal/binance"
	"gitlab.sskjz.com/overseas/live/osl/internal/client"
	"gitlab.sskjz.com/overseas/live/osl/internal/client/clogs"
	"gitlab.sskjz.com/overseas/live/osl/internal/contrib"
	"gitlab.sskjz.com/overseas/live/osl/internal/evt"
	"gitlab.sskjz.com/overseas/live/osl/internal/face"
	"gitlab.sskjz.com/overseas/live/osl/internal/fclub"
	"gitlab.sskjz.com/overseas/live/osl/internal/fclub/fctask"
	"gitlab.sskjz.com/overseas/live/osl/internal/feed"
	"gitlab.sskjz.com/overseas/live/osl/internal/follow"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund/journal"
	fundOrder "gitlab.sskjz.com/overseas/live/osl/internal/fund/order"
	"gitlab.sskjz.com/overseas/live/osl/internal/game"
	"gitlab.sskjz.com/overseas/live/osl/internal/game/bs"
	"gitlab.sskjz.com/overseas/live/osl/internal/game/bs2"
	"gitlab.sskjz.com/overseas/live/osl/internal/game/jing"
	"gitlab.sskjz.com/overseas/live/osl/internal/game/ss"
	"gitlab.sskjz.com/overseas/live/osl/internal/ganopay"
	"gitlab.sskjz.com/overseas/live/osl/internal/gift"
	"gitlab.sskjz.com/overseas/live/osl/internal/gift/trade"
	"gitlab.sskjz.com/overseas/live/osl/internal/history"
	"gitlab.sskjz.com/overseas/live/osl/internal/im"
	imBan "gitlab.sskjz.com/overseas/live/osl/internal/im/ban"
	"gitlab.sskjz.com/overseas/live/osl/internal/im/limiter"
	imPush "gitlab.sskjz.com/overseas/live/osl/internal/im/push"
	"gitlab.sskjz.com/overseas/live/osl/internal/in"
	"gitlab.sskjz.com/overseas/live/osl/internal/interaction/blindbox"
	"gitlab.sskjz.com/overseas/live/osl/internal/interaction/redpacket"
	"gitlab.sskjz.com/overseas/live/osl/internal/interaction/redpacket/gifting"
	"gitlab.sskjz.com/overseas/live/osl/internal/interaction/rocket"
	"gitlab.sskjz.com/overseas/live/osl/internal/link"
	"gitlab.sskjz.com/overseas/live/osl/internal/live"
	"gitlab.sskjz.com/overseas/live/osl/internal/live/ls"
	"gitlab.sskjz.com/overseas/live/osl/internal/live/lsr"
	"gitlab.sskjz.com/overseas/live/osl/internal/live/mock"
	liveNotice "gitlab.sskjz.com/overseas/live/osl/internal/live/notice"
	"gitlab.sskjz.com/overseas/live/osl/internal/live/rs"
	"gitlab.sskjz.com/overseas/live/osl/internal/live/rtc"
	"gitlab.sskjz.com/overseas/live/osl/internal/live/warning"
	loginGrant "gitlab.sskjz.com/overseas/live/osl/internal/login/grant"
	loginPhone "gitlab.sskjz.com/overseas/live/osl/internal/login/phone"
	"gitlab.sskjz.com/overseas/live/osl/internal/manage/data"
	"gitlab.sskjz.com/overseas/live/osl/internal/manage/data/dob"
	dobCharging "gitlab.sskjz.com/overseas/live/osl/internal/manage/data/dob/charging"
	dobGifting "gitlab.sskjz.com/overseas/live/osl/internal/manage/data/dob/gifting"
	"gitlab.sskjz.com/overseas/live/osl/internal/manage/patrol"
	"gitlab.sskjz.com/overseas/live/osl/internal/marketing"
	"gitlab.sskjz.com/overseas/live/osl/internal/moment"
	"gitlab.sskjz.com/overseas/live/osl/internal/mute"
	"gitlab.sskjz.com/overseas/live/osl/internal/open"
	pp "gitlab.sskjz.com/overseas/live/osl/internal/patrol"
	"gitlab.sskjz.com/overseas/live/osl/internal/pay"
	"gitlab.sskjz.com/overseas/live/osl/internal/pay/sb"
	"gitlab.sskjz.com/overseas/live/osl/internal/pay/usolve"
	payWatch "gitlab.sskjz.com/overseas/live/osl/internal/pay/watch"
	"gitlab.sskjz.com/overseas/live/osl/internal/payc"
	"gitlab.sskjz.com/overseas/live/osl/internal/payermax"
	"gitlab.sskjz.com/overseas/live/osl/internal/paypal"
	"gitlab.sskjz.com/overseas/live/osl/internal/pk"
	"gitlab.sskjz.com/overseas/live/osl/internal/popup"
	"gitlab.sskjz.com/overseas/live/osl/internal/profitsharing"
	"gitlab.sskjz.com/overseas/live/osl/internal/profitsharing/cycle"
	"gitlab.sskjz.com/overseas/live/osl/internal/props"
	"gitlab.sskjz.com/overseas/live/osl/internal/props/pgift"
	"gitlab.sskjz.com/overseas/live/osl/internal/push"
	pushRE "gitlab.sskjz.com/overseas/live/osl/internal/push/pre"
	"gitlab.sskjz.com/overseas/live/osl/internal/ranklist"
	"gitlab.sskjz.com/overseas/live/osl/internal/recommender"
	"gitlab.sskjz.com/overseas/live/osl/internal/recommender/recmixer"
	"gitlab.sskjz.com/overseas/live/osl/internal/report"
	"gitlab.sskjz.com/overseas/live/osl/internal/review"
	"gitlab.sskjz.com/overseas/live/osl/internal/room"
	"gitlab.sskjz.com/overseas/live/osl/internal/room/interact"
	"gitlab.sskjz.com/overseas/live/osl/internal/room/interact/draw2"
	"gitlab.sskjz.com/overseas/live/osl/internal/room/luckyroom"
	"gitlab.sskjz.com/overseas/live/osl/internal/room/online"
	"gitlab.sskjz.com/overseas/live/osl/internal/room/rsd"
	"gitlab.sskjz.com/overseas/live/osl/internal/room/view"
	"gitlab.sskjz.com/overseas/live/osl/internal/salary"
	"gitlab.sskjz.com/overseas/live/osl/internal/search"
	"gitlab.sskjz.com/overseas/live/osl/internal/seller"
	"gitlab.sskjz.com/overseas/live/osl/internal/shortener"
	"gitlab.sskjz.com/overseas/live/osl/internal/smsb"
	"gitlab.sskjz.com/overseas/live/osl/internal/sticker"
	"gitlab.sskjz.com/overseas/live/osl/internal/task"
	"gitlab.sskjz.com/overseas/live/osl/internal/ufind"
	"gitlab.sskjz.com/overseas/live/osl/internal/ul"
	"gitlab.sskjz.com/overseas/live/osl/internal/urm"
	userAcl "gitlab.sskjz.com/overseas/live/osl/internal/user/acl"
	userAct "gitlab.sskjz.com/overseas/live/osl/internal/user/act"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/avatar"
	userDevice "gitlab.sskjz.com/overseas/live/osl/internal/user/device"
	userDress "gitlab.sskjz.com/overseas/live/osl/internal/user/dress"
	userLevel "gitlab.sskjz.com/overseas/live/osl/internal/user/level"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/nickname"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/privacy"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/uctz"
	"gitlab.sskjz.com/overseas/live/osl/internal/withdraw"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/dun"
	"gitlab.sskjz.com/overseas/live/osl/pkg/fcm"
	"gitlab.sskjz.com/overseas/live/osl/pkg/geoip"
	"gitlab.sskjz.com/overseas/live/osl/pkg/gid"
	"gitlab.sskjz.com/overseas/live/osl/pkg/http"
	httpAuth "gitlab.sskjz.com/overseas/live/osl/pkg/http/auth"
	httpCache "gitlab.sskjz.com/overseas/live/osl/pkg/http/cache"
	httpMux "gitlab.sskjz.com/overseas/live/osl/pkg/http/mux"
	ratelimit "gitlab.sskjz.com/overseas/live/osl/pkg/http/rlimit"
	"gitlab.sskjz.com/overseas/live/osl/pkg/i18n"
	"gitlab.sskjz.com/overseas/live/osl/pkg/logic"
	"gitlab.sskjz.com/overseas/live/osl/pkg/region"
	"gitlab.sskjz.com/overseas/live/osl/pkg/sto"
	"gitlab.sskjz.com/overseas/live/osl/pkg/sts"
	"gitlab.sskjz.com/overseas/live/osl/pkg/translate"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ulink"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ulink/facebook"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ulink/google"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ulink/twitter"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"gitlab.sskjz.com/overseas/live/osl/pkg/volc/mt"
	"gitlab.sskjz.com/overseas/live/osl/sys"
	"gitlab.sskjz.com/overseas/live/osl/sys/cc"
	"gitlab.sskjz.com/overseas/live/osl/sys/co"
	"gitlab.sskjz.com/overseas/live/osl/sys/cron"
	"gitlab.sskjz.com/overseas/live/osl/sys/dq"
	"gitlab.sskjz.com/overseas/live/osl/sys/es"
	"gitlab.sskjz.com/overseas/live/osl/sys/ev"
	"gitlab.sskjz.com/overseas/live/osl/sys/gdk"
	"gitlab.sskjz.com/overseas/live/osl/sys/log"
	"gitlab.sskjz.com/overseas/live/osl/sys/mq"
	"gitlab.sskjz.com/overseas/live/osl/sys/ob"
	"gitlab.sskjz.com/overseas/live/osl/sys/redi"
	"gitlab.sskjz.com/overseas/live/osl/sys/rpc"
	"gitlab.sskjz.com/overseas/live/osl/sys/unq"
	"gitlab.sskjz.com/overseas/live/osl/sys/up"
	"go.uber.org/fx"
)

var Module = fx.Options(
	fx.Provide(
		up.Provide,
		cc.Provide,
		mq.Provide,
		dq.Provide,
		ev.Provide,
		NewServer,
		NewRouter,
		log.Provide,
		unq.Provide,
		cron.Provide,
		db.ProvideMongo,
		db.ProvideGORM,
		redi.Provide,
		rpc.Provide,
		httpAuth.Provide,
		httpCache.Provide,
		httpMux.Provide,
		ratelimit.Provide,
		http.Provide,
		gid.Provide,
		geoip.Provide,
		logic.Provide,
		gdk.ProvideClient,
		room.Provide,
		live.Provide,
		user.Provide,
		ulink.Provide,
		userAct.Provide,
		userLevel.Provide,
		userDevice.Provide,
		userDress.Provide,
		fund.Provide,
		journal.Provide,
		fundOrder.Provide,
		trade.Provide,
		follow.Provide,
		fclub.Provide, fctask.Provide,
		gift.Provide,
		props.Provide,
		online.Provide,
		interact.Provide,
		sb.Provide,
		pay.Provide,
		payc.Provide,
		payermax.Provide, ganopay.Provide, binance.Provide, paypal.Provide, usolve.Provide,
		withdraw.Provide,
		ranklist.Provide,
		moment.Provide,
		recommender.Provide,
		recmixer.Provide,
		banner.Provide,
		history.Provide,
		rsd.Provide,
		draw2.Provide,
		feed.Provide,
		es.Provide,
		sto.Provide,
		sts.Provide,
		smsb.Provide,
		ufind.Provide,
		search.Provide,
		pk.Provide,
		contrib.Provide,
		avatar.Provide,
		privacy.Provide,
		loginGrant.Provide,
		loginPhone.Provide,
		ls.Provide,
		lsr.Provide,
		rtc.Provide,
		im.Provide,
		imBan.Provide,
		in.Provide,
		asset.Provide,
		region.Provide,
		upload.Provide,
		nickname.Provide,
		payermaxAPI.Provide,
		rechargeAPI.Provide,
		journalAPI.Provide,
		task.Provide,
		salary.Provide,
		urm.Provide,
		mixer.Provide,
		mt.Provide,
		translate.Provide,
		dun.Provide,
		face.Provide,
		shortener.Provide,
		agency.Provide,
		open.Provide,
		anchor.Provide,
		agency_invite.Provide,
		agency_newbie.Provide,
		rs.Provide,
		fcm.Provide,
		push.Provide, pushRE.Provide,
		imPush.Provide,
		report.Provide,
		bs.Provide, bs2.Provide, ss.Provide,
		game.Provide, game.ProvideStore, game.ProvideNotifier,
		review.Provide,
		launch.Provide,
		activity_20240819.Provide,
		client.Provide, clogs.Provide,
		patrol.Provide,
		ul.Provide,
		warning.Provide,
		userAcl.Provide,
		link.Provide,
		adm.Provide,
		seller.Provide,
		limiter.Provide,
		mute.Provide,
		luckyroom.Provide,
		lucky_star.Provide,
		valentines_day.Provide,
		rebate.Provide,
		payment_vehicle.Provide,
		blindBoxActivity.Provide,
		coin_grab.Provide,
		foolsday.Provide,
		hourlyrank.Provide,
		blindbox_collect.Provide,
		super_lucky_star.Provide,
		easter.Provide,
		widget.Provide,
		jing.Provide,
		data.Provide,
		view.Provide,
		halloween.Provide,
		adjust.Provide,
		christmas.Provide,
		rocket.Provide, blindbox.Provide,
		laborious.Provide,
		redpacket.Provide,
		sredpacket.Provide,
		sticker.Provide,
		popup.Provide,
		profitsharing.Provide,
		pp.Provide,
		rlaa.Provide,
	),
	fx.Invoke(
		sys.Initialize,
		ob.Invoke,
		co.Invoke,
		db.ReleaseGORM,
		db.ReleaseMongo,
		gdk.InvokeClient,
		mq.Invoke,
		dq.Invoke,
		i18n.Invoke,
		swagUI.Setup,
		// biz
		evt.Invoke,
		mock.Invoke,
		ranklist.Invoke,
		ranklist.InvokeEvb,
		ulink.Invoke,
		ufind.Invoke,
		avatar.Invoke,
		privacy.Invoke,
		follow.Invoke,
		recommender.Invoke,
		rsd.Invoke,
		link.RegisterPKChecker,
		pk.InvokeRoomStatus, pk.InvokeEvt,
		contrib.Invoke,
		ls.Invoke,
		ls.InvokeQueue,
		google.Invoke, facebook.Invoke, twitter.Invoke,
		journal.Invoke,
		payermax.Invoke, ganopay.Invoke, binance.Invoke, paypal.Invoke, usolve.Invoke,
		withdraw.Invoke,
		payc.Invoke, payWatch.Invoke,
		im.Invoke,
		in.Invoke,
		nickname.Invoke,
		userAcl.Invoke, userDevice.Invoke,
		uctz.Invoke,
		imPush.Invoke,
		anchor.Invoke,
		anchor.InvokeInAPI,
		warning.Invoke,
		agency_invite.Invoke,
		room.InvokeApiEvent,
		moment.Invoke,
		link.InvokeApiEvent,
		adm.Invoke, adm.InvokeQueueInAPI,
		review.InvokeInAPI,
		liveNotice.Invoke,
		luckyroom.Invoke,
		luckyroom.InvokeApiEvent,
		lucky_star.Invoke,
		//valentines_day.Invoke,
		//agency_newbie.Invoke,
		rebate.Invoke,
		payment_vehicle.Invoke,
		blindBoxActivity.Invoke,
		coin_grab.Invoke,
		foolsday.Invoke,
		hourlyrank.Invoke,
		blindbox_collect.Invoke,
		super_lucky_star.Invoke,
		easter.Invoke,
		salary.InvokeInAPI,
		dob.Invoke, dobCharging.Invoke, dobGifting.Invoke, marketing.Invoke,
		halloween.Invoke,
		christmas.InvokeInAPI,
		pushRE.InvokeInAPI,
		pgift.Invoke, // props autoloader
		laborious.InvokeInAPI,
		gifting.Invoke,
		cycle.Invoke,
		fctask.InvokeInAPI,
		game.InvokeEvt,
		// api
		linkAPI.Invoke,
		patrol.Invoke,
		handler.InvokeDebugHandler,
		mockAPI.Login, mockAPI.Pay,
		admin2.Invoke,
		upload.API,
		login.JWT, grantLogin.API, phoneLogin.API, appleLogin.API,
		connect.API, refer.API,
		userCard.API,
		profile.API,
		followAPI.Invoke,
		walletAPI.Invoke,
		exchangeAPI.Invoke,
		journalAPI.Invoke,
		withdrawAPI.Invoke,
		location.API,
		handler.InvokeFeedHandler,
		handler.InvokeLiveHandler,
		handler.InvokeAnchorHandler,
		handler.InvokeGiftHandler,
		propsAPI.Invoke,
		interactionAPI.Invoke,
		interactionRocketAPI.Invoke, interactionBlindBoxAPI.Invoke,
		rocket.Invoke,
		handler.InvokeChatHandler,
		handler.InvokeWishHandler,
		handler.InvokeAssetHandler,
		rechargeAPI.Invoke,
		payermaxAPI.Invoke,
		appstoreAPI.Invoke, playstoreAPI.Invoke,
		handler.InvokeRanklistHandler,
		handler.InvokeHistoryHandler,
		handler.InvokeSearchHandler,
		handler.InvokeImHandler,
		handler.InvokeInHandler,
		handler.InvokeEntranceHandler,
		handler.InvokeMomentHandler,
		handler.InvokeFaceHandler,
		handler.InvokeAgencyHandler,
		handler.InvokeAgencyDataHandler,
		handler.InvokeGameHandler,
		handler.InvokeOpenHandler,
		handler.InvokeReportHandler,
		handler.InvokeAdmCenterHandler,
		handler.InvokePopupHandler,
		ipk.Invoke,
		public.API,
		clientAPI.API,
		translateAPI.Invoke,
		bs.Register, bs.Invoke,
		bs2.Register, bs2.Invoke,
		jing.Register,
		ss.Register, ss.Invoke,
		gameAPI.Invoke,
		security.API,
		activity.Invoke, activity_20240819.Invoke,
		luckyroomAPI.Invoke,
		certAPI.Invoke,
		sellerAPI.Invoke, sellerAPI.Invoke2,
		adjust.Invoke, adjust.InvokeAPI,
		sflytext.Invoke,
		sredpacket.Invoke,
		stickerAPI.Invoke,
		fansclub.Invoke,
		rlaa.Invoke,
		giftStickerAPI.Invoke,
		// sys
		cron.Invoke,
		up.Invoke,
	),
)
