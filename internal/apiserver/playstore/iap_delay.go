package playstore

import (
	"context"
	"crypto/md5"
	"encoding/hex"
	"errors"
	"fmt"
	"time"

	"github.com/go-redis/redis/v8"
	"go.uber.org/zap"
)

const (
	keyDelaying = "PLAYSTORE:DELAYING:%s" // md5(receipt)
	keyResolved = "PLAYSTORE:RESOLVED:%s" // md5(receipt)
	ttlDelaying = 24 * time.Hour
	ttlResolved = 24 * time.Hour
)

func keyReceipt(in string) string {
	h := md5.Sum([]byte(in))
	return hex.EncodeToString(h[:])
}

func (s *IAP) markDelaying(ctx context.Context, userId, packageName, productId, receipt string) {
	if err := s.rc.Set(ctx, fmt.Sprintf(keyDelaying, keyReceipt(receipt)), userId, ttlDelaying).Err(); err != nil {
		s.log.Warn("mark pending failed", zap.String("userId", userId), zap.Error(err))
	}
	s.delayChk(ctx, userId, packageName, productId, receipt)
}

func (s *IAP) delaying(ctx context.Context, receipt string) (string, error) {
	v, err := s.rc.Get(ctx, fmt.Sprintf(keyDelaying, keyReceipt(receipt))).Result()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			return "", nil
		}
		return "", err
	}
	return v, nil
}

func (s *IAP) markResolved(ctx context.Context, packageName, productId, receipt, tradeNo string) {
	if err := s.rc.Del(ctx, fmt.Sprintf(keyDelaying, keyReceipt(receipt))).Err(); err != nil {
		s.log.Warn("unmark pending failed", zap.String("tradeNo", tradeNo), zap.Error(err))
	}
	if err := s.rc.Set(ctx, fmt.Sprintf(keyResolved, keyReceipt(receipt)), tradeNo, ttlResolved).Err(); err != nil {
		s.log.Warn("mark resolved failed", zap.String("tradeNo", tradeNo), zap.Error(err))
	}
	s.delayAck(ctx, packageName, productId, receipt)
}

func (s *IAP) resolved(ctx context.Context, receipt string) (string, error) {
	v, err := s.rc.Get(ctx, fmt.Sprintf(keyResolved, keyReceipt(receipt))).Result()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			return "", nil
		}
		return "", err
	}
	return v, nil
}
