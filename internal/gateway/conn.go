package gateway

import (
	"time"

	"gitlab.sskjz.com/go/ob/meter"
	"gitlab.sskjz.com/go/ws"
	"gitlab.sskjz.com/overseas/live/osl/pkg/dbg"
	"gitlab.sskjz.com/overseas/live/osl/pkg/protocol"
	"go.uber.org/atomic"
	"go.uber.org/zap"
)

const (
	maxOutboundBuffer = 4 * 1024 * 1024
)

func newConn(raw ws.Conn, log *zap.Logger) *Conn {
	return &Conn{
		Conn: raw,
		log:  log,
		seq:  atomic.NewUint32(1e6),
		// metrics
		ackSend: meter.NewCounter[uint]("osl_gateway_ack_send", meter.Singleton()),
		ackRecv: meter.NewCounter[uint]("osl_gateway_ack_recv", meter.Singleton()),
	}
}

type Conn struct {
	ws.Conn
	log *zap.Logger
	seq *atomic.Uint32
	// metrics
	ackSend meter.Counter[uint]
	ackRecv meter.Counter[uint]
}

func (c *Conn) Send(data []byte) error {
	if len(data) > protocol.MinPacketLength {
		if protocol.ReadSeq(data) == 0 {
			protocol.WriteSeq(data, c.seq.Inc())
		}
		if protocol.ReadFlag(data)&protocol.FlagAck != 0 {
			c.ackSend.Add(1)
		}
		if dbg.Ing() {
			p, _ := protocol.DecodePacket(data)
			c.log.Debug("do send",
				zap.Any("tags", c.Tags()),
				zap.Uint32("msgType", p.MsgType),
				zap.Uint32("seq", p.Sequence),
				zap.String("payload", string(p.Payload)),
			)
		}
	}
	return c.Conn.Send(data)
}

func (c *Conn) Recv(data []byte) error {
	if len(data) >= protocol.MinPacketLength {
		if protocol.ReadFlag(data)&protocol.FlagAck != 0 {
			c.ackRecv.Add(1)
			return ws.ErrSkipMessage
		}
		if dbg.Ing() {
			p, _ := protocol.DecodePacket(data)
			c.log.Debug("do recv",
				zap.Any("tags", c.Tags()),
				zap.Uint32("msgType", p.MsgType),
				zap.Uint32("seq", p.Sequence),
				zap.String("payload", string(p.Payload)),
			)
		}
	}
	return nil
}

func (c *Conn) OnTick() time.Duration {
	if c.OutboundBuffered() > maxOutboundBuffer {
		_ = c.Close(ws.NewCloseFrame(1006, "slow connection"))
		c.log.Info("outbound buffer exceeded")
		return time.Minute
	}
	return time.Second
}
