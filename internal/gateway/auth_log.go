package gateway

import (
	"net/http"

	"gitlab.sskjz.com/go/ws"
	"go.uber.org/zap"
)

func authLogging(log *zap.Logger, auth ws.Authorizer) ws.Authorizer {
	return func(conn ws.Conn, host string, uri string, header http.Header) error {
		logger := log.With(zap.String("host", host), zap.String("uri", uri), zap.Any("header", header),
			zap.String("connId", conn.Id()), zap.String("clientIP", conn.ClientIP()),
		)
		err := auth(conn, host, uri, header)
		if err != nil {
			logger.Info("auth failed", zap.Error(err))
		} else {
			tags := conn.Tags()
			logger.Debug("auth success", zap.String("userId", tags["userId"]), zap.Any("tags", tags))
		}
		return err
	}
}
