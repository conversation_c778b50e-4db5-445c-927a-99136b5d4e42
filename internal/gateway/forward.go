package gateway

import (
	"context"

	"gitlab.sskjz.com/go/gdk"
	"gitlab.sskjz.com/go/ob/meter"
	"gitlab.sskjz.com/go/ws"
	"go.uber.org/zap"
)

func newForward(px gdk.Proxy, log *zap.Logger) *Forward {
	return &Forward{
		px:  px,
		log: log,
		// metrics
		req: meter.NewCounter[int]("osl_gateway_forward_request"),
	}
}

type Forward struct {
	px  gdk.Proxy
	log *zap.Logger
	// metrics
	req meter.Counter[int]
}

func (f *Forward) Wrap(conn ws.Conn) *Conn {
	return newConn(conn, f.log.With(zap.String("connId", conn.Id())))
}

func (f *Forward) OnMessage(conn *Conn, bytes []byte) {
	f.req.Add(1, "via", "message")
	if err := f.px.OnMessage(context.Background(), conn.Id(), gdk.KVPair(conn.Tags()), gdk.KVPair(conn.Meta()), bytes); err != nil {
		f.log.Warn("proxy onMessage failed", zap.Error(err))
	}
}

func (f *Forward) OnOpen(conn *Conn) {
	f.req.Add(1, "via", "connected")
	if err := f.px.OnConnected(context.Background(), conn.Id(), gdk.KVPair(conn.Tags()), gdk.KVPair(conn.Meta())); err != nil {
		f.log.Warn("proxy onConnected failed", zap.Error(err))
	}
}

func (f *Forward) OnClose(conn *Conn, err error) {
	f.req.Add(1, "via", "disconnect")
	if err := f.px.OnDisconnect(context.Background(), conn.Id(), gdk.KVPair(conn.Tags()), gdk.KVPair(conn.Meta())); err != nil {
		f.log.Warn("proxy onDisconnect failed", zap.Error(err))
	}
}
