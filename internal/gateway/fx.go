package gateway

import (
	"context"
	"time"

	"github.com/samber/lo"
	"gitlab.sskjz.com/go/gdk"
	"gitlab.sskjz.com/go/log"
	"gitlab.sskjz.com/go/ob/meter"
	"gitlab.sskjz.com/go/up"
	"gitlab.sskjz.com/go/ws"
	"gitlab.sskjz.com/overseas/live/osl/pkg/conf"
	"go.uber.org/fx"
)

func Provide(desc *conf.Setting, vnd log.Vendor) (*ws.Hub, gdk.Gateway) {
	hub := ws.New(
		ws.WithLogger(vnd.Scope("ws.hub")),
		ws.WithReadTimeout(time.Minute),
		ws.WithAuthorizer(lo.TernaryF(desc.Gateway.Public,
			func() ws.Authorizer {
				return authLogging(vnd.Scope("ws.pub"), newGuestAuth().Check)
			},
			func() ws.Authorizer {
				return authLogging(vnd.Scope("ws.auth"), newTokenAuth(newJWT(desc.Auth.JWTKey)).Check)
			},
		)),
		ws.WithHealthCheck("/health/ping"),
		ws.WithCompression(),
	)
	return hub, newRouter(hub, vnd.Scope("router"))
}

func Invoke(desc *conf.Setting, lc fx.Lifecycle, dm *up.Daemon, proxy gdk.Proxy, hub *ws.Hub, vnd log.Vendor) {
	var fwd ws.Listener[*Conn] = newForward(proxy, vnd.Scope("forward"))
	if desc.Gateway.Public {
		fwd = newGuestFwd(fwd.(*Forward))
	}
	hub.Apply(ws.WithHandler[*Conn](fwd))

	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			return hub.Serve("tcp://" + desc.Gateway.HTTP)
		},
		OnStop: dm.OnStop(func(ctx context.Context) error {
			return hub.Close(ctx)
		}, up.StageDraining),
	})

	meter.Visitor(func(ctx context.Context) {
		meter.NewGauge[int]("osl_gateway_conn_activated", meter.WithContext(ctx)).Set(hub.Connections())
	})
}
