package gateway

import (
	"context"
	"errors"

	"gitlab.sskjz.com/go/gdk"
	"gitlab.sskjz.com/go/gdk/bcf"
	"gitlab.sskjz.com/go/gdk/bus"
	"gitlab.sskjz.com/go/ob/meter"
	"gitlab.sskjz.com/go/ws"
	"go.uber.org/zap"

	"gitlab.sskjz.com/overseas/live/osl/pkg/gproto"
)

var (
	ErrConnectionLost = errors.New("connection lost")
)

func newRouter(hub *ws.Hub, log *zap.Logger) *Router {
	return &Router{
		ws:  hub,
		log: log,
		// metrics
		req: meter.NewCounter[int]("osl_gateway_route_request"),
	}
}

type Router struct {
	ws  *ws.Hub
	log *zap.Logger
	// metrics
	req meter.Counter[int]
}

func (r *Router) Unicast(ctx context.Context, peer *bus.Peer, payload []byte) error {
	c, has := r.ws.GetConn(peer.ConnId)
	if !has {
		return ErrConnectionLost
	}

	pkt, err := gproto.Decode(payload)
	if err != nil {
		return err
	}

	if skipped(pkt, c) {
		return nil
	}

	err = c.Send(pkt.Payload)
	if err != nil {
		r.log.Warn("unicast failed", zap.String("gwId", peer.GwId), zap.String("connId", peer.ConnId), zap.Error(err))
		return err
	}

	r.req.Add(1, "via", "unicast")
	return nil
}

func (r *Router) Broadcast(ctx context.Context, to bus.Target, payload []byte) error {
	pkt, err := gproto.Decode(payload)
	if err != nil {
		return err
	}

	f := bcf.With(to)

	if tag := f.Direct(); tag != nil {
		if err := r.ws.BcastTag(tag.Key, tag.Val, pkt.Payload, ws.BFilter(func(c ws.Conn) bool {
			return !skipped(pkt, c)
		})); err != nil {
			r.log.Warn("broadcast failed", zap.String("tagKey", tag.Key), zap.String("tagVal", tag.Val), zap.Error(err))
			return err
		}
		r.req.Add(1, "via", "multicast")
		return nil
	}

	if f.Global() {
		if err := r.ws.BcastAll(pkt.Payload, ws.BFilter(func(c ws.Conn) bool {
			return !skipped(pkt, c)
		})); err != nil {
			r.log.Warn("broadcast global failed", zap.Error(err))
			return err
		}
		r.req.Add(1, "via", "broadcast")
		return nil
	}

	return errors.New("broadcast filter not support")
}

func (r *Router) SetOption(ctx context.Context, peer *bus.Peer, tags gdk.KVPair, meta gdk.KVPair) error {
	c, has := r.ws.GetConn(peer.ConnId)
	if !has {
		return ErrConnectionLost
	}

	if len(tags) > 0 {
		c.SetTags(ws.Hmap(tags))
	}

	if len(meta) > 0 {
		c.SetMeta(ws.Hmap(meta))
	}

	r.req.Add(1, "via", "setOption")
	return nil
}

func (r *Router) CloseConn(ctx context.Context, peer *bus.Peer, code int, msg string) error {
	c, has := r.ws.GetConn(peer.ConnId)
	if !has {
		return ErrConnectionLost
	}

	if err := c.Close(ws.NewCloseFrame(code, msg)); err != nil {
		r.log.Warn("close conn failed", zap.String("gwId", peer.GwId), zap.String("connId", peer.ConnId), zap.Error(err))
		return err
	}

	r.req.Add(1, "via", "closeConn")
	return nil
}
