package gateway

import (
	"errors"

	"github.com/golang-jwt/jwt/v4"
)

var (
	ErrWithoutUserId    = errors.New("missing user id")
	ErrInvalidPubKey    = errors.New("public key invalid")
	ErrInvalidAlgorithm = errors.New("invalid signing algorithm")
)

func newJWT(key string) *JWT {
	return &JWT{alg: "HS256", key: []byte(key), uid: "uid"}
}

type JWT struct {
	alg string
	key []byte
	uid string
}

func (j *JWT) Parse(token string) (string, error) {
	tt, err := jwt.Parse(token, func(t *jwt.Token) (any, error) {
		if jwt.GetSigningMethod(j.alg) != t.Method {
			return nil, ErrInvalidAlgorithm
		}
		return j.key, nil
	})
	if err != nil {
		return "", err
	}

	claims := tt.Claims.(jwt.MapClaims)

	val, has := claims[j.uid]
	if !has {
		return "", ErrWithoutUserId
	}

	if uid, is := val.(string); !is || uid == "" {
		return "", ErrWithoutUserId
	} else {
		return uid, nil
	}
}
