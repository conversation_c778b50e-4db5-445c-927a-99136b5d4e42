package main

import (
	"flag"

	"gitlab.sskjz.com/overseas/live/osl/internal/adm"
	"gitlab.sskjz.com/overseas/live/osl/internal/agency"
	"gitlab.sskjz.com/overseas/live/osl/internal/anchor"
	"gitlab.sskjz.com/overseas/live/osl/internal/fclub"
	"gitlab.sskjz.com/overseas/live/osl/internal/fclub/fctask"
	"gitlab.sskjz.com/overseas/live/osl/internal/follow"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund/journal"
	"gitlab.sskjz.com/overseas/live/osl/internal/fund/order"
	"gitlab.sskjz.com/overseas/live/osl/internal/gift"
	"gitlab.sskjz.com/overseas/live/osl/internal/im"
	"gitlab.sskjz.com/overseas/live/osl/internal/interaction/rocket"
	"gitlab.sskjz.com/overseas/live/osl/internal/link"
	"gitlab.sskjz.com/overseas/live/osl/internal/live"
	"gitlab.sskjz.com/overseas/live/osl/internal/live/ls"
	"gitlab.sskjz.com/overseas/live/osl/internal/live/lsr"
	"gitlab.sskjz.com/overseas/live/osl/internal/live/rtc"
	"gitlab.sskjz.com/overseas/live/osl/internal/manage/patrol"
	"gitlab.sskjz.com/overseas/live/osl/internal/pk"
	"gitlab.sskjz.com/overseas/live/osl/internal/profitsharing"
	"gitlab.sskjz.com/overseas/live/osl/internal/profitsharing/cycle"
	"gitlab.sskjz.com/overseas/live/osl/internal/room"
	"gitlab.sskjz.com/overseas/live/osl/internal/room/interact"
	"gitlab.sskjz.com/overseas/live/osl/internal/room/online"
	"gitlab.sskjz.com/overseas/live/osl/internal/room/rpc"
	"gitlab.sskjz.com/overseas/live/osl/internal/room/rsd"
	"gitlab.sskjz.com/overseas/live/osl/internal/room/view"
	"gitlab.sskjz.com/overseas/live/osl/internal/seller"
	"gitlab.sskjz.com/overseas/live/osl/internal/ul"
	"gitlab.sskjz.com/overseas/live/osl/internal/urm"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/avatar"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/dress"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/level"
	"gitlab.sskjz.com/overseas/live/osl/internal/user/privacy"
	"gitlab.sskjz.com/overseas/live/osl/pkg/conf"
	"gitlab.sskjz.com/overseas/live/osl/pkg/db"
	"gitlab.sskjz.com/overseas/live/osl/pkg/env"
	"gitlab.sskjz.com/overseas/live/osl/pkg/i18n"
	"gitlab.sskjz.com/overseas/live/osl/pkg/logic"
	"gitlab.sskjz.com/overseas/live/osl/pkg/sto"
	"gitlab.sskjz.com/overseas/live/osl/pkg/ulink"
	"gitlab.sskjz.com/overseas/live/osl/pkg/user"
	"gitlab.sskjz.com/overseas/live/osl/sys"
	"gitlab.sskjz.com/overseas/live/osl/sys/cc"
	"gitlab.sskjz.com/overseas/live/osl/sys/co"
	"gitlab.sskjz.com/overseas/live/osl/sys/cron"
	"gitlab.sskjz.com/overseas/live/osl/sys/dq"
	"gitlab.sskjz.com/overseas/live/osl/sys/ev"
	"gitlab.sskjz.com/overseas/live/osl/sys/gdk"
	"gitlab.sskjz.com/overseas/live/osl/sys/log"
	"gitlab.sskjz.com/overseas/live/osl/sys/mq"
	"gitlab.sskjz.com/overseas/live/osl/sys/ob"
	"gitlab.sskjz.com/overseas/live/osl/sys/redi"
	"gitlab.sskjz.com/overseas/live/osl/sys/unq"
	"gitlab.sskjz.com/overseas/live/osl/sys/up"
	"go.uber.org/fx"
)

func main() {
	var config string
	flag.StringVar(&config, "c", "", "-c config.yaml")
	flag.Parse()

	env.App = "osl-roomsrv"

	app := fx.New(
		fx.Provide(
			conf.Provide(config),
			up.Provide,
			unq.Provide,
			cc.Provide,
			ev.Provide,
			log.Provide,
			cron.Provide,
			gdk.ProvideClient,
			gdk.ProvideWorker,
			db.ProvideGORM,
			db.ProvideMongo,
			mq.Provide,
			dq.Provide,
			redi.Provide,
			user.Provide,
			level.Provide,
			dress.Provide,
			fclub.Provide, fctask.Provide,
			logic.Provide,
			room.Provide,
			online.Provide,
			gift.Provide,
			live.Provide,
			rsd.Provide,
			rtc.Provide,
			pk.Provide,
			follow.Provide,
			avatar.Provide,
			privacy.Provide,
			sto.Provide,
			agency.Provide,
			urm.Provide,
			im.Provide,
			ul.Provide,
			ulink.Provide,
			patrol.Provide,
			link.Provide,
			rpc.Provide,
			view.Provide,
			rocket.Provide,
			fund.Provide,    // dep by seller
			journal.Provide, // dep by fclub
			seller.Provide,
			profitsharing.Provide,
			adm.Provide,
			ls.Provide,
			anchor.Provide,
			lsr.Provide,
			order.Provide,
		),
		fx.Invoke(
			sys.Initialize,
			ob.Invoke,
			co.Invoke,
			db.ReleaseGORM,
			db.ReleaseMongo,
			gdk.InvokeClient,
			mq.Invoke,
			dq.Invoke,
			i18n.Invoke,
			// biz
			logic.Invoke,
			avatar.Invoke,
			privacy.Invoke,
			room.InvokeEvt,
			room.InvokeRoomProcessor,
			rpc.Invoke,
			online.Invoke,
			link.InvokeRoomStatus, link.RegisterPKChecker,
			pk.InvokeRoomStatus,
			interact.Invoke,
			room.InvokeLuckMsgDispatcher,
			cycle.Invoke,
			fctask.InvokeInRoom,
			// sys
			gdk.InvokeWorker,
			cron.Invoke,
			up.Invoke,
		),
	)

	app.Run()
}
